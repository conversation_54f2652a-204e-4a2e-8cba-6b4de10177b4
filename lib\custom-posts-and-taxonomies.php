<?php
/**
 * All custom post and taxonomy registerations.
 *
 * @package twkmedia
 */

// Custom post types goes here.

// Register Custom Post Type Policy
function create_policy_cpt()
{

	$labels = array(
		'name' => _x('Policy', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Policy', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Policies', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Policy', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Policy Archives', 'textdomain'),
		'attributes' => __('Policy Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Policy:', 'textdomain'),
		'all_items' => __('All Policies', 'textdomain'),
		'add_new_item' => __('Add New Policy', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Policy', 'textdomain'),
		'edit_item' => __('Edit Policy', 'textdomain'),
		'update_item' => __('Update Policy', 'textdomain'),
		'view_item' => __('View Policy', 'textdomain'),
		'view_items' => __('View Policy', 'textdomain'),
		'search_items' => __('Search Policy', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Policy', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Policy', 'textdomain'),
		'items_list' => __('Policy list', 'textdomain'),
		'items_list_navigation' => __('Policy list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Policy list', 'textdomain'),
	);
	$icon_policies = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-policies-white.svg');
	$args = array(
		'label' => __('Policy', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_policies),
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'taxonomies'  => array( '' ),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => false,
		'show_in_rest' => true,
		'publicly_queryable' => false,
		'capability_type' => 'post',
		

	);
	register_post_type('policy', $args);
}
add_action('init', 'create_policy_cpt', 0);


// Register Custom Post Type Vacancies
function create_vacancy_cpt()
{

	$labels = array(
		'name' => _x('Vacancies', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Vacancy', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Vacancies', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Vacancy', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Vacancy Archives', 'textdomain'),
		'attributes' => __('Vacancy Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Vacancy:', 'textdomain'),
		'all_items' => __('All Vacancies', 'textdomain'),
		'add_new_item' => __('Add New Vacancy', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Vacancy', 'textdomain'),
		'edit_item' => __('Edit Vacancy', 'textdomain'),
		'update_item' => __('Update Vacancy', 'textdomain'),
		'view_item' => __('View Vacancy', 'textdomain'),
		'view_items' => __('View Vacancy', 'textdomain'),
		'search_items' => __('Search Vacancy', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Vacancy', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Vacancy', 'textdomain'),
		'items_list' => __('Vacancy list', 'textdomain'),
		'items_list_navigation' => __('Vacancy list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Vacancy list', 'textdomain'),
	);
	$icon_vacancy = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-cranleigh-vacancies-white.svg');

	$args = array(
		'label' => __('Vacancies', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_vacancy),
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'taxonomies'  => array( '' ),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => false,
		'show_in_rest' => true,
		'publicly_queryable' => true,
		'capability_type' => 'post',
	);
	register_post_type('vacancy', $args);
}
add_action('init', 'create_vacancy_cpt', 0);


// Register Taxonomy Vacancy Type
function create_vacancy_type_tax()
{
	$labels = array(
		'name'              => _x('Vacancy Types', 'taxonomy general name', 'textdomain'),
		'singular_name'     => _x('Vacancy Type', 'taxonomy singular name', 'textdomain'),
		'search_items'      => __('Search Vacancy Types', 'textdomain'),
		'all_items'         => __('All Vacancy Types', 'textdomain'),
		'parent_item'       => __('Parent Vacancy Type', 'textdomain'),
		'parent_item_colon' => __('Parent Vacancy Type:', 'textdomain'),
		'edit_item'         => __('Edit Vacancy Type', 'textdomain'),
		'update_item'       => __('Update Vacancy Type', 'textdomain'),
		'add_new_item'      => __('Add New Vacancy Type', 'textdomain'),
		'new_item_name'     => __('New Vacancy Type Name', 'textdomain'),
		'menu_name'         => __('Vacancy Type', 'textdomain'),
	);
	$args = array(
		'labels' => $labels,
		'description' => __('', 'textdomain'),
		'hierarchical' => true,
		'public' => false,
		'publicly_queryable' => false,
		'show_ui' => true,
		'show_in_menu' => true,
		'show_in_nav_menus' => false,
		'show_tagcloud' => false,
		'show_in_quick_edit' => true,
		'show_admin_column' => false,
		'show_in_rest' => true,
	);
	register_taxonomy('vacancy_type', array('vacancy'), $args);
}
add_action('init', 'create_vacancy_type_tax');

// Register Custom Post Type Governors
function create_governor_cpt()
{

	$labels = array(
		'name' => _x('Governors', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Governor', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Governors', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Governor', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Governor Archives', 'textdomain'),
		'attributes' => __('Governor Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Governor:', 'textdomain'),
		'all_items' => __('All Governors', 'textdomain'),
		'add_new_item' => __('Add New Governor', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Governor', 'textdomain'),
		'edit_item' => __('Edit Governor', 'textdomain'),
		'update_item' => __('Update Governor', 'textdomain'),
		'view_item' => __('View Governor', 'textdomain'),
		'view_items' => __('View Governor', 'textdomain'),
		'search_items' => __('Search Governor', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Governor', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Governor', 'textdomain'),
		'items_list' => __('Governor list', 'textdomain'),
		'items_list_navigation' => __('Governor list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Governor list', 'textdomain'),
	);
	$icon_governors = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-governors-white.svg');
	$args = array(
		'label' => __('Governors', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_governors),
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'taxonomies'  => array( 'category' ),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => false,
		'show_in_rest' => true,
		'publicly_queryable' => true,
		'capability_type' => 'post',
	);
	register_post_type('governor', $args);
}
add_action('init', 'create_governor_cpt', 0);

// Register Custom Post Type Events
function create_event_cpt()
{

	$labels = array(
		'name' => _x('Events', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Event', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Events', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Event', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Event Archives', 'textdomain'),
		'attributes' => __('Event Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Event:', 'textdomain'),
		'all_items' => __('All Events', 'textdomain'),
		'add_new_item' => __('Add New Event', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Event', 'textdomain'),
		'edit_item' => __('Edit Event', 'textdomain'),
		'update_item' => __('Update Event', 'textdomain'),
		'view_item' => __('View Event', 'textdomain'),
		'view_items' => __('View Event', 'textdomain'),
		'search_items' => __('Search Event', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Event', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Event', 'textdomain'),
		'items_list' => __('Event list', 'textdomain'),
		'items_list_navigation' => __('Event list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Event list', 'textdomain'),
	);
	$icon_events = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-events-white.svg');

	$args = array(
		'label' => __('Events', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_events),
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => true,
		'show_in_rest' => true,
		'publicly_queryable' => false,
		'capability_type' => 'post',
	);
	register_post_type('cran-event', $args);
}
add_action('init', 'create_event_cpt', 0);

// Register Taxonomy Event Category
function create_event_cat_tax()
{
	$labels = array(
		'name'              => _x('Event Categories', 'taxonomy general name', 'textdomain'),
		'singular_name'     => _x('Event Category', 'taxonomy singular name', 'textdomain'),
		'search_items'      => __('Search Event Categories', 'textdomain'),
		'all_items'         => __('All Event Categories', 'textdomain'),
		'parent_item'       => __('Parent Event Category', 'textdomain'),
		'parent_item_colon' => __('Parent Event Category:', 'textdomain'),
		'edit_item'         => __('Edit Event Category', 'textdomain'),
		'update_item'       => __('Update Event Category', 'textdomain'),
		'add_new_item'      => __('Add New Event Category', 'textdomain'),
		'new_item_name'     => __('New Event Category Name', 'textdomain'),
		'menu_name'         => __('Event Category', 'textdomain'),
	);
	$args = array(
		'labels' => $labels,
		'description' => __('', 'textdomain'),
		'hierarchical' => true,
		'public' => false,
		'publicly_queryable' => false,
		'show_ui' => true,
		'show_in_menu' => true,
		'show_in_nav_menus' => false,
		'show_tagcloud' => false,
		'show_in_quick_edit' => true,
		'show_admin_column' => false,
		'show_in_rest' => true,
	);
	register_taxonomy('event-type', array('cran-event'), $args);
}
add_action('init', 'create_event_cat_tax');


// Register Custom Post Type Departments
function create_department_cpt()
{

	$labels = array(
		'name' => _x('Departments', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Department', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Departments', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Department', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Department Archives', 'textdomain'),
		'attributes' => __('Department Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Department:', 'textdomain'),
		'all_items' => __('All Departments', 'textdomain'),
		'add_new_item' => __('Add New Department', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Department', 'textdomain'),
		'edit_item' => __('Edit Department', 'textdomain'),
		'update_item' => __('Update Department', 'textdomain'),
		'view_item' => __('View Department', 'textdomain'),
		'view_items' => __('View Department', 'textdomain'),
		'search_items' => __('Search Department', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Department', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Department', 'textdomain'),
		'items_list' => __('Department list', 'textdomain'),
		'items_list_navigation' => __('Department list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Department list', 'textdomain'),
	);
	$icon_departments = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-departments-white.svg');

	$args = array(
		'label' => __('Departments', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_departments),
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'taxonomies'  => array( '' ),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => false,
		'show_in_rest' => true,
		'publicly_queryable' => true,
		'capability_type' => 'post',
	);
	register_post_type('department', $args);
}
add_action('init', 'create_department_cpt', 0);


// Register Taxonomy Department Category
function create_department_cat_tax()
{
	$labels = array(
		'name'              => _x('Department Categories', 'taxonomy general name', 'textdomain'),
		'singular_name'     => _x('Department Category', 'taxonomy singular name', 'textdomain'),
		'search_items'      => __('Search Department Categories', 'textdomain'),
		'all_items'         => __('All Department Categories', 'textdomain'),
		'parent_item'       => __('Parent Department Category', 'textdomain'),
		'parent_item_colon' => __('Parent Department Category:', 'textdomain'),
		'edit_item'         => __('Edit Department Category', 'textdomain'),
		'update_item'       => __('Update Department Category', 'textdomain'),
		'add_new_item'      => __('Add New Department Category', 'textdomain'),
		'new_item_name'     => __('New Department Category Name', 'textdomain'),
		'menu_name'         => __('Department Category', 'textdomain'),
	);
	$args = array(
		'labels' => $labels,
		'description' => __('', 'textdomain'),
		'hierarchical' => true,
		'public' => false,
		'publicly_queryable' => false,
		'show_ui' => true,
		'show_in_menu' => true,
		'show_in_nav_menus' => false,
		'show_tagcloud' => false,
		'show_in_quick_edit' => true,
		'show_admin_column' => false,
		'show_in_rest' => true,
	);
	register_taxonomy('department_cat', array('department'), $args);
}
add_action('init', 'create_department_cat_tax');


// Register Custom Post Type Sports

function create_sport_cpt()
{

	$labels = array(
		'name' => _x('Sports', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Sport', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Sports', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Sport', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Sport Archives', 'textdomain'),
		'attributes' => __('Sport Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Sport:', 'textdomain'),
		'all_items' => __('All Sports', 'textdomain'),
		'add_new_item' => __('Add New Sport', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Sport', 'textdomain'),
		'edit_item' => __('Edit Sport', 'textdomain'),
		'update_item' => __('Update Sport', 'textdomain'),
		'view_item' => __('View Sport', 'textdomain'),
		'view_items' => __('View Sport', 'textdomain'),
		'search_items' => __('Search Sport', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Sport', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Sport', 'textdomain'),
		'items_list' => __('Sport list', 'textdomain'),
		'items_list_navigation' => __('Sport list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Sport list', 'textdomain'),
	);
	$icon_sport = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-sports-white.svg');
	
	
	
	$args = array(
		'label' => __('Sports', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_sport), 
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'taxonomies'  => array( '' ),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => false,
		'show_in_rest' => true,
		'publicly_queryable' => false,
		'capability_type' => 'post',
	);
	register_post_type('sport', $args);
}
add_action('init', 'create_sport_cpt', 0);

// Register Custom Post Type Events & Trips
function create_chdbtrips_cpt()
{

	$labels = array(
		'name' => _x('Events & Trips', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Event & Trip', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Events & Trips', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Event & Trip', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Event & Trip Archives', 'textdomain'),
		'attributes' => __('Event & Trip Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Event & Trip:', 'textdomain'),
		'all_items' => __('All Events & Trips', 'textdomain'),
		'add_new_item' => __('Add New Event & Trip', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Event & Trip', 'textdomain'),
		'edit_item' => __('Edit Event & Trip', 'textdomain'),
		'update_item' => __('Update Event & Trip', 'textdomain'),
		'view_item' => __('View Event & Trip', 'textdomain'),
		'view_items' => __('View Event & Trip', 'textdomain'),
		'search_items' => __('Search Event & Trip', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Event & Trip', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Event & Trip', 'textdomain'),
		'items_list' => __('Event & Trip list', 'textdomain'),
		'items_list_navigation' => __('Event & Trip list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Event & Trip list', 'textdomain'),
	);
	$icon_events_trips = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-events-trips-white.svg');

	$args = array(
		'label' => __('Events & Trips', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_events_trips),
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => true,
		'show_in_rest' => true,
		'publicly_queryable' => false,
		'capability_type' => 'post',
	);
	register_post_type('chdbtrips', $args);
}
add_action('init', 'create_chdbtrips_cpt', 0);


// Register Custom Post Type Cranleigh Matters
function create_cranleighmatters_cpt()
{

	$labels = array(
		'name' => _x('Cranleigh Matters', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Cranleigh Matter', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Cranleigh Matters', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Cranleigh Matter', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Cranleigh Matter Archives', 'textdomain'),
		'attributes' => __('Cranleigh Matter Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Cranleigh Matter:', 'textdomain'),
		'all_items' => __('All Cranleigh Matters', 'textdomain'),
		'add_new_item' => __('Add New Cranleigh Matter', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Cranleigh Matter', 'textdomain'),
		'edit_item' => __('Edit Cranleigh Matter', 'textdomain'),
		'update_item' => __('Update Cranleigh Matter', 'textdomain'),
		'view_item' => __('View Cranleigh Matter', 'textdomain'),
		'view_items' => __('View Cranleigh Matter', 'textdomain'),
		'search_items' => __('Search Cranleigh Matter', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Cranleigh Matter', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Cranleigh Matter', 'textdomain'),
		'items_list' => __('Cranleigh Matter list', 'textdomain'),
		'items_list_navigation' => __('Cranleigh Matter list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Cranleigh Matter list', 'textdomain'),
	);
	$icon_cranleigh_matters = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-cranleigh-matters-white.svg');

	$args = array(
		'label' => __('Cranleigh Matters', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_cranleigh_matters),
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'taxonomies'  => array( 'category' ),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => true,
		'show_in_rest' => true,
		'publicly_queryable' => false,
		'capability_type' => 'post',
	);
	register_post_type('cranleigh-matters', $args);
}
add_action('init', 'create_cranleighmatters_cpt', 0);



// Register Custom Post Type Cranleigh Matters News
function create_cranleighmn_cpt()
{

	$labels = array(
		'name' => _x('Cranleigh Weekly News', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Cranleigh Weekly News', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Cranleigh Weekly News', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Cranleigh Matter', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Cranleigh Weekly News Archives', 'textdomain'),
		'attributes' => __('Cranleigh Weekly News Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Cranleigh Weekly News:', 'textdomain'),
		'all_items' => __('All Cranleigh Weekly News', 'textdomain'),
		'add_new_item' => __('Add New Cranleigh Weekly News', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Cranleigh Weekly News', 'textdomain'),
		'edit_item' => __('Edit Cranleigh Weekly News', 'textdomain'),
		'update_item' => __('Update Cranleigh Weekly News', 'textdomain'),
		'view_item' => __('View Cranleigh Weekly News', 'textdomain'),
		'view_items' => __('View Cranleigh Weekly News', 'textdomain'),
		'search_items' => __('Search Cranleigh Weekly News', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Cranleigh Weekly News', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Cranleigh Weekly News', 'textdomain'),
		'items_list' => __('Cranleigh Weekly News list', 'textdomain'),
		'items_list_navigation' => __('Cranleigh Weekly News list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Cranleigh Weekly News list', 'textdomain'),
	);
	$icon_cranleigh_matters = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-cranleigh-matters-white.svg');

	$args = array(
		'label' => __('Cranleigh Weekly News', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_cranleigh_matters),
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'taxonomies'  => array( 'category' ),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => false,
		'hierarchical' => true,
		'exclude_from_search' => true,
		'show_in_rest' => true,
		'publicly_queryable' => true,
		'capability_type' => 'post',
		//'rewrite' => array('slug' => 'weekly-news'),  
	);
	register_post_type('matters-news', $args);
}
add_action('init', 'create_cranleighmn_cpt', 0);

// Register Custom Post Type Dedicated Community
function create_dedicatedcommunity_cpt()
{

	$labels = array(
		'name' => _x('Dedicated Community', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Dedicated Community', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Dedicated Community', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Dedicated Community', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Dedicated Community Archives', 'textdomain'),
		'attributes' => __('Dedicated Community Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Dedicated Community:', 'textdomain'),
		'all_items' => __('All Dedicated Community', 'textdomain'),
		'add_new_item' => __('Add New Dedicated Community', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Dedicated Community', 'textdomain'),
		'edit_item' => __('Edit Dedicated Community', 'textdomain'),
		'update_item' => __('Update Dedicated Community', 'textdomain'),
		'view_item' => __('View Dedicated Community', 'textdomain'),
		'view_items' => __('View Dedicated Community', 'textdomain'),
		'search_items' => __('Search Dedicated Community', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Dedicated Community', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Dedicated Community', 'textdomain'),
		'items_list' => __('Dedicated Community list', 'textdomain'),
		'items_list_navigation' => __('Dedicated Community list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Dedicated Community list', 'textdomain'),
	);
	$icon_dedicated = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-dedicated-community-white.svg');

	$args = array(
		'label' => __('Dedicated Community', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_dedicated),
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'taxonomies'  => array( 'category' ),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => true,
		'show_in_rest' => true,
		'publicly_queryable' => false,
		'capability_type' => 'post',
	);
	register_post_type('dedicated-community', $args);
}
add_action('init', 'create_dedicatedcommunity_cpt', 0);

// Register Custom Post Type Culture Magazine
function create_culture_cpt()
{

	$labels = array(
		'name' => _x('Culture Magazine', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Culture Magazine', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Culture Magazine', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Culture Magazine', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Culture Magazine Archives', 'textdomain'),
		'attributes' => __('Culture Magazine Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Culture Magazine:', 'textdomain'),
		'all_items' => __('All Culture Magazine', 'textdomain'),
		'add_new_item' => __('Add New Culture Magazine', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Culture Magazine', 'textdomain'),
		'edit_item' => __('Edit Culture Magazine', 'textdomain'),
		'update_item' => __('Update Culture Magazine', 'textdomain'),
		'view_item' => __('View Culture Magazine', 'textdomain'),
		'view_items' => __('View Culture Magazine', 'textdomain'),
		'search_items' => __('Search Culture Magazine', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Culture Magazine', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Culture Magazine', 'textdomain'),
		'items_list' => __('Culture Magazine list', 'textdomain'),
		'items_list_navigation' => __('Culture Magazine list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Culture Magazine list', 'textdomain'),
	);
	$icon_culture_magazine = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-cranleigh-culture-magazine-white.svg');

	$args = array(
		'label' => __('Culture Magazine', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode($icon_culture_magazine),
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'taxonomies'  => array( 'category' ),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => false,
		'show_in_rest' => true,
		'publicly_queryable' => true,
		'capability_type' => 'post',
	);
	register_post_type('culture', $args);
}
add_action('init', 'create_culture_cpt', 0);



// Register Custom Post Type Podcast
function create_podcast_cpt()
{

	$labels = array(
		'name' => _x('Podcast', 'Post Type General Name', 'textdomain'),
		'singular_name' => _x('Podcast', 'Post Type Singular Name', 'textdomain'),
		'menu_name' => _x('Podcast', 'Admin Menu text', 'textdomain'),
		'name_admin_bar' => _x('Podcast', 'Add New on Toolbar', 'textdomain'),
		'archives' => __('Podcast Archives', 'textdomain'),
		'attributes' => __('Podcast Attributes', 'textdomain'),
		'parent_item_colon' => __('Parent Podcast:', 'textdomain'),
		'all_items' => __('All Podcast', 'textdomain'),
		'add_new_item' => __('Add New Podcast', 'textdomain'),
		'add_new' => __('Add New', 'textdomain'),
		'new_item' => __('New Podcast', 'textdomain'),
		'edit_item' => __('Edit Podcast', 'textdomain'),
		'update_item' => __('Update Podcast', 'textdomain'),
		'view_item' => __('View Podcast', 'textdomain'),
		'view_items' => __('View Podcast', 'textdomain'),
		'search_items' => __('Search Podcast', 'textdomain'),
		'not_found' => __('Not found', 'textdomain'),
		'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
		'featured_image' => __('Featured Image', 'textdomain'),
		'set_featured_image' => __('Set featured image', 'textdomain'),
		'remove_featured_image' => __('Remove featured image', 'textdomain'),
		'use_featured_image' => __('Use as featured image', 'textdomain'),
		'insert_into_item' => __('Insert into Podcast', 'textdomain'),
		'uploaded_to_this_item' => __('Uploaded to this Podcast', 'textdomain'),
		'items_list' => __('Podcast list', 'textdomain'),
		'items_list_navigation' => __('Podcast list navigation', 'textdomain'),
		'filter_items_list' => __('Filter Podcast list', 'textdomain'),
	);
	//$icon_dedicated = file_get_contents(get_template_directory() . '/assets/images/admin/wp-icon-dedicated-community-white.svg');

	$args = array(
		'label' => __('Podcast', 'textdomain'),
		'description' => __('', 'textdomain'),
		'labels' => $labels,
		'menu_icon' => 'dashicons-format-video',
		'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
		'taxonomies'  => array( 'category' ),
		'public' => true,
		'show_ui' => true,
		'show_in_menu' => true,
		'menu_position' => 5,
		'show_in_admin_bar' => true,
		'show_in_nav_menus' => true,
		'can_export' => true,
		'has_archive' => true,
		'hierarchical' => true,
		'exclude_from_search' => true,
		'show_in_rest' => true,
		'publicly_queryable' => false,
		'capability_type' => 'post',
	);
	register_post_type('podcast', $args);
}
add_action('init', 'create_podcast_cpt', 0);


// Register Taxonomy Podcast Type
function create_podcast_type_tax()
{
	$labels = array(
		'name'              => _x('Podcast Types', 'taxonomy general name', 'textdomain'),
		'singular_name'     => _x('Podcast Type', 'taxonomy singular name', 'textdomain'),
		'search_items'      => __('Search Podcast Types', 'textdomain'),
		'all_items'         => __('All Podcast Types', 'textdomain'),
		'parent_item'       => __('Parent Podcast Type', 'textdomain'),
		'parent_item_colon' => __('Parent Podcast Type:', 'textdomain'),
		'edit_item'         => __('Edit Podcast Type', 'textdomain'),
		'update_item'       => __('Update Podcast Type', 'textdomain'),
		'add_new_item'      => __('Add New Podcast Type', 'textdomain'),
		'new_item_name'     => __('New Podcast Type Name', 'textdomain'),
		'menu_name'         => __('Podcast Type', 'textdomain'),
	);
	$args = array(
		'labels' => $labels,
		'description' => __('', 'textdomain'),
		'hierarchical' => true,
		'public' => false,
		'publicly_queryable' => false,
		'show_ui' => true,
		'show_in_menu' => true,
		'show_in_nav_menus' => false,
		'show_tagcloud' => false,
		'show_in_quick_edit' => true,
		'show_admin_column' => false,
		'show_in_rest' => true,
	);
	register_taxonomy('podcast_type', array('podcast'), $args);
}
add_action('init', 'create_podcast_type_tax');

/**
 * Changes WP default behaviour.
 * By default WP does not show any results in archives page if your taxonomy is only associated with a custom post type.
 *
 * @param [type] $query
 * @return void
 */
function dw_handle_posts( $query ) {
	if ( ! $query->is_main_query() || is_admin() ) {
		return;
	}

	if ( $query->is_tax ) {
		$post_type = get_query_var( 'post_type' );

		if ( ! $post_type ) {
			global $wp_taxonomies;

			$taxo      = get_queried_object();
			$post_type = ( isset( $wp_taxonomies[ $taxo->taxonomy ] ) ) ? $wp_taxonomies[ $taxo->taxonomy ]->object_type : array();

			$query->set( 'post_type', $post_type );
		}
	}

	return $query;
}
//add_action( 'pre_get_posts', 'dw_handle_posts' );



add_filter('register_post_type_args', 'modify_staff_post_type_args', 10, 2);
function modify_staff_post_type_args($args, $post_type) {
    if ('staff' === $post_type) {
        $args['publicly_queryable'] = false;
    }
    return $args;
}


add_action('template_redirect', 'redirect_cpt_single');
function redirect_cpt_single() {
    $post_types_to_redirect = array('staff', 'chdbtrips', 'cran-event', 'dedicated-community', 'cranleigh-matters');

    if (is_singular($post_types_to_redirect)) {
        wp_redirect(home_url()); // Redirect to homepage or change the URL
        exit;
    }
}



