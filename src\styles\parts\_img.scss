///ALIGNMENT
img.alignleft {
    float: left;
    margin-right: 30px;
}

img.alignright {
    float: right;
    margin-left: 30px;
}

img.logo-divider {
    max-width: 90%;
}

img.aligncenter {
    display: block;
    margin: 0 auto;
}

///RESPONSIVE
img.responsive {
    width: 100%;
    height: auto;
}


///BACKGROUNDS 
.img-background {
	background-size: cover !important;
	background-repeat: no-repeat !important;
	background-position: center !important;

    &--cover {
        background-size: cover !important;
		background-repeat: no-repeat !important;
		background-position: center !important;
    }
    &--contain {
        background-size: contain !important;
        background-repeat: no-repeat !important;
		background-position: center !important;
    }
}




// Object fit.
.object-fit {
    &--cover {
        background-size: cover;
        background-position: center center;

        img {
            object-fit: cover;
        }
    }    
    
    &--contain {
        background-size: contain;
        background-position: center center;
        background-repeat: no-repeat;

        img {
            object-fit: contain;
        }
    }

    &--circle{
        img{
            border-radius: 50%;
        }
    }
    &.not-compatible {
        img {
            opacity: 0;
        }
    }
}

.absolute-fill{
    position: relative;
    & > * {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    }
}

.bleed-right{
    $container-width: 1300px;   
    $gutter-width: 30px;
    width: calc(100% + 50vw  - ((#{$container-width} - #{$gutter-width}) / 2));
    max-width: none;   
}

.responsive-img{
    height: 0;
    position: relative;
    img{
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
    }
}

