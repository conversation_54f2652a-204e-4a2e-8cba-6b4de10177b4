<?php // Staff 
?>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 lg:gap-x-[30px] lg:gap-y-14 mb-10 lg:mb-12 md:px-6 lg:px-0">
    <?php if ($query->have_posts()) : ?>
            <?php
            while ($query->have_posts()) : $query->the_post();  ?>
          
           <div class="relative mb-0 lg:mb-4 cursor-pointer" data-element="play_video" data-video="<?php the_field('vimeo_id_podcast');?>"> 
                <span class="absolute top-0 right-0 w-[50px] h-[50px]  text-[22px] flex justify-center items-center bg-cranleighclay">
                    #<?php echo get_field('number_podcast'); ?>
                </span>
                <?php if(get_the_post_thumbnail_url()): ?>
                    <img class="aspect-[1.78540772532] w-full object-cover" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
                <?php else: ?>
                    <img class="aspect-[1.78540772532] w-full object-cover" src="<?php the_field('staff_fallback_image', 'options'); ?>" alt="<?php the_title();?>" />
                <?php endif; ?>
                <div class=" text-green-light text-[14px] uppercase leading-[19px] mt-[22px]">
                            
                <?php echo the_field('date_podcast'); ?>                            
                <?php
                
                $cat = get_the_terms($post->ID, 'podcast_type');
                if ($cat) : ?> • <?php echo $cat[0]->name; ?><?php endif; ?>
                <?php echo the_field('date_podcast'); ?>    
                </div>

                <h2 class=" text-title30 uppercase mt-3 mb-3"><?php the_title();?></h2>
                <div class="text-copy16"><?php echo the_excerpt(); ?></div>

               


            </div>
                

            <?php 
            endwhile; ?>

            <?php
            $total_pages = $query->max_num_pages;
            $paged = isset($_GET['sf_paged']) ? $_GET['sf_paged'] : 1;

            ?>
            </div>
            <div class="w-full mt-20 mb-20">
                <div class="flex justify-center text-black">

                <?php require locate_template( 'tpl/parts/_pagination.php' ); ?>
            </div>
           
                  
      


    <?php else : ?>
        <div class="text-center">
            <h2 class="">Sorry, no results found</h2>
        </div>
    <?php endif; ?>
   
</div>