<?php
/**
 * Template part to display the houses tpl
 *
 * @package twkmedia
 */



?>
 <?php 
    $image = get_field('banner_housestpl');
    if( !empty( $image ) ): ?>
<div data-element="houses_banner" class="relative flex justify-center items-end z-10 h-[calc(100vh_-_env(safe-area-inset-bottom))] lg:h-screen w-screen 
 xl:bg-fixed bg-no-repeat bg-cover bg-center"
style="background-image:url('<?php echo esc_url($image['url']); ?>');"
>
    <div data-element="houses_overlay" class="absolute w-full h-full inset-0 before:content-[''] before:absolute before:top-0 before:left-0 before:w-full before:h-[20vh] before:bg-gradient-to-t before:from-black/0 before:to-black/50
    after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-[50vh] after:bg-gradient-to-b after:from-black/0 after:to-black/50">
        
    </div>
    <div class="text-center relative z-20 pb-32" data-element="houses_title">
        <span class="font-aspectbold text-white text-base uppercase"><?php the_field('subtitle_housestpl'); ?></span>
        <h1 class="font-aspectbold text-white text-title46 lg:text-titlelg uppercase mt-2 lg:mt-6"><?php the_field('title_housestpl'); ?></h1>
    </div>
 </div>
<?php else: ?>
    <?php 
    $pages_fallback_image = get_field('pages_fallback_image', 'options');?>
    <div data-element="houses_banner" class="relative flex justify-center items-end z-10 h-[calc(100vh_-_env(safe-area-inset-bottom))] lg:h-screen w-screen  bg-fixed bg-no-repeat bg-cover bg-center"
style="background-image:url('<?php echo esc_url($pages_fallback_image['url']); ?>');">
</div>
    <div data-element="houses_overlay" class="absolute w-full h-full inset-0 before:content-[''] before:absolute before:top-0 before:left-0 before:w-full before:h-[20vh] before:bg-gradient-to-t before:from-black/0 before:to-black/50
    after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-[50vh] after:bg-gradient-to-b after:from-black/0 after:to-black/50">
        
    </div>
    <div class="text-center relative z-20 pb-32" data-element="houses_title">
        <span class="font-aspectbold text-white text-base uppercase"><?php the_field('subtitle_housestpl'); ?></span>
        <h1 class="font-aspectbold text-white text-title46 lg:text-titlelg uppercase mt-6"><?php the_field('title_housestpl'); ?></h1>
    </div>
</div>
<?php endif; ?>
<div class="relative z-20  w-100vw  -mt-[100px] pt-[40px]">
    <div class="absolute bg-blue h-full w-[calc(100vw-60px)] lg:w-[calc(100vw-124px)] left-1/2 -translate-x-1/2 top-0"  data-element="houses_scroll"></div>
    <div class="relative z-10">
        <div>
            <span class="relative table font-aspectbold text-white text-base uppercase text-center pr-7 mx-auto
            after:content-[''] after:absolute after:top-1/2 after:right-0 after:-translate-y-[calc(50%_+_1px)] after:w-[14px] after:h-[15px] after:bg-arrow-down-white after:bg-contain after:bg-no-repeat after:bg-center" data-element="houses_scroll_down">Scroll down</span>

        </div>
        <div class="container lg:flex items-center pb-16 pt-[45px] px-10 lg:px-0">
            <div class="lg:w-6/12 wysiwyg wysiwygwhite text-white pr-[10%]">
                <?php the_field('copy_housestpl'); ?>
            </div>
            <div class="lg:w-6/12 mt-8 lg:mt-0">
                <div class="swiper" data-element="image_carousel">
                <div class="swiper-wrapper">
                <?php if( have_rows('image_carousel_housestpl') ): ?>
                    <?php while( have_rows('image_carousel_housestpl') ): the_row();?>
                        <?php 
                        $image = get_sub_field('image');
                        if( !empty( $image ) ): ?>
                        <div class="swiper-slide">
                            <?php echo twk_output_acf_image($image, 'large', 'aspect-[5/3] object-cover object-center' , 'lazy'); ?>
                        </div>
                        <?php endif; ?>
                    <?php endwhile; ?>
                <?php endif; ?>
                </div>
                <div class="swiper-pagination"></div>
            </div>
        
            </div>
            
        
        </div>
    </div>
</div>
<div class="relative bg-white">
    <div class="container px-10 lg:px-0">
        <h2 class="text-titleh4 md:text-[48px] lg:text-titleh2 text-center uppercase mt-[90px] mb-[80px] md:mb-5 lg:mb-[80px]"><?php the_field('houses_title_housestpl'); ?></h2>
        <h3 class="text-center lg:text-left text-titlelg lg:text-titlexl uppercase lg:ml-10 translate-y-[34px] lg:translate-y-[70px] lg:-mt-10"><?php the_field('houses_girl_title_housestpl'); ?></h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-8">
            <?php if( have_rows('girls_houses_grid_housestpl') ): ?>
                <?php while( have_rows('girls_houses_grid_housestpl') ): the_row();?>
                    <div class="relative">
                        
                        <?php 
                        $image = get_sub_field('image');
                        if( !empty( $image ) ): ?>
                            <?php echo twk_output_acf_image($image, 'large', 'w-full aspect-[1/1] object-cover' , 'lazy'); ?>
                        <?php endif; ?>
                        <div class="flex py-6 px-5 items-center justify-between bg-white border border-t-0 border-[#ced3d9]">
                            <h3 class="text-titlesidebar uppercase"><?php echo get_sub_field('title');?></h3>
                            <?php 
                            $logo = get_sub_field('logo');
                            if( !empty( $logo ) ): ?>
                                <?php echo twk_output_acf_image($logo, 'large', 'h-8 w-auto' , 'lazy'); ?>
                            <?php endif; ?>
                        </div>
                        
                        <?php if(get_sub_field('link')):?><a class="absolute inset-0 h-full w-full" href="<?php the_sub_field('link');?>" ></a><?php endif; ?>
                    </div>
                <?php endwhile; ?>
            <?php endif; ?>
        </div>

        <div class="flex lg:block flex-col-reverse">
            <div class="relative z-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-8 lg:mt-24 before:content-none lg:before:content-[''] before:absolute before:w-full before:h-px before:bg-blue/50 before:-top-12 before:left-0">
                <?php if( have_rows('boys_houses_grid_housestpl') ): ?>
                    <?php while( have_rows('boys_houses_grid_housestpl') ): the_row();?>
                        <div class="relative">
                            
                            <?php 
                            $image = get_sub_field('image');
                            if( !empty( $image ) ): ?>
                                <?php echo twk_output_acf_image($image, 'large', 'w-full aspect-[1/1] object-cover' , 'lazy'); ?>

                            <?php endif; ?>
                            <div class="flex py-6 px-5 items-center justify-between bg-white border border-t-0 border-[#ced3d9]">
                                <h3 class="text-titlesidebar uppercase"><?php echo get_sub_field('title');?></h3>
                                <?php 
                                $logo = get_sub_field('logo');
                                if( !empty( $logo ) ): ?>
                                    <img class="h-8 w-auto" src="<?php echo esc_url($logo['url']); ?>" alt="<?php echo esc_attr($logo['alt']); ?>" />
                                <?php endif; ?>
                            </div>
                            
                            <?php if(get_sub_field('link')):?><a class="absolute inset-0 h-full w-full" href="<?php the_sub_field('link');?>" ></a><?php endif; ?>
                        </div>
                    <?php endwhile; ?>
                <?php endif; ?>
            </div>
            <h3 class="relative text-titlelg lg:text-titlexl uppercase lg:ml-auto lg:mr-10 text-center lg:text-right translate-y-8 lg:-translate-y-7 lg:-mb-10 mt-20 lg:mt-0"><?php the_field('houses_boy_title_housestpl'); ?></h3>
        </div>
    </div>

    
</div>
    

    


   


