const scssPath = "./src/styles";

module.exports = {
	// ftpDetails: {
	//     sftp: true,
	//     host: "cranleighorg.sftp.wpengine.com",
	//     port: 2222,
	//     username: "cranleighorg-prod",
	//     password: "dUNLugaE6098",
	//     remotePath: "/wp-content/themes/cranleigh"
	// },
	ftpDetails: {
		sftp: true,
		host: "cranleighstage.sftp.wpengine.com",
		port: 2222,
		username: "cranleighstage-twk",
		password: "aEZsDpvkyk7-W3hGxUyG",
		remotePath: "/wp-content/themes/cranleigh",
	},
	// name of output file on the left, path to input file on the right
	entryPoints: {
		bundle: "./src/script.js",
		// screen: scssPath + "/screen.scss",
		"editor-style": scssPath + "/editor-style.scss",
	},
};
