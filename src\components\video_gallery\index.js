import { $qs, $qsa } from '../../utils/QuerySelector';
import gsap from 'gsap';
import Swiper, { Navigation, Pagination, Scrollbar, Autoplay, FreeMode, Grid } from 'swiper';


function video_gallery() { 


    var swiper = new Swiper(".swiper[data-element='video_slider']", {
        modules: [Navigation, Pagination, Scrollbar, Autoplay, Grid],
        loop: false,
        slidesPerView: 1,
        spaceBetween: 24,
        breakpoints: {
            1024: {
              slidesPerView: 2,
              spaceBetween: 0,
              grid: {
                rows:2
              }
            }
          },
        navigation: {
            nextEl: "[data-element='video_slider'] .swiper-button-next",
            prevEl: "[data-element='video_slider'] .swiper-button-prev",
        },
        pagination: {
            el: "[data-element='video_slider'] .swiper-pagination",
            clickable: true,
        },
    });



    $('.magnific-video').magnificPopup({ 
		type: 'iframe',
		mainClass: 'mfp-video-wrapper',
		removalDelay: 160,
		preloader: false,
		fixedContentPos: false,
        gallery: {
			enabled: true,
			navigateByImgClick: true,
			preload: [0,1] // Will preload 0 - before current, and 1 after the current image
		},

		iframe: {
			markup: '<div class="mfp-iframe-scaler">' +
				'<div class="mfp-close"></div>' +
				'<iframe class="mfp-iframe" frameborder="0" allow="autoplay"></iframe>' +
				'</div>',
			patterns: {
				youtu: {
					index: 'youtu.be',
					id: function( url ) {
					
						// Capture everything after the hostname, excluding possible querystrings.
						var m = url.match( /^.+youtu.be\/([^?]+)/ );
				
						if ( null !== m ) {
							return m[1];
						}
				
						return null;
			
					},
					// Use the captured video ID in an embed URL. 
					// Add/remove querystrings as desired.
					src: '//www.youtube.com/embed/%id%?autoplay=1&rel=0'
				},
				youtube: {
					index: 'youtube.com/',
					id: 'v=',
					src: 'https://www.youtube.com/embed/%id%?autoplay=1'
				},
				vimeo: {
					index: 'vimeo.com/', 
					id: function(url) {        
						var m = url.match(/(https?:\/\/)?(www.)?(player.)?vimeo.com\/([a-z]*\/)*([0-9]{6,11})[?]?.*/);
						if ( !m || !m[5] ) return null;
						return m[5];
					},
					src: 'https://player.vimeo.com/video/%id%?autoplay=1'
				}
			}
		}
	});
    
 }
export default video_gallery;