<?php
/**
 * Template Name: Cookies
 *
 * Cookies Settings page.
 *
 * @package twkmedia
 */

get_header();

// Cookies.
// controls whether we need to show the cookies popup or not.
$cookie_name = get_field( 'cookies_bar_cookies_name', 'option' );
if ( '' === $cookie_name || NULL === $cookie_name ) {
	$cookie_name = 'twk_cookies_bar_cookie_00001';
}

if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();
		?>

			<main id="main" class="main my-50">
				<div class="container">
					<div class="row justify-content-center">
						<div class="col-sm-10">
							<h1 class="page-title"><?php the_title(); ?></h1>
						</div>
					</div>
				</div>

				<?php
				// FLEXIBLE CONTENT.
				include locate_template( 'tpl/parts/flexible-content.php' );
				?>

				<div class="container">
					<div class="row justify-content-center">
						<div class="col-sm-10">
							<form action="#" class="my-5">
								<div class="group d-flex align-items-center mb-15" title="Essential cookies are needed for the website to work">
									<p class="d-inline-block align-bottom mr-4 mb-0">Essential cookies</p>
									<label class="switch mb-0" for="essential-check">
										<input type="checkbox" id="essential-check" checked disabled />
										<div class="slider round"></div>
									</label>
								</div>

								<div class="group d-flex align-items-center mb-15">
									<p class="d-inline-block align-bottom mr-4 mb-0">Google Analytics</p>
									<label class="switch mb-0" for="google-analytics-check">
										<input type="checkbox" id="google-analytics-check" />
										<div class="slider round"></div>
									</label>
								</div>

								<div class="my-4">
									<input type="submit" value="Save changes" class="button button-primary accept-cookies">
								</div>
							</form>
						</div>
					</div>
				</div>
			</main>

		<?php
	endwhile;
endif;

get_footer();
?>


<script>
	(function ($){

		$( window ).on('load', function(){

			// Helper functions.
			function getCookie(name) {
				var dc = document.cookie;
				var prefix = name + "=";
				var begin = dc.indexOf("; " + prefix);
				if (begin == -1) {
					begin = dc.indexOf(prefix);
					if (begin != 0) return null;
				}
				else
				{
					begin += 2;
					var end = document.cookie.indexOf(";", begin);
					if (end == -1) {
					end = dc.length;
					}
				}
				// because unescape has been deprecated, replaced with decodeURI
				//return unescape(dc.substring(begin + prefix.length, end));
				return decodeURI(dc.substring(begin + prefix.length, end));
			}

			function setCookie(cname, cvalue, exdays = 5000) {
				var d = new Date();
				d.setTime(d.getTime() + (exdays*24*60*60*1000));
				var expires = "expires="+ d.toUTCString();

				if ( exdays == 0 ){
					document.cookie = cname + "=" + cvalue + ";path=/";
				} else {
					document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
				}
			}

			function deleteCookie(cname,  cvalue) {
				expires = "expires=Fri, 31 Dec 1900 23:59:59 GMT";
				document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
			}

			let cookie_name                       = "<?php echo esc_html( $cookie_name ); ?>"; // controls whether we need to show the cookies popup or not.
			let cookie_name_essential             = "twk_cookies_essential";
			let cookie_name_analytics             = "twk_cookies_google_analytics";
			let twk_cookies_essential             = getCookie(cookie_name_essential);   // Controls if the notifications popups are visible and other essential cookies
			let twk_cookies_google_analytics      = getCookie(cookie_name_analytics);   // Controls if the GA/GTM is used

			// Prepare the slides.
			$( '#necessary-check' ).attr( 'checked', true );  // always needs to be checked

			if ( twk_cookies_google_analytics ) {
				$( '#google-analytics-check' ).attr( 'checked', true );
			}
			
			

			// When accepts cookies.
			$('input.accept-cookies').on('click', function(event){
				event.preventDefault();

				setCookie( cookie_name_essential, 'true'); // Always needs to be accepted for the site to work properly
				setCookie( cookie_name, 'true');

				if ( $( 'input#google-analytics-check' ).is(":checked") ) {
					setCookie( cookie_name_analytics, 'true');
				} else {
					deleteCookie( cookie_name_analytics, 'true');
				}

				location.reload(); // reloads page so the user knows something happened.
			});

		});

	})(jQuery);
</script>
