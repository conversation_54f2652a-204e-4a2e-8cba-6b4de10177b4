.swiper {
	margin-left: auto;
	margin-right: auto;
	position: relative;
	overflow: hidden;
	list-style: none;
	padding: 0;
	z-index: 1;
}
.swiper-vertical > .swiper-wrapper {
	flex-direction: column;
}
.swiper-wrapper {
	position: relative;
	width: 100%;
	height: 100%;
	z-index: 1;
	display: flex;
	transition-property: transform;
	box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-wrapper {
	transform: translate3d(0px, 0, 0);
}
.swiper-pointer-events {
	touch-action: pan-y;
}
.swiper-pointer-events.swiper-vertical {
	touch-action: pan-x;
}
.swiper-slide {
	flex-shrink: 0;
	width: 100%;
	position: relative;
	transition-property: transform;
}
.swiper-slide-invisible-blank {
	visibility: hidden;
}
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
	height: auto;
}
.swiper-autoheight .swiper-wrapper {
	align-items: flex-start;
	transition-property: transform, height;
}
.swiper-backface-hidden .swiper-slide {
	transform: translateZ(0);
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
}
.swiper-css-mode > .swiper-wrapper {
	overflow: auto;
	scrollbar-width: none;
	-ms-overflow-style: none;
}
.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
	display: none;
}
.swiper-css-mode > .swiper-wrapper > .swiper-slide {
	scroll-snap-align: start start;
}
.swiper-horizontal.swiper-css-mode > .swiper-wrapper {
	scroll-snap-type: x mandatory;
}
.swiper-vertical.swiper-css-mode > .swiper-wrapper {
	scroll-snap-type: y mandatory;
}
.swiper-centered > .swiper-wrapper::before {
	content: "";
	flex-shrink: 0;
	order: 9999;
}
.swiper-centered.swiper-horizontal
	> .swiper-wrapper
	> .swiper-slide:first-child {
	margin-inline-start: var(--swiper-centered-offset-before);
}
.swiper-centered.swiper-horizontal > .swiper-wrapper::before {
	height: 100%;
	min-height: 1px;
	width: var(--swiper-centered-offset-after);
}
.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {
	margin-block-start: var(--swiper-centered-offset-before);
}
.swiper-centered.swiper-vertical > .swiper-wrapper::before {
	width: 100%;
	min-width: 1px;
	height: var(--swiper-centered-offset-after);
}
.swiper-centered > .swiper-wrapper > .swiper-slide {
	scroll-snap-align: center center;
}
.swiper-navigation-disabled .swiper-button-next,
.swiper-navigation-disabled .swiper-button-prev {
	display: none !important;
}
.swiper-button-next:after,
.swiper-button-prev:after {
	font-family: swiper-icons;
	font-size: var(--swiper-navigation-size);
	text-transform: none !important;
	letter-spacing: 0;
	font-variant: initial;
	line-height: 1;
}
.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
	content: "next";
}
.swiper-button-lock {
	display: none;
}
.swiper-grid>.swiper-wrapper {
    flex-wrap: wrap
} 

.swiper-grid-column>.swiper-wrapper {
    flex-wrap: wrap;
    flex-direction: row
}