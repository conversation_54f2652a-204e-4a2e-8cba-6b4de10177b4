/*------------------------------------*\
    #CALENDAR
\*------------------------------------*/

$darkColor: $secondary;
$titleFont: "HK Grotesk Bold";
$regularFont: "HK Grotesk Regular";
$accentColor: $primary;
	
.widgettitle {
    display: none; 
}

.allfilters {
    width: 100%;
    display: inline-block;
}

.calendar {
    margin-top: 60px;
}

#calendar {
    
    a {
        font-family: $regularFont;
    }
    
    span {
        font-family: $regularFont;
    }
    
    .calendar-events {
        table-layout: inherit;
        min-width: 100%;
        
        tbody {
            display: table;
            width: 100%;
        }
        
        tr {
            width: 100%;
            /*display: table;*/
        }
    }
    
    tr {
        
        td {
            //min-width: 200px; 
            // padding: 12px 30px; 
            padding: 7px 20px;
            border: 1px solid #DDD;
        }
    }

    tr.date {
        font-family: $titleFont;
        text-transform: uppercase;
        line-height: 24px;
        color: $primary;
        background-color: $darkColor !important;

        td {
            padding: 18px;
        }
    }
}

.tribe-mini-calendar-wrapper {
    margin-bottom: 40px;
}

.tribe-mini-calendar-list-wrapper {
    display: none;
}
//#calendar tr.date.today{ background-color: $accentColor !important; }



#calendar tr td:first-child,
#calendar tr td:last-child {
    padding-left: 20px;
    padding-right: 20px;
}

#calendar tr td.ta_c,
#calendar tr td.event,
#calendar tr td a {
    font-size: 18px;
}

#calendar tr:nth-child(even) {
    background-color: #e8e8e8;
}

#calendar tr:nth-child(odd) {
    background-color: #f7f7f7;
}

#calendar tr td.event a {
    color: $text-grey;
}

#calendar tr td.ta_c {
    width: 120px;
    color: $text-grey;
}

#calendar tr td.location {
    width: 160px;
}

.tribe-mini-calendar td.tribe-events-has-events a:hover {
    /*background-color: $accentColor !important;*/
    background-color: #808998 !important;
}

.tribe-mini-calendar .tribe-events-has-events div[id*='daynum-'] a:before {
    background-color: white;
}

.tribe-mini-calendar .tribe-events-has-events div[id*='daynum-'] a:before {
    width: 5px;
    height: 5px;
}

#calendar .calendar-nav { 
    line-height: 23px; 
    color: $text-grey; 
    font-size: 16px; 
}

#calendar .calendar-nav a {
    margin: 0 5px;
    
    &:hover {
        color: $secondary;
    }
}

#calendar .calendar-nav .active-item {
    color: $secondary !important;
}

// Mini calendar
.tribe-mini-calendar .tribe-mini-calendar-dayofweek { 
    color: $darkColor; 
    font-family: $titleFont;
    font-size: 18px; 
    font-weight: 700; 
    line-height: 32px; 
    text-align: center; 
    background-color: transparent !important; 
}

.tribe-mini-calendar .tribe-mini-calendar-nav td,
.tribe-mini-calendar thead { 
    background-color: transparent !important; 
    background: transparent !important; 
    border: none; 
}

.tribe-mini-calendar td.tribe-events-future.tribe-mini-calendar-today,
.tribe-mini-calendar td.tribe-events-past.tribe-mini-calendar-today {
	background-color: $primary !important;
}

.tribe-mini-calendar td.tribe-events-has-events.tribe-mini-calendar-today {
    background-color: $primary !important;
}

.tribe-mini-calendar .tribe-mini-calendar-nav span { 
    color: $darkColor; 
    font-family: $titleFont;
    padding-bottom: 20px;
    font-size: 24px; 
    font-weight: 700; 
    line-height: 32px; 
}

.tribe-mini-calendar .tribe-mini-calendar-dayofweek {
    border: none;
}

.tribe-mini-calendar td.tribe-events-past {
    background-color: #b2b2b2 !important;
}

.tribe-mini-calendar td.tribe-events-past.tribe-events-has-events {
    background-color: $secondary !important;
}

.tribe-mini-calendar td.tribe-events-present {
    background-color: $accentColor !important;
}

.tribe-mini-calendar td.tribe-events-future {
    background-color: $text-grey !important;
    opacity: 0.35;
}

.tribe-mini-calendar td.tribe-events-future.tribe-events-has-events {
    background-color: $darkColor !important;
    opacity: 1;
}

.tribe-mini-calendar .tribe-mini-calendar-no-event,
.tribe-mini-calendar .tribe-events-has-events a { 
    color: $darkColor !important; 
    padding: 15px 0px !important; 
}

.tribe-mini-calendar .tribe-mini-calendar-no-event, 
.tribe-mini-calendar .tribe-events-has-events a {
    color: white !important;
}

.tribe-mini-calendar .tribe-events-has-events div[id*='daynum-'] a:before { 
    background: white !important; 
    bottom: 8px !important; 
    display: block;
    width: 5px !important;
    height: 5px !important;
    margin: 5px auto 0 -3px !important;
}

.tribe-mini-calendar-nav .prev-month {
    float: left;
}

.tribe-mini-calendar-nav .next-month {
    float: right;
}

.tribe-mini-calendar .tribe-mini-calendar-nav .next-month span,
.tribe-mini-calendar .tribe-mini-calendar-nav .prev-month span { 
    text-indent: 100%; 
    overflow: hidden; 
    white-space: nowrap; 
    width: 35px; 
    height: 19px; 
    background-image: url('../img/Arrow.svg'); 
	background-size: cover;
    vertical-align: middle; 
    display: inline-block; 
}

.tribe-mini-calendar .tribe-mini-calendar-nav .prev-month {
    transform: rotate(90deg);
}

.tribe-mini-calendar .tribe-mini-calendar-nav .next-month {
    transform: rotate(-90deg);
}

.tribe-mini-calendar .tribe-mini-calendar-nav .next-month span {
    background-position: 100% 101%;
}

.calendar-nav {
    display: inline-block;
}

.calendar-filter { 
    display:inline-block; 
    // float: right; 
    width: 100%;
    margin-bottom: 20px; 
}

.calendar-filter h4 { 
    display: inline-block; 
    padding-right: 15px; 
    position: relative; 
}

.calendar-filter h4:after { 
    position: absolute; 
    content: ''; 
    height: 1px; 
    width: 500px; 
    right: -500px; 
    top: 16px; 
    background-color: $cranleighClay; 
}

#month-selector,
#subcat-calendar-filter,
#calendar-filter {
    width: 100%;
}

#month-selector select,
#subcat-calendar-filter select,
#calendar-filter select { 
    background-color: #e8e8e8; 
    border-radius: 23px; 
    color: $darkColor; 
    font-size: 18px; 
    // font-weight: 700; 
    line-height: 20px; 
    padding: 11px 40px 9px 20px !important; 
    background-position: 90% 50%; 
    border: none; 
    -webkit-appearance: none; 
    -moz-appearance: none; 
    appearance: none; 
    outline: none; 
    background-image: url('../img/Arrow.svg'); 
    background-repeat: no-repeat; 
    width: 100%;
}
// .subcat-filter-wrapper{ margin-left: 20px; }



// Calendar Lightbox
#calendar-lightbox { 
    position: fixed; 
    top: 0; 
    left: 0; 
    width: 100%; 
    background-color: rgba(0,0,0,.8); 
    height: 100vh; 
    z-index: 10000; 
}

.calendar-lightbox__content { 
    max-width: 500px; 
    width: 500px; 
    background-color: white; 
    position:fixed; 
    top: 50%; 
    left: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%); 
    transform: translateX(-50%) translateY(-50%);
    padding: 20px;
}

.calendar-lightbox__content h3 { 
    margin-top: 0; 
    font-size: 35px; 
    line-height: 120%; 
    font-family: $titleFont;
}

#content-area .time-event,
.calendar-lightbox__content .time-event { 
    font-size: 18px; 
    width: 100%;
    display: inline-block;
}

// #ajax-cal-content{ padding-bottom: 40px; }

#content-area .tribe-events-cal-links,
.calendar-lightbox__content .tribe-events-cal-links {
    margin-top: 30px;
}

#content-area .tribe-events-cal-links a,
.content #tribe-events .cal-content .tribe-events-cal-links a, 
.calendar-lightbox__content .tribe-events-cal-links a {
    position: relative;
    display: inline-block;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 1px;
    font-weight: 500;
    text-transform: uppercase;
    color: $primary;
    background-color: transparent;
    padding: 15px 20px;
    border: 2px solid $primary;
    border-radius: 0;
    margin-right: 20px;
    outline: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover {
        background-color: $primary;
        color: #FFF;
    }
    
}

#content-area{ width: 100%; }
#content-area h3{ padding-top: 0; }

#content-area .loadergif,
.calendar-lightbox__content .loadergif { 
    text-align: center; 
    width: 150px; 
    height: auto;
    margin: 0 auto; 
    display: inherit; 
    border-bottom: 20px solid white;
}

.subcat-filter-wrapper.ignorethis {
    display: none;
}

.export-month {
    margin-bottom: 20px;
    width: 100%;
    padding: 20px;
}



/*------------------------------------*\
#CUSTOM LIGHTBOX
\*------------------------------------*/

.custom-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
}

.custom-lightbox__background {
    background-color: black;
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: .8;
}

.custom-lightbox__container {
    padding-top: 30px;
    width: 600px;
    position: absolute;
    z-index: 1;
    left: 50%;
    top: 50%;
    max-height: 95%;
    max-width: 95%;
    /*@include transform(translateY(-50%) translateX(-50%));*/
    -webkit-transform: translateY(-50%) translateX(-50%);
    -ms-transform: translateY(-50%) translateX(-50%);
    transform: translateY(-50%) translateX(-50%);
}

.custom-lighbox__content {
    background-color: white;
    padding: 30px;
    overflow-y: auto;
    width: 100%;
}

.custom-lighbox__content.is_calendar {
    position: relative;
}

.custom-lightbox__close {
//    cursor: pointer;
//    font-style: normal !important;
//    font-size: 22px;
//    position: absolute;
//    right: 10px;
//    top: 5px;
//    color: #c9a205;
}

.cal-content p {
    color: #77777A !important;
    font-size: 1.3125em !important;
    line-height: 2.9rem !important;
    font-family: IntervalProLight, monospace !important;
    margin-bottom: 20px !important;
}

.cal-content > h3 {
    font-size: 24px;
    color: $secondary;
}

.cal-content .tribe-events-meta-group {
    border-top: 1px solid #ECECEC;
    padding-top: 20px;
}

.cal-content .tribe-events-meta-group h3 {
    font-size: 19px;
    color: #112b53;
}

.cal-content .tribe-venue {
    margin: 0;
}

.time-event {
    color: $text-grey;
}

.ajax-events.caltpl {
    img {
        width: 300px!important;
    }
}


