import gsap from 'gsap';


		
function departmentstaff() {	
    var mqdep = window.matchMedia('(min-width: 1024px)');
$('.get_popup_staff_inside').on( 'click', function(e) {
    e.preventDefault();
    var postid = $(this).attr('data-postid');
    $('#boxcontent_staff').mouseup(function (e) {
        var container = $(".boxpop > div");
        if(!container.is(e.target) &&
        container.has(e.target).length === 0) {
            let tl = gsap.timeline();
            if (mqdep.matches) {
                tl.to('#boxcontent_staff > div', { right:'-55vw', duration:.5})
                .to('#boxcontent_staff', {autoAlpha:0, duration:.3});
            } else {
                tl.to('#boxcontent_staff > div', { right:'-100vw', duration:.5})
                .to('#boxcontent_staff', {autoAlpha:0, duration:.3});
            }
        }
    });
    
    $.ajax({
        type: "GET",
            url: my_ajax_object.ajax_url,
            dataType : 'html',
            data: {
            action: 'my_load_ajax_content_staff_inside',
            idpost: postid,
            }
    }).done(function (data) {
        // Just simple html() method with data will show all the content.
        $('#boxcontent_staff').html(data);
        gsap.to('#boxcontent_staff', {autoAlpha:1, duration:.3});
        gsap.from('#boxcontent_staff > div', { right:'-50vw', duration:.6, delay:.4})
        $(document).on("click", '[data-element="close_popup_staff"]', function(event) { 
            let tl = gsap.timeline();
            if (mqdep.matches) {
                tl.to('#boxcontent_staff > div', { right:'-55vw', duration:.5})
                .to('#boxcontent_staff', {autoAlpha:0, duration:.3});
            } else {
                tl.to('#boxcontent_staff > div', { right:'-100vw', duration:.5})
                .to('#boxcontent_staff', {autoAlpha:0, duration:.3});
            }
        });
       
    });
});
}

export default departmentstaff;