<?php
/**
 * Template part to display the block "Video Gallery".
 *
 * @package twkmedia
 */

?>

<?php if( have_rows('gallery') ): ?>
<div class="swiper my-10" data-element="video_slider">
    <div class="swiper-wrapper pb-4">
    <?php $count=0; while( have_rows('gallery') ): the_row();         ?>
        <div class="swiper-slide">
            <div class="lg:p-3"> 
                <div class="relative 
                after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-y-1/2 after:-translate-x-1/2 after:w-[56px] after:h-[56px] lg:after:w-[74px] lg:after:h-[74px] after:bg-play-icon-red after:bg-no-repeat after:bg-contain after:z-20 after:cursor-pointer
                before:content-[''] before:absolute before:bottom-0 before:left-0 before:w-full before:h-[33%] before:bg-gradient-to-b before:from-black/0 before:to-black/50">
                <a href="<?php echo get_sub_field('video');?>" class="magnific-video absolute h-full w-full inset-0 z-30"></a>
                <?php 
                    $video = get_sub_field('cover_image');
                    if( !empty( $video ) ): ?>
                        <img class="w-full aspect-[16/9] object-cover" src="<?php echo esc_url($video['url']); ?>" alt="<?php echo esc_attr($video['alt']); ?>" />
                <?php endif; ?>
                    
                </div>
            
            </div>
        </div>
        <?php $count++; endwhile; ?>
    </div>
    <div class="swiper-pagination"></div>
    <div class="container w-full flex lg:justify-between children:bg-pagination-previous-navy children:w-[60px] children:h-[60px] children:lg:w-[65px] children:lg:h-[65px] children:bg-no-repeat children:bg-contain children:indent-[9999px] children:cursor-pointer rotate-3 lg:rotate-0">
        <div class="swiper-button-prev mr-4"></div>
        <div class="swiper-button-next rotate-180"></div>
    </div>
</div>

<?php endif; ?>
