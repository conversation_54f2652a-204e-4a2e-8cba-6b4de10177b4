<?php
/**
 * Template Name: Podcast
 *
 * @package twkmedia
 */

get_header();

if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();
		?>

		<?php include locate_template('src/components/banner/banner.php'); ?>
		
		<div class="container">

			
			
			<div class="container bg-blue py-2 px-10 mb-12 -mt-14 lg:mt-0">
				<div class="flex justify-between  flex-wrap flex-col-reverse lg:flex-row items-center lg:items-center ">
					<div class="flex items-center justify-start"><span  class="hidden lg:block font-aspectbold text-[16px] uppercase text-white mr-4">Filter:</span><?php echo do_shortcode('[searchandfilter id="76059"]'); ?></div>
				</div>
			</div>



			<div class="container px-10 lg:px-0">
				<?php echo do_shortcode('[searchandfilter id="76059" show="results"]'); ?>
			</div>



				


		</div>
		
		
		<div id="boxcontent" class="boxpop fixed inset-0 bg-black/50 h-screen w-screen z-[60] invisible opacity-0"></div>

		<div class="lg:mt-32">
			<?php include locate_template('src/components/where_next/where_next.php'); ?>
		</div>

		<?php
	endwhile;
endif;

get_footer();
