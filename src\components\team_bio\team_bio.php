<?php
/**
 * Template part to display the block "Team Bio".
 *
 * @package twkmedia
 */

?>

<h4 class="font-aspectbold text-[28px] leading-[36px] tracking-[-0.75px] lg:text-titleh4 uppercase mb-6 mt-8"><?php echo get_sub_field('title');?></h4>






<?php
$featured_posts = get_sub_field('select_staff');
if( $featured_posts ): ?>
    <?php foreach( $featured_posts as $featured_post ): 
        $permalink = get_permalink( $featured_post->ID );
        $title = get_the_title( $featured_post->ID );
        $the_post_thumbnail_url  = get_the_post_thumbnail_url($featured_post->ID);
        $staff_leadjobtitle = get_field( 'staff_leadjobtitle', $featured_post->ID );
        ?>
        <div class="bg-blue lg:grid grid-cols-4 lg:min-h-[270px] w-full mb-6">
            <div class="relative hidden lg:block h-full col-span-2 xl:col-span-1">
                <?php if($the_post_thumbnail_url): ?>
                <img class="absolute inset-0 w-full h-full object-cover" src="<?= $the_post_thumbnail_url; ?>" alt="<?= $title; ?>" />
                <?php else: ?>
                    <img class="absolute inset-0 w-full h-full object-cover" src="<?php the_field('staff_fallback_image', 'options'); ?>" alt="<?= $title; ?>" />
                <?php endif; ?>
            </div>
            <div class="col-span-2 xl:col-span-3 py-12 px-9 pr-3 lg:flex justify-between flex-col">
                <div>
                    <h3 class="text-[32px] leading-[32px] uppercase text-white "><?= $title; ?></h3>
                    <p class="text-copy16 text-white mt-1"><?= $staff_leadjobtitle; ?></p>
                </div>
                <div>
                    <a class="get_popup_staff_inside button after:bg-bio-icon-navy after:h-4 duration-300 ease-in-out
                                 hover:bg-[#cca026] hover:after:translate-x-0 mt-4 xl:mt-0" href="<?= $permalink; ?>" target="_self" data-postid="<?php echo $featured_post->ID; ?>">View bio</a>
                </div>
            </div>
           
            
        </div>


    <?php endforeach; ?>
<?php endif; ?>



