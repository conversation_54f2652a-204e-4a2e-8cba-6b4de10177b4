<?php
/**
 * Template part to display the block "Blockquote".
 *
 * @package twkmedia
 */

?>


<?php if(get_sub_field('quote')):?>
<figure  class="bg-heathlandgreen mb-10 lg:mb-12 py-8 px-10 lg:px-20 -mx-4 lg:mx-0">
  <blockquote>
    <p class="font-aspectregular text-copy19 leading-[28px] text-white uppercase"><?php the_sub_field('quote');?></p>
  </blockquote>
  <?php if(get_sub_field('name')):?><figcaption class="font-aspectregular text-copy16 text-white uppercase mt-4">— <?php the_sub_field('name');?></figcaption><?php endif;?>
</figure>
<?php endif; ?>

<?php if( have_rows('quote_carousel') ): ?>
    <div class="relative bg-heathlandgreen mb-10 lg:mb-12 
    after:content-[''] after:absolute after:left-10 after:top-10 after:w-[54px] after:h-[51px] after:bg-contain after:bg-no-repeat after:bg-quote-mark-white">
      <div class="swiper " data-element="quote_carousel">
          <div class="swiper-wrapper">
          <?php while( have_rows('quote_carousel') ): the_row();?>
              <div class="swiper-slide py-8 px-10 lg:pl-[120px] lg:pr-[160px]">
                <figure>
                  <blockquote>
                    <p class="font-aspectregular text-[24px] leading-[31px] text-white uppercase mt-24 lg:mt-0"><?php the_sub_field('quote');?></p>
                  </blockquote>
                  <?php if(get_sub_field('name')):?><figcaption class="font-aspectregular text-copy16 text-white uppercase mt-4">— <?php the_sub_field('name');?></figcaption><?php endif;?>
                </figure>
              </div>
          <?php endwhile; ?>
          </div>
          <div class="swiper-pagination"></div>
      </div>
    </div>
<?php endif; ?>


