{"version": 3, "sources": ["webpack:///./assets/sass/C:/laragon/www/wp-base/wp-content/themes/base-theme/assets/sass/admin-style.scss", "webpack:///./assets/sass/C:/laragon/www/wp-base/wp-content/themes/base-theme/assets/sass/C:/laragon/www/wp-base/wp-content/themes/base-theme/assets/sass/varmix/_color.scss", "webpack:///./assets/sass/C:/laragon/www/wp-base/wp-content/themes/base-theme/assets/sass/C:/laragon/www/wp-base/wp-content/themes/base-theme/assets/sass/admin-style.scss"], "names": [], "mappings": "AAAA,iBAAgB;ACsBf;EARA,uBAVa;CDDd;;ACoBC;EALA,YAda;CDGd;;ACeC;EARA,uBAVa;CDOd;;ACYC;EALA,YAda;CDWd;;ACOC;EARA,0BAVa;CDed;;ACIC;EALA,eAda;CDmBd;;ACDC;EARA,0BAVa;CDuBd;;ACJC;EALA,eAda;CD2Bd;;ACLA;EACI;CDQJ;;AE/BA;;EAAA;AAGA;EACC;CFkCD;;AE9BA;;EAAA;AAGA;EACI;CFiCJ;;AE3BA;;EAAA;AAIC;EACC;CF6BF;;AEvBA;;EAAA;AAIA;AACA;EACI;EACA;EACA;EACA;EACH;EACA;CFyBD;AEvBC;EACC;EACA;CFyBF;AEvBC;EACC,0BDlDQ;CD2EV;AEvBC;EACC,0BDpDU;CD6EZ;;AErBA;AACA;EACC;EACG;EACH;EACA;EACG;CFwBJ;AEtBC;EACC;CFwBF;AEtBC;EACC;CFwBF;AEtBC;EACC;CFwBF;;AEpBA;AACA;EACC;EACG;EACH;EACA;EACG;CFuBJ;AErBC;EACC;CFuBF;AErBC;EACC;CFuBF;;AEnBA;AACA;EACC;EACG;EACH;EACA;EACG;CFsBJ;AEpBC;EACC;CFsBF;AEpBC;EACC;CFsBF;AEpBC;EACC;CFsBF;AEpBC;EACC", "file": "assets/css/admin-style.css", "sourcesContent": ["@charset \"UTF-8\";\n.background-black {\n  background-color: #000;\n}\n\n.color-black {\n  color: #000;\n}\n\n.background-white {\n  background-color: #fff;\n}\n\n.color-white {\n  color: #fff;\n}\n\n.background-primary {\n  background-color: #13212e;\n}\n\n.color-primary {\n  color: #13212e;\n}\n\n.background-secondary {\n  background-color: #05E5C8;\n}\n\n.color-secondary {\n  color: #05E5C8;\n}\n\n.color-inherit {\n  color: inherit !important;\n}\n\n/*\n\tMenu bar\n*/\n#wp-admin-bar-wp-logo > .ab-item {\n  pointer-events: none;\n}\n\n/*\n\tAt a glance widget\n*/\n#dashboard_right_now .code-snippet-count a:before {\n  content: \"\";\n}\n\n/*\n\tShow a field only for the TWK media team\n*/\nbody:not(.user-id-1) .twk-only {\n  display: none;\n}\n\n/*\n\tACF Radio Colors\n*/\n/* primary : <span class=\"color-choice color-choice--primary\" title=\"Primary\">Primary</span> */\n.color-choice {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  font-size: 0;\n  margin: 0px 5px 5px;\n  border: 1px solid transparent;\n}\n.color-choice--transparent {\n  background-color: transparent;\n  border-color: black;\n}\n.color-choice--primary {\n  background-color: #13212e;\n}\n.color-choice--secondary {\n  background-color: #05E5C8;\n}\n\n/* left : <span class=\"alignment-choice alignment-choice--left\" title=\"Left\">Left</span> */\n.alignment-choice {\n  display: inline-block;\n  vertical-align: middle;\n  width: 24px;\n  height: 24px;\n  font-size: 0;\n}\n.alignment-choice--left {\n  background: url(\"../img/admin/align-left.svg\") no-repeat;\n}\n.alignment-choice--right {\n  background: url(\"../img/admin/align-right.svg\") no-repeat;\n}\n.alignment-choice--center {\n  background: url(\"../img/admin/align-center.svg\") no-repeat;\n}\n\n/* video : <span class=\"media-choice media-choice--video\" title=\"Video\">Video</span> */\n.media-choice {\n  display: inline-block;\n  vertical-align: middle;\n  width: 24px;\n  height: 24px;\n  font-size: 0;\n}\n.media-choice--picture {\n  background: url(\"../img/admin/picture.svg\") no-repeat;\n}\n.media-choice--video {\n  background: url(\"../img/admin/video.svg\") no-repeat;\n}\n\n/* full : <span class=\"border-choice border-choice--full\" title=\"Full\">Full</span> */\n.border-choice {\n  display: inline-block;\n  vertical-align: middle;\n  width: 24px;\n  height: 24px;\n  font-size: 0;\n}\n.border-choice--full {\n  background: url(\"../img/admin/border-full.svg\") no-repeat;\n}\n.border-choice--center {\n  background: url(\"../img/admin/border-center.svg\") no-repeat;\n}\n.border-choice--top {\n  background: url(\"../img/admin/border-top.svg\") no-repeat;\n}\n.border-choice--bottom {\n  background: url(\"../img/admin/border-bottom.svg\") no-repeat;\n}", "// Colors\r\n$primary: #13212e;    // Replace with primary color variable. ex. $blue\r\n$secondary: #05E5C8;  // Replace with secondary color variable. ex. $red\r\n\r\n$color_list : (\r\n\t'black': #000,\r\n\t'white': #fff,\r\n\t'primary': $primary,\r\n\t'secondary': $secondary,\r\n);\r\n\r\n\r\n// This mixin will generate background-color property for the colors in our list\r\n@mixin background-color($args) {\r\n\tbackground-color: $args;\r\n}\r\n// This mixin will generate color property for the colors in our list\r\n@mixin inner-color($args) {\r\n\tcolor: $args;\r\n}\r\n\r\n@each $name,$color in $color_list {\r\n\t.background-#{$name} { @include background-color($color); }\r\n\t.color-#{$name} { @include inner-color($color); }\r\n}\r\n\r\n.color-inherit {\r\n    color: inherit !important;\r\n}\r\n", "// Admin styles.\r\n@import \"varmix/color\";\r\n\r\n\r\n/*\r\n\tMenu bar\r\n*/\r\n#wp-admin-bar-wp-logo > .ab-item{\r\n\tpointer-events: none;\r\n}\r\n\r\n\r\n/*\r\n\tAt a glance widget\r\n*/\r\n#dashboard_right_now .code-snippet-count a:before {\r\n    content: \"\\f475\";\r\n}\r\n\r\n\r\n\r\n\r\n/*\r\n\tShow a field only for the TWK media team\r\n*/\r\nbody:not(.user-id-1){\r\n\t.twk-only{\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\n\r\n\r\n/*\r\n\tACF Radio Colors\r\n*/\r\n\r\n/* primary : <span class=\"color-choice color-choice--primary\" title=\"Primary\">Primary</span> */\r\n.color-choice {\r\n    display: inline-block;\r\n    width: 20px;\r\n    height: 20px;\r\n    font-size: 0;\r\n\tmargin: 0px 5px 5px;\r\n\tborder: 1px solid transparent;\r\n\r\n\t&--transparent{\r\n\t\tbackground-color: transparent;\r\n\t\tborder-color: black;\r\n\t}\r\n\t&--primary{\r\n\t\tbackground-color: $primary;\r\n\t}\r\n\t&--secondary{\r\n\t\tbackground-color: $secondary;\r\n\t}\r\n}\r\n\r\n/* left : <span class=\"alignment-choice alignment-choice--left\" title=\"Left\">Left</span> */\r\n.alignment-choice{\r\n\tdisplay: inline-block;\r\n    vertical-align: middle;\r\n\twidth: 24px;\r\n\theight: 24px;\r\n    font-size: 0;\r\n\r\n\t&--left{\r\n\t\tbackground: url('../img/admin/align-left.svg') no-repeat;\r\n\t}\r\n\t&--right{\r\n\t\tbackground: url('../img/admin/align-right.svg') no-repeat;\r\n\t}\r\n\t&--center{\r\n\t\tbackground: url('../img/admin/align-center.svg') no-repeat;\r\n\t}\r\n}\r\n\r\n/* video : <span class=\"media-choice media-choice--video\" title=\"Video\">Video</span> */\r\n.media-choice{\r\n\tdisplay: inline-block;\r\n    vertical-align: middle;\r\n\twidth: 24px;\r\n\theight: 24px;\r\n    font-size: 0;\r\n\r\n\t&--picture{\r\n\t\tbackground: url('../img/admin/picture.svg') no-repeat;\r\n\t}\r\n\t&--video{\r\n\t\tbackground: url('../img/admin/video.svg') no-repeat;\r\n\t}\r\n}\r\n\r\n/* full : <span class=\"border-choice border-choice--full\" title=\"Full\">Full</span> */\r\n.border-choice{\r\n\tdisplay: inline-block;\r\n    vertical-align: middle;\r\n\twidth: 24px;\r\n\theight: 24px;\r\n    font-size: 0;\r\n\r\n\t&--full{\r\n\t\tbackground: url('../img/admin/border-full.svg') no-repeat;\r\n\t}\r\n\t&--center{\r\n\t\tbackground: url('../img/admin/border-center.svg') no-repeat;\r\n\t}\r\n\t&--top{\r\n\t\tbackground: url('../img/admin/border-top.svg') no-repeat;\r\n\t}\r\n\t&--bottom{\r\n\t\tbackground: url('../img/admin/border-bottom.svg') no-repeat;\r\n\t}\r\n}\r\n\r\n"], "sourceRoot": ""}