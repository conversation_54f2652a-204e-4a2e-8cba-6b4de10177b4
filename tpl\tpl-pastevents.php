<?php
/**
 * Template Name: Past Events
 *
 * @package twkmedia
 */

get_header();

if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();
		?>

		<?php include locate_template('src/components/banner/banner.php'); ?>
		
		<div class="container">

            <div class="container bg-blue py-2 px-10 mb-12">
				<div class="flex justify-between  flex-wrap flex-col-reverse lg:flex-row items-end lg:items-center ">
					<div><?php echo do_shortcode('[searchandfilter id="68701"]'); ?></div>
                    <div class="flex justify-between items-center border border-white rounded-[33px] px-[5px] py-[5px] children:font-aspectbold children:text-[12px] children:rounded-[33px] children:uppercase children:py-[0px] children:px-3 ">
                        <?php 
                        $link = get_field('upcoming_events','options');
                        if( $link ): 
                            $link_url = $link['url'];
                            $link_title = $link['title'];
                            $link_target = $link['target'] ? $link['target'] : '_self';
                            ?>
                            <a class="opacity-60 text-white"href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
                        <?php endif; ?>
                        <?php 
                        $link = get_field('past_events','options');
                        if( $link ): 
                            $link_url = $link['url'];
                            $link_title = $link['title'];
                            $link_target = $link['target'] ? $link['target'] : '_self';
                            ?>
                            <a class="opacity-100 text-blue bg-cranleighclay"href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
                        <?php endif; ?>
                    </div>
				</div>
			</div>
			
	

			<div class="container px-10 lg:px-0">
				<?php echo do_shortcode('[searchandfilter id="68701" show="results"]'); ?>
			</div>



				


		</div>
		
		
		<div id="boxcontent" class="boxpop fixed inset-0 bg-black/50 h-screen w-screen z-[60] invisible opacity-0"></div>

		<div class="lg:mt-32">
			<?php include locate_template('src/components/where_next/where_next.php'); ?>
		</div>

		<?php
	endwhile;
endif;

get_footer();
