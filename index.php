<?php
/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no home.php file exists.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package twkmedia
 */

get_header();

if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();
		?>
		<div class="container base-theme-padding">
			<div class="row">
				<div class="col-md-6 offset-md-3">
					<?php the_content(); ?>
				</div>
			</div>
		</div>
		<?php
	endwhile;
endif;

get_footer();

