.overlay--container{
    position: relative;
    z-index: 0;
}

.overlay--content-over{
    position: relative;
    z-index: 1;
}

[class^=overlay]{
    &.overlay--light{
        opacity: .5;
    }
    &.overlay--lighter{
        opacity: .25;
    }

    &.overlay--dark{
        opacity: .75;
    }
    &.overlay--darker{
        opacity: .9;
    }
}

.overlay--full{
    position: absolute;
    top: 0; bottom: 0;
    left: 0; right: 0;
    background-color: black;
	opacity: .7;
}

.overlay--top{
    position: absolute;
    top: 0;
    left: 0; right: 0;
    height: 50%;
    opacity: .7;
    background: linear-gradient(180deg, #000000 0%, rgba(0,0,0,0) 100%);
}

.overlay--bottom{
    position: absolute;
    bottom: 0;
    left: 0; right: 0;
    height: 50%;
    opacity: .7;
	background: linear-gradient(180deg, rgba(0,0,0,0) 0%, #000000 100%);
}

.overlay--left{
    position: absolute;
    top: 0; bottom: 0;
    left: 0; right: 40%;
    opacity: .7;
	background: linear-gradient(90deg, #000000  0%, rgba(0,0,0,0) 100%);
	
	@media( max-width: 767px ) {
		right: 0;
	}
}
