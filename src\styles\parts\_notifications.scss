/*
	NOTIFICATIONS
*/
.notification.cookies-bar {
    position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 100;
	background: $primary;
	padding: 30px 50px 10px;
	color: white;
	
	.notification__content{
		display: flex;
		justify-content: space-between;
		align-items: center;

		.notification__text{
			max-width: 75%;
			margin-right: 40px;
			
			p {
				//font-family: $serif;
				font-size: 15px;
				line-height: 20px;
			}
		}
		a{
			color: white;

			&:not(.button):hover, &:not(.button):focus{
				text-decoration: underline;
			}
		}

		.buttons {
			display: flex;
			align-items: center;

			.button{
				padding: 14px 30px;
				font-size: 11px;
			}
		}
	}
	@media( max-width: 991px ){
		.buttons{
			display: block;
			margin-top: 30px;

			.button{
				margin-bottom: 15px;
			}
		}
	}
	@media( max-width: 575px ){
		padding: 30px 30px 5px;

		.notification__content{
			display: block;

			.notification__text{
				margin-right: 0px;
			}
		}
		.buttons{
			.button{
				margin-left: 0 !important;
				margin-right: 30px;
			}
		}
	}
}



.notification.popup{
	position: fixed;
	z-index: 90;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background-color: $primary;
	padding: 50px;

	.close-button {
		position: absolute;
		top: 15px;
		right: 15px;
		cursor: pointer;
	}
}





// Custom toggle
.switch {
	position: relative;
	display: inline-block;
	height: 34px;
	width: 60px;
}

.switch input {
	display:none;
}

.slider {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ccc;
	cursor: pointer;
	transition: .4s;
}

.slider:before {
	content: "";
	position: absolute;
	left: 4px;
	bottom: 4px;
	width: 26px;
	height: 26px;
	background-color: #fff;
	transition: .4s;
}

input:checked + .slider {
	background-color: #66bb6a;
}

input:checked + .slider:before {
	transform: translateX(26px);
}

.slider.round {
	border-radius: 34px;
}

.slider.round:before {
	border-radius: 50%;
}


input:disabled + .slider {
    opacity: 0.57;
    cursor: not-allowed;
}
