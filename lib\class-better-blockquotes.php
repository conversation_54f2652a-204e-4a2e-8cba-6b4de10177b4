<?php
/**
 * Better Blockquotes Class
 *
 * @package twkmedia
 */

/**
 * Better Blockquotes
 *
 * @since 1.0.0
 */
class Better_Blockquotes {

	const VERSION = '1.0.0';

	/**
	 * Constructor
	 *
	 * @return void
	 */
	public function __construct() {
		add_action( 'init', array( $this, 'init' ) );

	}

	/**
	 * Initialize the plugin
	 *
	 * @return void
	 */
	public function init() {

		// Setup internationalization.
		$this->i18n();

		// Translations used by javascript loaded with this plugin.
		add_action( 'admin_enqueue_scripts', array( $this, 'localize_script' ) );

		// Loads the buttons if user has permissions.
		add_action( 'admin_head', array( $this, 'tinymce_button_init' ) );
	}

	/**
	 * Internationalization setup
	 *
	 * @return void
	 */
	public function i18n() {
		$domain = 'better-blockquotes';
		$locale = apply_filters( 'plugin_locale', get_locale(), $domain );
		load_textdomain( $domain, WP_LANG_DIR . "/tinymce-email-button/$domain-$locale.mo" );
		load_plugin_textdomain( $domain, false, dirname( plugin_basename( __FILE__ ) ) . '/languages/' );
	}

	/**
	 * Localization for the tinymce-email-button.js file
	 *
	 * @return void
	 */
	public function localize_script() {

		wp_localize_script(
			'editor',
			'better_blockquotes',
			array(
				'add_blockquote' => __( 'Add Blockquote', 'better-blockquotes' ),
				'blockquote'     => __( 'Blockquote', 'better-blockquotes' ),
				'quote'          => __( 'Quote', 'better-blockquotes' ),
				'citation'       => __( 'Citation', 'better-blockquotes' ),
				'citation_link'  => __( 'Citation Link', 'better-blockquotes' ),
				'class'          => __( 'Class', 'better-blockquotes' ),
				'class_options'  => apply_filters( 'betterblockquotes_classes', false ),
			)
		);

	}

	/**
	 * TinyMCE Button Init
	 *
	 * @return void
	 */
	public function tinymce_button_init() {

		// Exit if user can't edit posts.
		if ( ! current_user_can( 'edit_posts' ) && ! current_user_can( 'edit_pages' ) ) {
			return;
		}

		// Exit if rich editing is not enable.
		if ( 'true' !== get_user_option( 'rich_editing' ) ) {
			return;
		}

		add_filter( 'mce_buttons', array( $this, 'register_tinymce_button' ) );
		add_filter( 'mce_external_plugins', array( $this, 'add_tinymce_plugin' ) );

	}

	/**
	 * Loads the javascript required for the custom TinyMCE button
	 *
	 * @param array $plugin_array plugin_array.
	 * @return array TinyMCE buttons
	 */
	public function add_tinymce_plugin( $plugin_array ) {

		$plugin_array['better_blockquote'] = get_template_directory_uri() . '/assets/js/vendor/blockquotes.js';
		return $plugin_array;

	}

	/**
	 * Adds the button to TinyMCE
	 *
	 * @param array $buttons Buttons.
	 * @since 1.0.0
	 */
	public function register_tinymce_button( $buttons ) {

		// Removes the default blockquote button.
		if ( false !== ( $key = array_search( 'blockquote', $buttons ) ) ) {
			unset( $buttons[ $key ] );
		}

		// Add the custom blockquotes button.
		array_push( $buttons, 'better_blockquote' );

		return $buttons;
	}

}

new Better_Blockquotes();
