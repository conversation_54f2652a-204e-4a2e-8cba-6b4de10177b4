import gsap from 'gsap';
import Swiper, { Navigation, Pagination, Scrollbar, Autoplay, FreeMode } from 'swiper';
import ScrollTrigger from "gsap/ScrollTrigger";
gsap.registerPlugin(ScrollTrigger);

function homepage() {    
    //console.log('newhome')
    var mq = window.matchMedia('(min-width: 1024px)')

    // Color mapping from old names to new semantic names
    const colorMap = {
        'red': 'weyblue',
        'yellow': 'cranleighclay',
        'blue': 'blue',  // blue stays the same
        'weyblue': 'weyblue',
        'cranleighclay': 'cranleighclay'
    };

    function mapColor(color) {
        return colorMap[color] || color;
    }

    gsap.to([ "[data-target='home_copy'] *"], {autoAlpha:1, duration:1, delay:1.5 })
    gsap.to(["[data-target='video_home']"], {autoAlpha:1, duration:1,delay:.5 });
    gsap.from("[data-target='home_strip']", {height: "100%", stagger:.2, duration:1, delay:1})    

    if (mq.matches) {
    let navigationpin =  gsap.to("[data-js='navigation']",  {
      autoAlpha:1,
      scrollTrigger: {
        trigger: "[data-element='home_banner']",
        pin: "[data-js='navigation']",
        scrub: 2,
        start: "top top",
      }
    });
    }

    let st = ScrollTrigger.create({
      trigger: "[data-element='home_slide_wrap']",
      pin: "[data-element='home_slide_img_wrap']",
      start: "top top",
      end: "bottom bottom"
    });

    const imgtotal = $( "[data-element='home_slide_img']").length;
    const databg1 =  $( "[data-element='home_slide_bg'][data-count='1'" ).data('bg');
    const mappedBg1 = mapColor(databg1);
    gsap.set($( "[data-element='home_slide_bg']" ).parent().parent(), {className: "duration-300 bg-"+mappedBg1+""})
    $( "[data-element='home_slide_bg']" ).each(function( index ) {
      const slide = $(this);
      const slideparent = $(this).parent().parent();
      const bg = $(this).data('bg');
      const mappedBg = mapColor(bg);
      const imgcount = slide.attr('data-count');
      const img = $( "[data-element='home_slide_img'][data-count='" + imgcount + "']" );
      const widthmenu = $( "[data-element='home_slide_menu'][data-count='" + imgcount + "']" ).width();
      const leftmenu = $( "[data-element='home_slide_menu'][data-count='" + imgcount + "']" ).offset().left - $( "[data-element='home_slide_menu_wrap']" ).offset().left + 8
      
      const bordercolor =  $( "[data-element='home_slide_menu'][data-count='" + imgcount + "']" ).data('border');
      const mappedBorderColor = mapColor(bordercolor);
      
      gsap.to(slideparent, {
        scrollTrigger: {
          trigger: slide,
          start: "center bottom", 
          end:"+="+""+$(this).height(),
          toggleActions: "play none none reverse",
          onEnter: function(){ 
            gsap.set(slideparent, {className: "duration-300 bg-"+mappedBg+"",})
            gsap.set($("[data-element='home_slide_menu_line']"), {className: "h-[5px] absolute bottom-0 duration-300 bg-"+mappedBorderColor+"",})
            
            $("[data-element='home_slide_menu_line']").css('width', widthmenu )
            $("[data-element='home_slide_menu_line']").css('left', (leftmenu) )
           
           },
           onEnterBack: function(){ 
            gsap.set(slideparent, {className: "duration-300 bg-"+mappedBg+"",})
            gsap.set($("[data-element='home_slide_menu_line']"), {className: "h-[5px] absolute bottom-0 duration-300 bg-"+mappedBorderColor+"",})
            $("[data-element='home_slide_menu_line']").css('width', widthmenu )
            $("[data-element='home_slide_menu_line']").css('left', (leftmenu) )
           },
        }
        
        
      });
      if(imgcount < imgtotal) {
        gsap.to(img, {
          scrollTrigger: {
            trigger: slide,
            start: "bottom 60%",
            end:300,
            toggleActions: "play none none reverse",
          },
          y:"-100vh"
        });
      }
    });
    
    gsap.set("[data-element='sliding_image_lr']", {transformOrigin: "right bottom", rotation: -4})
    const slidelr_number = $("[data-element='sliding_image_lr'] picture").length;
    gsap.set("[data-element='sliding_image_lr']", {xPercent: -100,y: 0});  
   
   //slider stats
    var swiper = new Swiper("[data-element='stats_home']", {
        modules: [Navigation, Pagination, Scrollbar],
        loop: true,
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        on: {
          click() {
            swiper.slideNext()
          },
        },
      });
    if($("[data-element='stats_home'] .swiper-slide").length == 1) {
        $("[data-element='stats_home']").addClass( "disabled" );
    }

    let sections = gsap.utils.toArray("[data-anim='fade']");
    sections.forEach((section) => {

      gsap.from(section,{
        scrollTrigger: {
          trigger: section,
          fastScrollEnd: true,
          once: true,
          start: "top bottom-=100px",
          end: "bottom 80%", 
        },
        duration: 1,
        ease: "power1.out",
        autoAlpha:0,
        y:"+=80px"
      });
      

    }); 
    gsap.set($("[data-anim='grid_gallery'] picture:nth-child(1)"), {y:"-=50px",x:"-=50px",});
    gsap.set($("[data-anim='grid_gallery'] picture:nth-child(2)"), {y:"-=50px",x:"+=50px",});
    gsap.set($("[data-anim='grid_gallery'] picture:nth-child(3)"), {y:"+=50px",x:"-=50px",});
    gsap.set($("[data-anim='grid_gallery'] picture:nth-child(4)"), {y:"+=50px",x:"+=50px",});
    gsap.to($("[data-anim='grid_gallery'] picture"),{
      scrollTrigger: {
        trigger: $("[data-anim='grid_gallery']"),
        fastScrollEnd: true,
        once:true,
        start: "top 80%",
        end: "50% 50%", 
      },
      duration: 1,
      ease: "power1.out",
      autoAlpha:1,
      y:"0",
      x:"0",
    });
}

export default homepage;