// Admin styles.
@import "varmix/color";


/*
	Menu bar
*/
#wp-admin-bar-wp-logo > .ab-item{
	pointer-events: none;
}


/*
	At a glance widget
*/
#dashboard_right_now .code-snippet-count a:before {
    content: "\f475";
}




/*
	Show a field only for the TWK media team
*/
body:not(.user-id-1){
	.twk-only{
		display: none;
	}
}



/*
	ACF Radio Colors
*/

/* primary : <span class="color-choice color-choice--primary" title="Primary">Primary</span> */
.color-choice {
    display: inline-block;
    width: 20px;
    height: 20px;
    font-size: 0;
	margin: 0px 5px 5px;
	border: 1px solid transparent;

	&--transparent{
		background-color: transparent;
		border-color: black;
	}
	&--primary{
		background-color: $primary;
	}
	&--secondary{
		background-color: $secondary;
	}
}

/* left : <span class="alignment-choice alignment-choice--left" title="Left">Left</span> */
.alignment-choice{
	display: inline-block;
    vertical-align: middle;
	width: 24px;
	height: 24px;
    font-size: 0;

	&--left{
		background: url('../img/admin/align-left.svg') no-repeat;
	}
	&--right{
		background: url('../img/admin/align-right.svg') no-repeat;
	}
	&--center{
		background: url('../img/admin/align-center.svg') no-repeat;
	}
}

/* video : <span class="media-choice media-choice--video" title="Video">Video</span> */
.media-choice{
	display: inline-block;
    vertical-align: middle;
	width: 24px;
	height: 24px;
    font-size: 0;

	&--picture{
		background: url('../img/admin/picture.svg') no-repeat;
	}
	&--video{
		background: url('../img/admin/video.svg') no-repeat;
	}
}

/* full : <span class="border-choice border-choice--full" title="Full">Full</span> */
.border-choice{
	display: inline-block;
    vertical-align: middle;
	width: 24px;
	height: 24px;
    font-size: 0;

	&--full{
		background: url('../img/admin/border-full.svg') no-repeat;
	}
	&--center{
		background: url('../img/admin/border-center.svg') no-repeat;
	}
	&--top{
		background: url('../img/admin/border-top.svg') no-repeat;
	}
	&--bottom{
		background: url('../img/admin/border-bottom.svg') no-repeat;
	}
}

