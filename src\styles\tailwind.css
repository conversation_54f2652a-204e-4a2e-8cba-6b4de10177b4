@import "./magnific-popup.css";
@tailwind base;
@tailwind components;
@tailwind utilities;
@import './partials/typography.css';
@import './partials/swiper.css';
@import './partials/tables.css';

@layer base {
	html {
		font-family: "proxima-nova", sans-serif;
		font-weight: 400;
		font-size: 21px;
		line-height: 32px;
		letter-spacing: normal;
		-webkit-font-smoothing: antialiased;
		@apply text-blue;
	}
}

:root {
	--swiper-theme-color: #007aff;
}

.post-edit-link {
	@apply fixed top-2 left-1/2 -translate-x-1/2 z-20 bg-weyblue border border-white ease-in duration-300 text-[11px] text-white px-12 mt-7 table py-3 uppercase rounded-[30px] hover:opacity-90;
}

@layer components {
	.button {
		@apply relative inline-table text-[15px] md:text-[17px] lg:text-cta text-white bg-blue py-[13px] md:py-[16px] lg:py-[21px] px-[28px] md:px-[35px] pr-[69px] md:pr-[75px] rounded-[33px] after:content-[''] after:absolute after:top-1/2 after:right-10 after:-translate-y-1/2 after:w-4 after:h-2.5 after:bg-arrow-right-gold after:bg-contain after:bg-center after:bg-no-repeat
        after:duration-300  after:ease-in hover:after:translate-x-5;
	}
	.button-blue {
		@apply relative table text-[15px] md:text-cta text-cranleighclay bg-blue py-[13px] md:py-[16px] lg:py-[21px] px-[28px] md:px-[35px] pr-[69px] md:pr-[75px] rounded-[33px] after:content-[''] after:absolute after:top-1/2 after:right-10 after:-translate-y-1/2 after:w-4 after:h-2.5 after:bg-arrow-right-yellow after:bg-contain after:bg-center after:bg-no-repeat
        after:duration-300  after:ease-in hover:after:translate-x-5;
	}
	.button-white {
		@apply relative table text-[15px] md:text-cta text-white border border-white py-[13px] md:py-[16px] lg:py-[21px] px-[28px] md:px-[35px] pr-[69px] md:pr-[75px] rounded-[33px] after:content-[''] after:absolute after:top-1/2 after:right-10 after:-translate-y-1/2 after:w-4 after:h-2.5 after:bg-arrow-right-white after:bg-contain after:bg-center after:bg-no-repeat
        after:duration-300  after:ease-in hover:after:translate-x-5
        duration-300 ease-in hover:bg-white hover:text-black hover:after:bg-arrow-right-gold;
	}

	.buttonpdf {
		@apply relative inline-table  text-[15px] md:text-cta text-black bg-cranleighclay py-[13px] md:py-[16px] lg:py-[21px] px-[28px] md:px-[35px] pr-[69px] md:pr-[75px] rounded-[33px] mr-4 last:mr-0 mb-4 last:mb-0 after:content-[''] after:absolute after:top-1/2 after:right-10 after:-translate-y-1/2 after:w-4 after:h-4 after:bg-pdf-icon-navy after:bg-contain after:bg-center after:bg-no-repeat
        after:duration-300  after:ease-in hover:bg-cranleighclay/70 duration-300;
	}
	.button.scroll-to {
		@apply hover:after:translate-x-0 hover:after:translate-y-0.5;
	}
}

.pdflist li {
	@apply relative list-none after:bg-[transparent] after:content-[''] after:absolute after:w-4 after:h-4 after:top-1/2 after:-translate-y-1/2 after:left-0 after:bg-pdf-icon-navy after:bg-center after:bg-contain after:bg-no-repeat pl-6;
}
.pdflist li a {
	@apply text-blue;
}

[data-element="logo_carousel"] .swiper-wrapper,
.social_carousel .swiper-wrapper {
	transition-timing-function: linear !important;
}

[data-element="testimonial_carousel"] .swiper-pagination {
	@apply absolute bottom-1.5 lg:bottom-[20px] right-[20px] z-10 flex;
}
[data-element="testimonial_carousel"] .swiper-pagination span {
	@apply w-[13px] h-[4px] bg-blue rounded-full mx-1 opacity-60 duration-300;
}
[data-element="testimonial_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
	@apply w-[26px] opacity-100 bg-weyblue;
}

.sub-menu li:first-child a {
	@apply link-container font-aspectbold uppercase text-titlesidebar leading-[44px] mb-8 table;
}
.sub-menu-1 li:first-child {
	display: none !important;
}

@media only screen and (max-width: 1023px) {
	[data-element="stats_carousel"] {
		margin-left: -16px !important;
		margin-right: -16px !important;
	}
	.sub-menu-open {
		z-index: -1 !important;
	}
	.sub-menu-open > a {
		opacity: 0 !important;
	}
	.tablepress {
		margin-bottom: 48px !important;
	}
	.gform_wrapper.gravity-theme .gfield textarea.large {
		height: 180px !important;
	}
	[data-element="vacancy_wrap"] {
		height: auto !important;
	}
	.gform_wrapper .gform_validation_errors > h2.hide_summary {
		@apply pl-12 text-left;
	}
}
[data-element="stats_carousel"].disabled .swiper-pagination {
	display: none !important;
}
[data-element="stats_carousel"].disabled .swiper-slide {
	@apply pb-8;
}
[data-element="stats_carousel"] .swiper-pagination {
	@apply absolute bottom-8 lg:bottom-[20px] left-10 lg:left-auto lg:right-[20px] z-10 flex;
}
[data-element="stats_carousel"] .swiper-pagination span {
	@apply w-[13px] h-[4px] bg-white rounded-full mx-1 opacity-60 duration-300;
}
[data-element="stats_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
	@apply w-[26px] opacity-100 bg-white;
}

[data-element="video_carousel"].disabled .swiper-pagination {
	display: none !important;
}
[data-element="video_carousel"] .swiper-pagination {
	@apply absolute bottom-[20px] right-[20px] z-10 flex;
}
[data-element="video_carousel"] .swiper-pagination span {
	@apply w-[13px] h-[4px] bg-white rounded-full mx-1 opacity-60 duration-300;
}
[data-element="video_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
	@apply w-[26px] opacity-100 bg-white;
}

[data-element="quote_carousel"].disabled .swiper-pagination {
	display: none !important;
}
[data-element="quote_carousel"] .swiper-pagination {
	@apply absolute top-10 right-10 z-10 flex;
}
[data-element="quote_carousel"] .swiper-pagination span {
	@apply w-[13px] h-[4px] bg-white rounded-full mx-1 opacity-60 duration-300;
}
[data-element="quote_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
	@apply w-[26px] opacity-100 bg-white;
}

[data-element="staff_carousel"].disabled .swiper-pagination {
	display: none !important;
}
[data-element="staff_carousel"] .swiper-pagination {
	@apply absolute bottom-0 right-0 z-10 flex;
}
[data-element="staff_carousel"] .swiper-pagination span {
	@apply w-[13px] h-[4px] bg-white rounded-full mx-1 opacity-60 duration-300;
}
[data-element="staff_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
	@apply w-[26px] opacity-100 bg-white;
}

[data-element="stats_home"].swiper {
	padding-bottom: 20px;
}
[data-element="stats_home"] .swiper-pagination {
	@apply absolute bottom-[0px] left-1/2 -translate-x-1/2 z-10 flex;
}
[data-element="stats_home"] .swiper-pagination span {
	@apply w-[13px] h-[4px] bg-white rounded-full mx-1 opacity-100 duration-300;
}
[data-element="stats_home"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
	@apply w-[26px] opacity-100 bg-cranleighclay;
}

[data-element="image_carousel"].disabled .swiper-pagination {
	display: none !important;
}
[data-element="image_carousel"] .swiper-pagination {
	@apply absolute bottom-[20px] right-[20px] z-10 flex;
}
[data-element="image_carousel"] .swiper-pagination span {
	@apply w-[13px] h-[4px] bg-white rounded-full mx-1 opacity-60 duration-300;
}
[data-element="image_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
	@apply w-[26px] opacity-100 bg-white;
}

[data-element="instagram_carousel"] .swiper-pagination {
	@apply absolute bottom-7 right-5 z-10 flex;
}
[data-element="instagram_carousel"] .swiper-pagination span {
	@apply w-[13px] h-[4px] bg-white rounded-full mx-1 opacity-60 duration-300;
}
[data-element="instagram_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
	@apply w-[26px] opacity-100 bg-white;
}


[data-element="video_slider"].disabled .swiper-pagination {
	display: none !important;
}
[data-element="video_slider"] .swiper-pagination {
	@apply absolute bottom-[20px] left-1/2 -translate-x-1/2 z-10 flex;
}
[data-element="video_slider"] .swiper-pagination span {
	@apply w-[13px] h-[4px] bg-[#0C223F] rounded-full mx-1 opacity-60 duration-300;
}
[data-element="video_slider"]
	.swiper-pagination
	span.swiper-pagination-bullet-active  {
	@apply w-[26px] opacity-100 bg-weyblue;
}

[data-state="open"] h3 {
	@apply after:bg-accordion-minus-weyblue;
}
[data-state="open"] .wysiwyg {
	@apply lg:pb-2;
}

.currentmenu {
	@apply opacity-0  w-[5px] h-5 top-0 left-0 bg-cranleighclay m-0 p-0;
	position: absolute !important;
}

[data-element="sidebar_menu"] .page_item_has_children > a {
	@apply relative pr-5 after:content-[''] after:w-[10px] after:h-[6px] after:absolute after:top-2 after:right-0 after:bg-sidebar-menu-arrow-cranleighclay after:bg-center after:bg-contain after:bg-no-repeat;
}
[data-element="sidebar_menu"] .page_item_has_children > a.actv {
	@apply after:rotate-180;
}
[data-element="sidebar_menu"] .children {
	@apply overflow-hidden children:mb-3 pl-4 text-copy16;
}
[data-element="sidebar_menu"] .children li:first-child {
	@apply mt-3;
}
[data-element="sidebar_menu"] .children li:last-child {
	@apply mb-0;
}
[data-element="sidebar_menu"]
	.page_item_has_children
	.page_item_has_children
	> a::after,
[data-element="sidebar_menu"]
	.page_item_has_children
	.page_item_has_children
	> a.actv::after {
	@apply content-none;
}
[data-element="quick_links"].actv {
	@apply after:rotate-180;
}

[data-element="where_carousel"] .swiper-button-disabled {
	@apply cursor-default opacity-60;
}

.link-container {
	@apply relative delay-[0s];
}
.link-container.sub-menu-open {
	@apply delay-300  z-20;
}
.link-container.sub-menu-open a::after {
	content: none !important;
}
.sub-menu {
	@apply fixed top-0 overflow-auto right-0 z-10 h-screen w-screen lg:w-[50vw] max-w-[600px] bg-darkblue  border-l-2 border-white pt-[70px] pb-[70px] px-10 lg:px-[60px];
}

.sub-menu li {
	@apply mb-4 md:mb-2;
}
.sub-menu li a {
	@apply text-[20px] lg:text-[24px] leading-[24px];
}
.sub-menu-1,
.sub-menu-2 {
	@apply relative right-auto h-auto w-full border-none py-0 px-0;
}
.sub-menu-1 {
	@apply mt-2;
}
.sub-menu-1 .menu-back,
.sub-menu-2 .menu-back {
	@apply hidden;
}
.sub-menu-1 li {
	@apply mb-1;
}
.sub-menu-1 li a {
	@apply text-copy16 pl-5;
}
.sub-menu-2 a {
	@apply text-copy16 pl-10;
}

#main-menu > .menu-item-has-children > .link-container > a {
	@apply relative pr-8 after:content-[''] after:w-4 after:h-4 after:absolute after:top-1/2 after:-translate-y-1/2 after:right-0 after:bg-sidebar-menu-arrow-cranleighclay after:bg-contain after:bg-no-repeat after:bg-center after:-rotate-90;
}
#main-menu .current_page_item {
	@apply text-cranleighclay;
}

.sub-menu-0
	> .menu-item-has-children.menu-item-has-children
	> .link-container
	> a {
	@apply relative pr-8 after:content-[''] after:w-4 after:h-4 after:absolute after:top-1/2 after:-translate-y-1/2 after:right-0 after:bg-menu-plus-cranleighclay after:bg-contain after:bg-no-repeat after:bg-center;
}

.sub-menu-1
	> .menu-item-has-children.menu-item-has-children
	> .link-container
	> a {
	@apply relative pr-8 after:content-none;
}

.sub-menu-0
	> .menu-item-has-children.menu-item-has-children
	> .link-container
	> a.actv,
.sub-menu-1
	> .menu-item-has-children.menu-item-has-children
	> .link-container
	> a.actv {
	@apply after:bg-menu-minus-cranleighclay;
}

button.open span {
	@apply bg-cranleighclay;
}
button.open [data-js="show-scroll"] {
	@apply bg-opacity-0;
}

#page-wrap {
	@apply after:content-[''] after:fixed after:w-full after:h-full after:bg-black/60 after:inset-0 after:opacity-0 after:invisible after:duration-300 after:z-20;
}

#page-wrap.open {
	@apply after:visible after:opacity-100;
}

.nochild div a::after {
	content: none !important;
}

/* Join the team tpl */

.page-template-tpl .banner .button {
	@apply after:rotate-90;
}

#search-filter-form-66371,
#search-filter-form-66427,
#search-filter-form-46287,
#search-filter-form-67716,
#search-filter-form-76059,
#search-filter-form-1856,
#search-filter-form-68473,
#search-filter-form-68701 {
	@apply w-full;
}
#search-filter-form-66371 ul,
#search-filter-form-66371 ul li.sf-field-taxonomy-vacancy_type,
#search-filter-form-66427 ul,
#search-filter-form-66427 ul li.sf-field-category,
#search-filter-form-46287 ul,
#search-filter-form-46287 ul li.sf-field-category,
#search-filter-form-67716 ul,
#search-filter-form-67716 ul li.sf-field-taxonomy-staff_categories,
#search-filter-form-76059 ul,
#search-filter-form-68473 ul,
#search-filter-form-68701 ul {
	@apply flex items-center justify-start flex-wrap lg:flex-nowrap w-full md:w-auto;
}

#search-filter-form-67716 ul, #search-filter-form-76059 ul {
	@apply flex-wrap;
}
.sf-field-taxonomy-vacancy_type h4,
.sf-field-category h4 {
	@apply font-aspectbold text-copy16 text-white uppercase mr-4 p-0;
}

#search-filter-form-66371 > ul,
#search-filter-form-67716 > ul,
#search-filter-form-76059 > ul,
#search-filter-form-1856 > ul {
	@apply justify-between;
}
#search-filter-form-66427 > ul,
#search-filter-form-46287 > ul {
	@apply inline-block w-full;
}
#search-filter-form-66427 > ul > li,
#search-filter-form-46287 > ul > li {
	@apply float-left last:md:float-right last:mt-2 last:lg:mt-1;
}
#search-filter-form-66427 > ul > li:last-child,
#search-filter-form-46287 > ul > li:last-child {
	@apply w-full md:w-auto children:w-full children:md:w-auto children:children:w-full children:children:md:w-auto;
}

#search-filter-form-66427 select,
#search-filter-form-46287 select {
	@apply text-titlesm py-[11px] px-[18px] mt-[5px] lg:ml-8 w-full md:w-[170px];
}

.searchandfilter label,
#search-filter-form-66371 input.sf-input-text,
#search-filter-form-66371 .sf-field-search {
	@apply w-full lg:w-auto;
}

#search-filter-form-66371 input,
#search-filter-form-66427 input,
#search-filter-form-46287 input,
#search-filter-form-67716 input,
#search-filter-form-76059 input,
#search-filter-form-1856 input,
#search-filter-form-68473 input,
#search-filter-form-68701 input {
	-webkit-appearance: none;
}
#search-filter-form-66371 .sf-label-radio,
#search-filter-form-66427 .sf-label-radio,
#search-filter-form-46287 .sf-label-radio,
#search-filter-form-67716 .sf-label-radio,
#search-filter-form-76059 .sf-label-radio,
#search-filter-form-1856 .sf-label-radio,
#search-filter-form-68473 .sf-label-radio,
#search-filter-form-68701 .sf-label-radio {
	@apply border border-white text-white rounded-[33px] text-titlesm text-center capitalize py-[11px] px-6  cursor-pointer;
}
#search-filter-form-66371 li,
#search-filter-form-66427 li,
#search-filter-form-46287 li,
#search-filter-form-67716 li,
#search-filter-form-76059 li,
#search-filter-form-68473 li,
#search-filter-form-68701 li,
#search-filter-form-1856 li {
	@apply mr-4 last:m-0 py-2.5;
}
#search-filter-form-66371 li input,
#search-filter-form-66427 li input,
#search-filter-form-46287 li input,
#search-filter-form-67716 li input,
#search-filter-form-76059 li input,
#search-filter-form-68473 li input,
#search-filter-form-68701 li input,
#search-filter-form-1856 li input {
	@apply h-0 flex;
}

#search-filter-form-66371 .sf-option-active .sf-label-radio,
#search-filter-form-66427 .sf-option-active .sf-label-radio,
#search-filter-form-46287 .sf-option-active .sf-label-radio,
#search-filter-form-67716 .sf-option-active .sf-label-radio,
#search-filter-form-76059 .sf-option-active .sf-label-radio,
#search-filter-form-68473 .sf-option-active .sf-label-radio,
#search-filter-form-68701 .sf-option-active .sf-label-radio {
	@apply border-cranleighclay text-blue bg-cranleighclay;
}
#search-filter-form-66371 input.sf-input-text,
#search-filter-form-66427 input.sf-input-text,
#search-filter-form-46287 input.sf-input-text,
#search-filter-form-67716 input.sf-input-text,
#search-filter-form-76059 input.sf-input-text,
#search-filter-form-68473 input.sf-input-text,
#search-filter-form-68701 input.sf-input-text {
	@apply relative bg-blue text-titlesm text-cranleighclay border border-white rounded-[33px] px-4 py-3 pl-10;
}

.sf-field-search {
	@apply relative after:content-[''] after:w-4 after:h-4 after:absolute after:top-1/2 after:-translate-y-1/2 after:left-3 after:bg-icon-search-white after:bg-contain after:bg-no-repeat after:bg-center;
}

form#search-filter-form-66426 .sf-field-search {
	@apply text-titlesm border border-blue text-blue pl-4 pr-8  after:left-auto after:right-3 after:bg-icon-search-blue py-1;
}
form#search-filter-form-66426 .sf-field-search input {
	@apply py-2 outline-none;
}

/* departments */
#search-filter-form-66416 {
	@apply w-full;
}
#search-filter-form-66416 ul,
#search-filter-form-66416 ul li.sf-field-taxonomy-vacancy_type {
	@apply flex items-center justify-start flex-wrap lg:flex-nowrap;
}

#search-filter-form-66416 > ul {
	@apply justify-between;
}

#search-filter-form-66416 input {
	-webkit-appearance: none;
}
#search-filter-form-66416 .sf-label-radio {
	@apply border border-white text-white rounded-[33px] text-titlesm text-center py-4 px-6;
}
#search-filter-form-66416 li {
	@apply mr-4 last:m-0;
}

#search-filter-form-66416 .sf-option-active .sf-label-radio {
	@apply border-cranleighclay text-blue bg-cranleighclay;
}
#search-filter-form-66416 input.sf-input-text {
	@apply relative bg-blue text-titlesm text-cranleighclay border border-white rounded-[33px] px-4 py-3 pl-10;
}

/* 404 page */
.error404 footer {
	display: none !important;
}
.error404 section.bg-grey.overflow-hidden.py-10.mt-36 {
	display: none !important;
}
.error404 [data-element="footer_logo_carousel"] {
	@apply hidden;
}

/* remove margin from last point admission */
[data-element="admission_step_n"]:last-child {
	margin-bottom: 0 !important;
}
[data-element="admission_img"][data-count="1"] {
	opacity: 1 !important;
}
[data-element="admission_step_n"][data-count="1"] {
	color: #ffc627 !important;
	background: #0c223f !important;
}

/*logo dark on white pages */
.page-template-tpl-news [data-js="navigation"] g.txt * {
	@apply fill-blue;
}

/* pagination */
.pagination__pages {
	@apply flex justify-center items-center mx-2;
}
.page-numbers {
	@apply w-[40px] h-[40px] lg:w-[39px] lg:h-[39px] flex justify-center items-center border border-blue rounded-full mx-1.5 lg:mx-2 text-titlesm leading-[18px] pt-px;
}
.page-numbers.dots {
	@apply border-none;
}
.page-numbers.current {
	@apply bg-cranleighclay text-blue border-none;
}
.next.page-numbers {
	@apply ml-3 lg:ml-[60px] m-0 indent-[-9999px] relative after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-y-1/2 after:-translate-x-1/2 after:w-[40px] lg:after:w-[39px] after:rotate-180 after:h-[40px] lg:after:h-[39px] after:bg-pagination-previous-navy after:bg-contain after:bg-no-repeat;
}

.prev.page-numbers {
	@apply mr-3 lg:mr-[60px] m-0 indent-[-9999px] relative after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-y-1/2 after:-translate-x-1/2  after:w-[40px] lg:after:w-[39px] after:h-[40px] lg:after:h-[39px] after:bg-pagination-previous-navy after:bg-contain after:bg-no-repeat;
}
.mfp-figure:after {
	content: none;
}
img.mfp-img {
	width: 100%;
	object-fit: contain;
	height: 100%;
}

.mfp-bottom-bar {
	@apply mt-[-80px] ml-[-32px];
}
.mfp-arrow {
	@apply left-[5vw] after:bg-pagination-previous-navy before:content-none after:border-0 after:w-[48px]  after:h-[48px] lg:after:w-[65px]  lg:after:h-[65px] after:top-0 after:m-0 after:bg-contain after:bg-no-repeat;
}
.mfp-arrow-right {
	@apply left-auto right-[5vw] after:rotate-180;
}

button.mfp-close,
button.mfp-arrow {
	@apply w-[48px] h-[48px] lg:w-[65px] lg:h-[65px] -mt-[24px] lg:-mt-[32px];
}

button.mfp-close {
	@apply m-0 top-2;
}
.mfp-counter {
	@apply font-aspectbold text-titlesm text-white uppercase;
}
.mfp-image-holder .mfp-close {
	@apply w-6 h-6  bg-close-icon-white bg-contain bg-no-repeat border-0 opacity-100 indent-[9999px] table mb-2;
}

/*Contact page*/
.page-template-tpl-contact div#content-wrap + section {
	@apply pt-0;
}
.page-template-tpl-contact [data-element="footer_logo_carousel"] {
	display: none !important;
}
@media only screen and (min-width: 1024px) {
	.page-template-tpl-contact [data-js="navigation"] {
		@apply fixed;
	}
	#map-canvas {
		position: sticky !important;
	}
}
.gform_title {
	@apply hidden;
}
.gfield_consent_label {
	@apply text-[14px] leading-[16px];
}
.gfield_consent_label a {
	@apply font-body relative after:content-[''] after:w-full after:h-px after:bg-cranleighclay after:absolute after:left-0 after:-bottom-[5px];
}
.gform_wrapper .gfield_required .gfield_required_text {
	@apply hidden;
}
.page-template-tpl-contact .gform_wrapper .gfield_required {
	@apply relative after:content-['*'];
}
.gform_wrapper textarea,
.gform_wrapper input {
	@apply text-blue outline-none py-2 px-3;
}
.gform_wrapper textarea {
	padding: 12px 20px !important;
	resize: none;
}

.wysiwyg .gform_wrapper ul li {
	@apply pl-0;
}
.wysiwyg .gform_wrapper ul li::after {
	@apply -left-[10px] top-[7px] w-1.5 h-1.5;
}

.gform_wrapper input[type="submit"] {
	@apply relative inline-table text-[15px] md:text-[17px] text-cta text-black bg-cranleighclay py-[13px] md:py-[16px] lg:py-[21px] px-[35px] pr-[75px] rounded-[33px] cursor-pointer after:h-3;
}
.gform_footer.top_label {
	@apply relative after:content-[''] after:absolute after:top-[calc(50%_-_4px)] after:left-[115px] after:-translate-y-1/2 after:w-4 after:h-4 after:bg-button-envelope-icon-black after:bg-contain after:bg-center after:bg-no-repeat after:duration-300  after:ease-in hover:after:translate-x-5;
}
.gform_wrapper .gfield_label {
	font-weight: 700 !important;
}
.gform_wrapper .gfield_error label {
	color: #fff !important;
}
.gform_wrapper .gfield_description {
	padding: 2px 0 2px 12px !important;
	font-size: 12px !important;
}

body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	.gfield.gfield--width-half {
	@apply col-span-12 lg:col-span-6;
}

body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	.gfield.gfield--width-third {
	@apply col-span-12 lg:col-span-4;
}

body:not(.page-template-tpl-contact) .gform_wrapper.gravity-theme .gfield {
	grid-column: 1/-1;
	min-width: 0;
}
body:not(.page-template-tpl-contact) .gform_wrapper.gravity-theme * {
	@apply box-border w-full;
}

body:not(.page-template-tpl-contact) .gform_wrapper div.gform_fields {
	@apply grid grid-cols-12 w-full gap-4;
}

body:not(.page-template-tpl-contact) .gfield_visibility_hidden,
body:not(.page-template-tpl-contact) .hidden_label {
}
.ginput_container_consent {
	@apply flex items-center;
}
.ginput_container_consent input {
	width: 20px !important;
	height: 20px !important;
	margin-right: 10px;
}
body:not(.page-template-tpl-contact) .gform_wrapper.gravity-theme textarea,
body:not(.page-template-tpl-contact) .gform_wrapper.gravity-theme input {
	@apply border border-blue;
}
body:not(.page-template-tpl-contact) .gform_wrapper input[type="submit"] {
	@apply border-none w-auto;
}
body:not(.page-template-tpl-contact) .gfield_label {
	@apply inline-block text-copy16 mb-2 p-0;
}
body:not(.page-template-tpl-contact) .gform_footer.top_label {
	@apply mt-5;
}

body:not(.page-template-tpl-contact) .gform_wrapper .gfield_required,
body:not(.page-template-tpl-contact) .gform_wrapper .gfield_label {
	width: auto !important;
}

body:not(.page-template-tpl-contact) ul.gform_fields textarea,
body:not(.page-template-tpl-contact) ul.gform_fields input {
	@apply border border-blue p-2;
}
body:not(.page-template-tpl-contact)
	.gform_legacy_markup_wrapper
	.gf_progressbar_percentage.percentbar_blue {
	@apply bg-blue;
}
body:not(.page-template-tpl-contact)
	.gform_legacy_markup_wrapper
	.gform_page_footer
	.button.gform_next_button,
body:not(.page-template-tpl-contact)
	.gform_legacy_markup_wrapper
	.gform_page_footer
	.button.gform_previous_button {
	@apply relative inline-table text-cta text-black bg-cranleighclay py-[21px] px-[35px] rounded-[33px] cursor-pointer;
}

body:not(.page-template-tpl-contact) .gform_wrapper.gravity-theme select {
	@apply border border-blue h-[50px];
}
body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	.gfield-choice-input {
	@apply w-auto;
}
body:not(.page-template-tpl-contact) .gform_wrapper .gfield_error label {
	color: #0c223f !important;
}
#boxcontent .popup-gallery p {
	@apply text-base;
}

body:not(.page-template-tpl-contact)
	.gform_wrapper
	.form_saved_message_emailform
	div.gform_fields {
	@apply flex items-end;
}
body:not(.page-template-tpl-contact)
	.gform_wrapper
	.form_saved_message_emailform
	input[type="email"] {
	@apply border border-blue h-[50px];
}
body:not(.page-template-tpl-contact)
	.gform_wrapper
	.form_saved_message_emailform
	input[type="submit"] {
	@apply h-[50px] px-[35px] py-0;
}
body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	input[type="button"] {
	@apply w-[200px] border-none h-[50px] px-[35px] py-0;
}

.gform_wrapper.gravity-theme .gf_progressbar_title,
body
	.gform_legacy_markup_wrapper
	.gf_progressbar_wrapper
	.gf_progressbar_title {
	@apply font-aspectbold uppercase text-titlesm text-blue opacity-100;
}
.gform_wrapper.gravity-theme .gf_progressbar_blue,
body .gform_legacy_markup_wrapper .gf_progressbar_wrapper .gf_progressbar_blue {
	@apply bg-blue/20 h-[6px] mb-10;
}
.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_blue,
body .gform_legacy_markup_wrapper .gf_progressbar_percentage.percentbar_blue {
	@apply bg-blue h-[6px] relative;
}
.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_blue span,
body
	.gform_legacy_markup_wrapper
	.gf_progressbar_percentage.percentbar_blue
	span {
	@apply text-blue absolute -bottom-8 -right-6 after:absolute after:-top-6 after:right-0 after:w-[18px] after:h-[18px] after:rounded-full after:bg-white after:border-[3px] after:border-blue;
}
.ui-datepicker-header select,
.ui-datepicker-header .ui-datepicker-next,
.ui-datepicker-header .ui-datepicker-prev {
	@apply text-white;
}
.ui-datepicker-header select {
	filter: brightness(0) invert(1);
	max-width: inherit !important;
}
.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	.ui-datepicker-next,
.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	.ui-datepicker-prev {
	filter: brightness(0) invert(1);
	background-image: url(../images/arrow-right-white.svg);
	@apply bg-no-repeat bg-contain bg-center w-[20px] mr-2.5;
}
.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	.ui-datepicker-prev {
	@apply bg-no-repeat bg-contain bg-center w-[20px] ml-2.5 rotate-180;
}
.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	.ui-datepicker-next::before,
.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	.ui-datepicker-prev::before {
	content: none !important;
}

select {
	appearance: none;
	background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
	background-repeat: no-repeat;
	background-position: right 1rem center;
	background-size: 1em;
}
.gform_wrapper.gravity-theme legend.gfield_label {
	@apply font-aspectbold text-[28px] leading-[34px] tracking-[-0.48px] uppercase;
}
.gform_wrapper fieldset,
.gform_wrapper.gravity-theme fieldset {
	@apply mt-8;
}
.gform_wrapper.gravity-theme .ginput_complex label,
body:not(.page-template-tpl-contact) .gform_wrapper .gfield_label {
	@apply font-bold;
}
.gform_wrapper.gravity-theme .ginput_container_address span {
	@apply mb-2;
}
.gform_wrapper.gravity-theme .gform_previous_button.button,
.gform_wrapper.gravity-theme .gform_save_link.button,
body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	input[type="button"],
.gpnf-modal .tingle-btn,
.gform_wrapper .gpnf-add-entry {
	@apply relative w-auto h-auto inline-table text-[15px] md:text-[17px] lg:text-cta text-black bg-cranleighclay py-[13px] md:py-[16px] lg:py-[21px] px-[28px] md:px-[35px] pr-[69px] rounded-[33px] after:content-none cursor-pointer;
}
.gform_wrapper.gravity-theme .gform_save_link.button svg {
	@apply hidden;
}
.gform_wrapper .gpnf-nested-entries th {
	@apply text-titlesm;
	width: 25% !important;
}
.gform_wrapper .gpnf-nested-entries th * {
	width: auto !important;
}
.gpnf-modal .gpnf-modal-header {
	background-color: #0d2140 !important;
}
.gpnf-modal .tingle-btn {
	background-color: #ffc627 !important;
	border-radius: 33px !important;
}
body:not(.page-template-tpl-contact) .gform_wrapper .gpnf-add-entry {
	@apply w-auto;
}
body:not(.page-template-tpl-contact) .gform_wrapper input[type="submit"],
.gform_legacy_markup_wrapper .gform_footer input.button,
.gform_legacy_markup_wrapper .gform_footer input[type="submit"],
.gform_legacy_markup_wrapper .gform_page_footer input.button,
.gform_legacy_markup_wrapper .gform_page_footer input[type="submit"] {
	@apply relative after:content-[''] after:absolute after:top-1/2 after:right-10 after:-translate-y-1/2 after:w-4 after:h-2.5 after:bg-arrow-right-gold after:bg-contain after:bg-center after:bg-no-repeat
        after:duration-300  after:ease-in hover:after:translate-x-5;
}
body
	.gform_legacy_markup_wrapper
	.field_sublabel_above
	.ginput_complex.ginput_container
	label,
body
	.gform_legacy_markup_wrapper
	.field_sublabel_above
	div[class*="gfield_time_"].ginput_container
	label {
	@apply font-bold;
	font-size: 15px;
	padding-top: 5px;
	margin-bottom: 0;
	margin-top: 0;
}
.gform_legacy_markup_wrapper .description_above .gfield_description {
	@apply leading-[18px];
}
body:not(.page-template-tpl-contact) .gform_wrapper input[type="submit"] {
	@apply text-[18px];
}
body
	.gform_legacy_markup_wrapper
	input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]) {
	font-size: 15px;
	margin-bottom: 0;
	margin-top: 0;
	padding: 8px;
}
body .gform_legacy_markup_wrapper .gfield_header_item,
body .gform_legacy_markup_wrapper .gform_fileupload_rules,
body .gform_legacy_markup_wrapper .ginput_complex label,
body:not(.page-template-tpl-contact) .gform_wrapper .gfield_label {
	@apply text-[15px] pt-[5px] mb-0 mt-0 leading-[20px];
}
body .gform_wrapper.gravity-theme .field_sublabel_above .description,
body .gform_wrapper.gravity-theme .field_sublabel_above .gfield_description,
body .gform_wrapper.gravity-theme .field_sublabel_above .gsection_description {
	@apply p-0 mt-0;
	padding: 0 !important;
	line-height: 19px;
}
.required_label > label {
	@apply relative after:content-['*'] after:text-weyblue;
}

body .gform_legacy_markup_wrapper .top_label div.ginput_container {
	margin-top: 0 !important;
}

body
	.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	select {
	background-size: 24px 18px;
	background-position: 100% 50%;
}
.ui-datepicker-header,
.ui-datepicker-header .ui-datepicker-header {
	background: #0c223f !important;
}

.tingle-modal--overflow::-webkit-scrollbar {
	@apply h-1.5 rounded-[3px] z-[9999px];
}
.tingle-modal--overflow::-webkit-scrollbar-track {
	@apply bg-white rounded-[3px];
}
.tingle-modal--overflow::-webkit-scrollbar-thumb {
	@apply bg-blue rounded-[3px];
}
.tingle-modal--overflow::-webkit-scrollbar-thumb:hover {
	@apply bg-darkblue;
}
.tingle-modal:before {
	filter: none !important;
	backdrop-filter: none !important;
}
body .gform_wrapper.gravity-theme .gfield_checkbox label,
body .gform_wrapper.gravity-theme .gfield_radio label {
	line-height: 22px;
}
.gchoice.gchoice_3_111_1 {
	@apply flex justify-between items-start mt-4;
}
.gchoice.gchoice_3_111_1 input {
	@apply translate-y-1;
}
.gform_previous_button.button {
	@apply before:content-['Previous'];
}
.gform_wrapper .gform_page_footer input[type="submit"] {
	@apply pl-[75px];
}
.gform_legacy_markup_wrapper .gfield_checkbox li label,
.gform_legacy_markup_wrapper .gfield_radio li label {
	font-size: 14px !important;
	margin-top: 6px !important;
	margin-bottom: 4px !important;
}
.gform_legacy_markup_wrapper .description_above .gfield_description {
	border: none !important;
	padding: 0 !important;
	margin-bottom: 14px !important;
}
.wysiwyg .gform_wrapper ul:not(.pdflist) li {
	@apply mb-2;
}

.home #page-wrap {
	@apply overflow-hidden;
}

.gform_wrapper.gravity-theme .gform_save_link.button {
	@apply bg-white border border-blue text-blue;
}

#signup-content {
	@apply bg-blue py-[200px] text-cranleighclay;
}

li.sf-field-search input {
	height: 40px !important;
}

.post-password-form input {
	@apply border border-grey;
}
.post-password-form p {
	@apply mb-5;
}

.gform_wrapper .post-password-form input[type="submit"] {
	padding-right: 35px;
}

.gform_button.button {
	@apply !m-0
}