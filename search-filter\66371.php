<?php // Vacancy 
?>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-8 px-10 lg:px-0">
    <?php if ($query->have_posts()) : ?>
            <?php $i = 0;
            while ($query->have_posts()) : $query->the_post();  ?>
            <?php foreach ( get_the_terms( get_the_ID(), 'vacancy_type' ) as $term ) {
                $color = get_field('color', $term);
                $short_title = get_field('short_title', $term);
                
            } ?>
            <div class="relative border border-blue py-[39px] px-[30px] hover:bg-<?= $color;?> <?php if($color == "grey"): echo 'hover:text-blue hover:border-grey'; else: echo 'hover:text-white [&_.icontochange]:after:hover:brightness-0 [&_.icontochange]:after:hover:invert'; endif; ?> duration-300 ease-in"> 
                <div data-element="vacancy_wrap">
                    <h3 class=" text-titlemd leading-[28px] lg:text-titlesidebar lg:leading-[36px] uppercase mb-2"><?php the_title();?></h3>
                    <div class="children:text-copy16"><?php the_excerpt();?></div>
                </div>
                <div class="flex justify-start items-center mt-7 mb-3 ">
                    <span class="relative bg-<?= $color;?> 
                     text-[13px] leading-[14px] md:text-titlesm uppercase py-[9px] pl-[35px] after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-3 after:top-1/2 after:-translate-y-1/2 after:w-[14px] after:h-[14px]
                    <?php if($color == "grey"): echo 'text-blue pr-3 after:bg-suitcase-icon-navy '; else: echo 'text-white pr-3 after:bg-suitcase-icon-white '; endif; ?>
                    
                    
                    "><?= $short_title; ?></span>
                    <span class="icontochange after:duration-300 after:ease-in relative  text-[13px] leading-[14px] md:text-titlesm uppercase pl-5 ml-5 after:bg-clock-icon-black after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-0 after:top-1/2 after:-translate-y-1/2 after:w-[14px] after:h-[14px]"><?php the_field('hrs_job'); ?></span>
                    
                </div>
                
                <span class="block icontochange after:duration-300 after:ease-in relative  text-[13px] leading-[14px] md:text-titlesm uppercase pl-5 after:bg-calendar-icon-black after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-0 after:top-[calc(50%_-_2px)] after:-translate-y-1/2 after:w-[14px] after:h-[14px]"><?php the_field('closing_date','options');?> <?php the_field('closing_date_job'); ?></span>
                <a class="absolute inset-0 h-full w-full" href="<?php the_permalink();?>"></a>
            </div>

                

            <?php $i++;
            endwhile; ?>

           
           
                  
      


    <?php else : ?>
        <div class="text-center">
            <h2 class="">Sorry, no results found</h2>
        </div>
    <?php endif; ?>
    </div>
</div>


<script>
   
    function sameHeights () {
    var nodeList = document.querySelectorAll('[data-element="vacancy_wrap"]');
    var elems = [].slice.call(nodeList);
    var mq = window.matchMedia('(min-width: 1024px)')
    if (mq.matches) {
        var tallest = Math.max.apply(Math, elems.map(function(elem, index) {
            elem.style.minHeight = ''; // clean first
            return elem.offsetHeight;
        }));
        elems.forEach(function(elem, index, arr){ 
            elem.style.minHeight = tallest + 'px';
        });
    }
    
    }
    var resized = true;
    var timeout = null;
    var refresh = function(){
    if(resized) {
        requestAnimationFrame(sameHeights);
    }
        clearTimeout(timeout);
        timeout = setTimeout( refresh, 100);
    resized = false;
    };
    window.addEventListener('resize', function() { resized = true; });
    refresh();
</script>