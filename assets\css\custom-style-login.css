.login h1 a {
	background-image: url('../img/twklogo.svg');
	padding-bottom: 0px;
	height: 50px;
	width: 300px;
	background-size: 250px;
	margin-bottom: 0;
}

.login .button-primary {
	background-color: #05E1C4;
	color: black;
	text-transform: uppercase;
	text-shadow: none;
	box-shadow: none;
}

.login .button-primary:hover,
.login .button-primary:focus {
	color: black;
	background-color: #0bb39d;
}

body.login {
	background: #14222f;
	background-repeat: no-repeat;
	background-attachment: fixed;
	background-position: center;
	background-size: cover;
}

body.login:before {
	content: '';
	background-color: rgba(0, 0, 0, 0.4);
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1;
}

#login {
	z-index: 3;
	position: absolute;
	left: calc(50% - 160px);
}

.login #backtoblog a,
.login #nav a {
	color: white;
}

.login form,
.login #login_error {
	border-radius: 0;
}

.login form .input,
.login form input[type=checkbox],
.login input[type=text] {
	border-radius: 0;
}

.wp-core-ui .button-group.button-large .button,
.wp-core-ui .button.button-large {
	padding: 5px 20px;
	height: auto;
	border: none;
	border-radius: 0;
}

#wp-auth-check-wrap #wp-auth-check {
	background-color: black;
}


/* Confirm admin email */
body.login-action-confirm_admin_email #login{
	left: 50%;
    transform: translateX(-50%);
}

