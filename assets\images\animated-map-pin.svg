<svg width="120px" height="120px" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g fill="#00C3A3" fill-rule="evenodd" stroke-width="1" stroke="#00C3A3" stroke-opacity="0.3">
    <circle cx="50" cy="50" r="50">
      <animate attributeName="r" begin="0s" dur="3s" values="0;20" keyTimes="0;1" keySplines="0.1,0.2,0.3,1" calcMode="spline" repeatCount="indefinite"></animate>
      <animate attributeName="stroke-opacity" begin="0s" dur="3s" values="0;.3;.3;0" repeatCount="indefinite"></animate>
      <animate attributeName="fill-opacity" begin="0s" dur="3s" values="1;0" repeatCount="indefinite"></animate>
    </circle>
    <circle cx="50" cy="50" r="30">
      <animate attributeName="r" begin="-1s" dur="3s" values="0;20" keyTimes="0;1" keySplines="0.1,0.2,0.3,1" calcMode="spline" repeatCount="indefinite"></animate>
      <animate attributeName="stroke-opacity" begin="-1s" dur="3s" values="0;.3;.3;0" repeatCount="indefinite"></animate>
      <animate attributeName="fill-opacity" begin="-1s" dur="3s" values="1;0" repeatCount="indefinite"></animate>
    </circle>
  </g>
</svg>