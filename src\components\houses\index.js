import { $qs, $qsa } from '../../utils/QuerySelector';
import gsap from 'gsap';
import Swiper, { Navigation, Pagination, Scrollbar, Autoplay } from 'swiper';


function houses() {
  if($("[data-element='image_carousel']").length) {	
    $( "[data-element='image_carousel']" ).each(function( index ) {
        var slidenumb = $(this).find('.swiper-slide').length;
        if(slidenumb == 1) {
          $(this).addClass( "disabled" );
          $(this).removeClass( "swiper" );
        }
      });
      //slider stats
       var swiper = new Swiper(".swiper[data-element='image_carousel']", {
           modules: [Navigation, Pagination, Scrollbar, Autoplay],
           loop: true,
           autoplay: {
            delay: 2000,
          },
           pagination: {
             el: ".swiper-pagination",
             clickable: true,
           },
         });

    }

    if($("[data-element='houses_banner']").length) {	

      const houses_title =  gsap.to("[data-element='houses_title']",  {
        autoAlpha:0,
        scrollTrigger: {
          trigger: "[data-element='houses_banner']",
          start: "50% 50%",
          end:"+=60%",
          scrub:true,
        }
      });

      const houses_scroll_down =  gsap.to("[data-element='houses_scroll_down']",  {
        autoAlpha:0,
        scrollTrigger: {
          trigger: "[data-element='houses_banner']",
          start: "50% 50%",
          end:"+=20%",
          scrub:true,
        }
      });

      const houses_overlay =  gsap.to("[data-element='houses_overlay']",  {
        autoAlpha:0,
        scrollTrigger: {
          trigger: "[data-element='houses_banner']",
          start: "50% 50%",
          end:"+=20%",
          scrub:true,
        }
      });

     

      const houses_scroll =  gsap.to("[data-element='houses_scroll']",  {
        width:"100%",
        scrollTrigger: {
          trigger: "[data-element='houses_banner']",
          start: "50% 50%",
          end:"+=60%",
          scrub:true,
        }
      });

    }



}

export default houses;



