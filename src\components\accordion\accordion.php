<?php
/**
 * Template part to display the block called "Accordion".
 *
 * @package twkmedia
 */

$title = get_sub_field('title');

?>

<section class="lg:mt-4 mb-10 lg:mb-12">
	<div class="container">
    <?php if(get_sub_field('accordion_title')):?><h2 class=" text-blue uppercase text-title mt-20 mb-3"><?php echo get_sub_field('accordion_title');?></h2><?php endif;?>
        <div class="">

            <?php if (have_rows('accordion')): ?>

                <div aria-label="Accordions">

                    <?php while (have_rows('accordion')): the_row(); ?>

                        <div class="border-b border-blue" data-target="accordion" data-state="closed">
                            <div class="flex justify-between items-center pt-6 pb-3 lg:pb-[18px] cursor-pointer" data-target="accordion-trigger">
                                <h3 class="relative w-full font-aspectregular text-titlemd lg:text-titlesidebar uppercase pr-12 after:content-[''] after:absolute after:top-[calc(50%_-_12px)] lg:after:top-1/2 after:-right-[50px] after:-translate-y-1/2 after:w-[26px] after:h-[26px] after:bg-accordion-plus-weyblue after:bg-contain after:bg-no-repeat after:bg-center"><?php echo get_sub_field('title'); ?></h3>
                                <div class="flex-none w-[49px] h-[49px] rounded-[50%] bg-pink"></div>
                            </div>
                            <div class="prose h-0 overflow-hidden" aria-hidden="true" data-target="accordion-content">
                                <div class="pb-9 wysiwyg">

                                    <?php echo get_sub_field('content'); ?>

                                </div>
                            </div>
                        </div>

                    <?php endwhile; ?>
                    
                </div>

            <?php endif; ?>
            
        </div>
	</div>
</section>