<?php
/**
 * Load assets
 *
 * @package twkmedia
 */

/**
 * Load CSS and Javascript files
 */
function load_theme_assets() {
	// Register and load CSS.
	wp_enqueue_style( 'tailwind-css', get_template_directory_uri() . '/assets/css/style.css', array(), filemtime( get_stylesheet_directory() . '/assets/css/style.css' ) );
	// Register and load JS.
	wp_register_script( 'script-js', get_template_directory_uri() . '/assets/js/bundle.js', array( 'jquery' ), filemtime( get_stylesheet_directory() . '/assets/js/script.js' ), true );

	// EXAMPLE OF HOW TO USE PHP VARIABLES IN JS FILE> CAN BE USEFUL FOR GETTING home_url().
	wp_localize_script( 'script-js', 'php_vars', array( 'themeDirUrl' => get_template_directory_uri(), 'homeUrl' => home_url() ) );
	wp_enqueue_script( 'script-js' );

}
add_action( 'wp_enqueue_scripts', 'load_theme_assets' );


/**
 * Prevent from loading the following styles
 *
 * Gutenberg blocks
 * WooCommerce Gutenberg blocks
 *
 * @return void
 */
function twk_remove_css_styles(){
	wp_dequeue_style( 'wp-block-library' );
	wp_dequeue_style( 'wp-block-library-theme' );
	wp_dequeue_style( 'wc-block-style' ); // Remove WooCommerce block CSS.
}
add_action( 'wp_enqueue_scripts', 'twk_remove_css_styles', 100 );
