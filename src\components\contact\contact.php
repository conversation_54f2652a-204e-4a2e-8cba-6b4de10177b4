<?php
/**
 * Template part to display the block called "Contact".
 *
 * @package twkmedia
 */


?>




<div class="relative bg-blue w-screen min-h-screen">
   
    <div class="flex justify-end flex-wrap flex-col-reverse lg:flex-row">
        <div id="map-canvas" class="sticky top-0 left-0 h-[300px] md:h-[400px] lg:h-screen w-full lg:w-1/2"></div>
        <div class="container w-full lg:w-1/2 lg:pr-[10%] pb-10 lg:pb-20">
            <div class="px-10 lg:pr-0 lg:pl-[10%] text-white">
                <div class="mt-[130px] lg:mt-[206px]">
                    <div class="container">
                        <h2 class="text-title lg:text-titleh2 text-white uppercase   mb-10 lg:mb-6"><?php the_field('title_contacttpl'); ?></h2>
                    </div>
                    <div class="mb-14 lg:mb-[74px]">
                        <div class="flex justify-start mx-auto mb-10 lg:mb-20 children:relative children:font-aspectbold children:text-titlesm children:lg:text-copy16 children:uppercase children:text-white children:duration-300 children:ease-in-out children:mr-5 lg:children:mr-10 last:children:mr-0 children:after:content-[''] children:after:absolute children:after:-bottom-2 children:after:left-0 children:after:h-0.5 children:after:w-full" role="tablist" aria-label="">
                            <button class="after:bg-cranleighclay" role="tab" aria-selected="true" aria-controls="panel-403-0" id="tab-0" tabindex="0"><?php the_field('our_address_title_contacttpl'); ?></button>
                            <button class="opacity-50" role="tab" aria-selected="false" aria-controls="panel-403-1" id="tab-1" tabindex="-1"><?php the_field('key_contacts_title_contacttpl'); ?></button>
                            <button class="opacity-50" role="tab" aria-selected="false" aria-controls="panel-403-2" id="tab-2" tabindex="-1"><?php the_field('make_an_enquiry_title_contacttpl'); ?></button>
                        </div>
                    </div>
                    <div class="container justify-center">
                        <div class="  overflow-hidden " id="panel-403-0" role="tabpanel" tabindex="0" aria-labelledby="tab-0">
                            <h3 class="text-titlemd lg:text-titlesidebar text-white uppercase mb-5"><?php the_field('our_address_title_contacttpl'); ?></h3>
                            
                            <?php the_field('address_contacttpl'); ?>

                            <div class="xl:flex mt-8 mb-9">
                                <div class="relative pl-6 mr-8 after:content-[''] after:w-[17px] after:h-[17px] after:absolute after:top-1/2 after:-translate-y-1/2 after:left-0 after:bg-phone-yellow after:bg-contain after:bg-no-repeat after:bg-center">
                                    <?php the_field('phone_number_contacttpl'); ?>
                                </div>
                                <div class="relative pl-6 after:content-[''] after:w-[17px] after:h-[17px] after:absolute after:top-1/2 after:-translate-y-1/2 after:left-0 after:bg-envelope-yellow-navy after:bg-contain after:bg-no-repeat after:bg-center">
                                <a href="mailto:<?php the_field('email_contacttpl'); ?>"><?php the_field('email_contacttpl'); ?></a>
                                </div>
                                
                            </div>

                            <div class="lg:w-3/12 flex items-center mb-14 justify-start">
                            <?php

                                $social_feeds = array('twitter', 'instagram',  'facebook', 'linkedin',  'youtube');

                                foreach ($social_feeds as $social_feed) {
                                    $url = $social_feed . '_url';
                                    $get_page = 'option';

                                    if (get_field($url, $get_page)) {
                                        echo '<a class="mr-3 last:mr-0 ease-in duration-300 hover:opacity-80 " href="' . esc_url( get_field( $url, $get_page ) ) . '" target="_blank">';
                                        include locate_template('assets/images/social/' . $social_feed . '_circle.svg');
                                        echo '</a>';
                                    }
                                }
                                ?>
                            </div>

                            <?php 
                            $link = get_field('get_directions_contacttpl');
                            if( $link ): 
                                $link_url = $link['url'];
                                $link_title = $link['title'];
                                $link_target = $link['target'] ? $link['target'] : '_self';
                                ?>
                                <a class="button after:bg-button-pin-icon-black after:h-4 after:w-2.5  duration-300 ease-in-out
                                 hover:bg-[#cca026] hover:after:translate-x-0
                                
                                " href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
                            <?php endif; ?>
                        </div>
                        <div class=" opacity-0 h-0 invisible overflow-hidden " id="panel-403-1" role="tabpanel" tabindex="0" aria-labelledby="tab-1">
                            <h3 class="text-titlemd lg:text-titlesidebar text-white uppercase mb-5"><?php the_field('key_contacts_title_contacttpl'); ?></h3>
                            <div class="text-copy16 text-white border border-white/20 children:border-b children:border-white/20 last:children:border-b-0 mb-8">
                                <?php if( have_rows('key_contacts_grid_contacttpl') ): ?>
                                    <?php while( have_rows('key_contacts_grid_contacttpl') ): the_row(); ?>
                                        <div class="grid grid-cols-1 lg:grid-cols-2 py-1.5 lg:py-0 odd:children:border-r children:border-white/20 children:pt-[14px] children:pb-[14px] children:lg:pb-5 children:px-8">
                                            <p><?php the_sub_field('name'); ?></p>
                                            <p class="[&_a]:text-yellow break-all"><?php the_sub_field('phone_email'); ?></p>
                                        </div>
                                    <?php endwhile; ?>
                                <?php endif; ?>   
                            </div>
                        </div>
                        <div class=" opacity-0 h-0 invisible overflow-hidden " id="panel-403-2" role="tabpanel" tabindex="0" aria-labelledby="tab-2">
                            <h3 class="text-titlemd lg:text-titlesidebar text-white uppercase mb-5"><?php the_field('make_an_enquiry_title_contacttpl'); ?></h3>
                            <?php $idform = get_field('form_id_contacttpl');?>
                            <?php echo do_shortcode( '[gravityform id="' . $idform .'" title="true" ajax="true"]' ); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
</div>
		