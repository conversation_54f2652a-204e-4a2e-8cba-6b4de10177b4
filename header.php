<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content-wrap">
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package twkmedia
 */

?>
<!DOCTYPE html>
<!--[if lt IE 7 ]> <html class="ie ie6 no-js" <?php language_attributes(); ?>> <![endif]-->
<!--[if IE 7 ]>    <html class="ie ie7 no-js" <?php language_attributes(); ?>> <![endif]-->
<!--[if IE 8 ]>    <html class="ie ie8 no-js" <?php language_attributes(); ?>> <![endif]-->
<!--[if IE 9 ]>    <html class="ie ie9 no-js" <?php language_attributes(); ?>> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-js" <?php language_attributes(); ?>>
<!--<![endif]-->
<!-- the "no-js" class is for Modernizr. -->

<head>

<!-- Start cookieyes banner --> <script id="cookieyes" type="text/javascript" src="https://cdn-cookieyes.com/client_data/8e29d4a1f51233d72e1c8e2d/script.js"></script> <!-- End cookieyes banner -->

	<?php if ( strpos( home_url(), 'twkmedia.com' ) == false ) : ?>
	<!-- Google Tag Manager -->
	<script>
	(function(w,d,s,l,i){
		w[l]=w[l]||[];
		w[l].push( {'gtm.start': new Date().getTime(),event:'gtm.js'});
		var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';
		j.async=true;
		j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
	})(window,document,'script','dataLayer','GTM-TVWN6GJ');
	</script>
	<!-- End Google Tag Manager -->
	<?php endif; ?>

	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<!-- Always force latest IE rendering engine (even in intranet) & Chrome Frame -->
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<?php if ( is_search() ) : ?>
	<meta name="robots" content="noindex, nofollow" />
	<?php endif; ?>
	<!-- SETUP AND AUTHENTICATE WITH TWK CLIENTS USER http://google.com/webmasters -->
	<meta name="google-site-verification" content="">

	<!-- USE YOAST TO ADD SOCIAL MEDIA META TAGS -->

	<!-- GENERATE FAVICON USING https://realfavicongenerator.net/ -->
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="pingback" href="<?php bloginfo( 'pingback_url' ); ?>" />

	<?php wp_head(); ?>
	<script type='text/javascript' id='ajax-script-js-extra'>
	/* <![CDATA[ */
	var my_ajax_object = {"ajax_url":"https:\/\/www.cranleigh.org\/wp-admin\/admin-ajax.php"};
	/* ]]> */
	</script>


	

</head>

<body <?php body_class('bg-grey-light font-body'); ?>>
	

	<?php if ( strpos( home_url(), 'twkmedia.com' ) == false ) : ?>
	<!-- Google Tag Manager (noscript) -->
	<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TVWN6GJ"
	height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
	<!-- End Google Tag Manager (noscript) -->
	<?php endif; ?>

	<?php
	// Cookies notification.
	/**
	 * TODO: Add cookie names to WP Rocket exceptions (Advanced Rules - Never Cache Cookies)
	 * twk_cookies_google_analytics
	 * twk_cookies_marketing
	 * twk_cookies_popup_
	 * twk_cookies_bar_cookie_
	 */
	if ( get_page_template_slug( get_the_ID() ) !== 'tpl/tpl-cookies.php' ) {
		require locate_template( 'tpl/parts/notifications-cookies.php' );
		require locate_template( 'tpl/parts/notifications-popup.php' );
	}
	?>
	<?php
	// The blog index page needs to get the queried ID
	// and not the first post ID.
	global $post;
	$post = get_queried_object();

	edit_post_link( 'Edit page' );
	?>

	<div id="page-wrap">
		<a class="skip-link sr-only" href="#content-wrap"><?php esc_html_e( 'Skip to content', '_s' ); ?></a>

		
				<div class="absolute top-[27px] left-6 lg:top-[35px] md:left-10 sm:left-[100px] z-30" data-js="navigation">
					<a href="<?php echo home_url(); ?>" class="children:w-[205px] md:w-[203px] lg:children:w-[243px] children:h-auto">
						<img src="<?php echo get_template_directory_uri(); ?>/assets/images/logos/cranleigh-2025-dark.png" alt="Cranleigh School">
					</a>
				</div>
				<div class="hidden lg:flex fixed top-12 lg:top-[70px] right-32 z-30 flex-wrap content-center pointer-events-auto">
					<div class=" w-[256px] bg-white rounded-[29px] border border-blue translate-y-1">
						<span class="relative block font-aspectbold uppercase text-blue text-copy16 px-6 pt-[13px] pb-[11px] cursor-pointer after:content-[''] after:absolute after:top-5 after:right-6  after:w-[11px] after:h-[6px] after:bg-quicklinks-arrow-navy after:bg-contain after:bg-center after:bg-no-repeat" data-element="quick_links"><?php the_field('quick_links_title' , 'options');?></span>
						<div class="h-0 overflow-hidden" data-state="closed" data-element="quick_links_menu">
							<?php if( have_rows('quick_links', 'options') ): ?>
							
							<?php while( have_rows('quick_links', 'options') ): the_row(); ?>
							<?php 
								$link = get_sub_field('link');
								if( $link ): 
									$link_url = $link['url'];
									$link_title = $link['title'];
									$link_target = $link['target'] ? $link['target'] : '_self';
									?>
									<a class="first:mt-2 table text-copy16 text-blue mb-4 px-6" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
								<?php endif; ?>
							<?php endwhile; ?>
							
						<?php endif; ?> 
						<h3 class="text-copy16 leading-[16px] text-blue uppercase mb-4 mt-8 px-6"><?php the_field('our_schools_title' , 'options');?></h3>
						<?php if( have_rows('our_schools_links', 'options') ): ?>
							
							<?php while( have_rows('our_schools_links', 'options') ): the_row(); ?>
								<a class="relative flex justify-between items-center border-b border-[#D8D8D8] pb-[14px] mb-[14px] last:mb-0 px-6 last:border-none <?php if(get_sub_field('active') == true): echo 'after:w-[5px] after:h-6 after:bg-blue after:absolute after:left-0 after:top-0'; endif; ?> " <?php $link = get_sub_field('link'); if( $link ): $link_url = $link['url'];?>href="<?php echo esc_url( $link_url ); ?>"<?php endif; ?>>

									<h4 class="text-copy16 text-blue"><?php the_sub_field('name');?></h4>
									
									<?php 
									$image = get_sub_field('icon');
									if( !empty( $image ) ): ?>
										<img class="w-4" src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
									<?php endif; ?>

									
									
								</a>
							<?php endwhile; ?>
							
						<?php endif; ?>
						</div>
					</div>
				</div>
				<div class="fixed top-[46px] lg:top-[96px] right-6 lg:right-14 z-[60] flex flex-wrap content-center pointer-events-auto" data-pin="navigation" data-js="navigation">
					
					
					<button class="flex justify-center items-center relative focus:outline-none" data-js="full-menu-toggle">
						<div class="absolute  w-16 h-16 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-weyblue" data-js="show-scroll"></div>
						<div class="block  ">
							<span aria-hidden="true" class="block  h-0.5 w-[28px] bg-white transform transition duration-500 ease-in-out -translate-y-1" :class="{'rotate-45': open,' -translate-y-1.5': !open }"></span>
							<span aria-hidden="true" class="block   h-0.5 w-[28px] bg-white   transform transition duration-500 ease-in-out" :class="{'opacity-0': open } "></span>
							<span aria-hidden="true" class="block   h-0.5 w-[28px] bg-white transform  transition duration-500 ease-in-out translate-y-1" :class="{'-rotate-45': open, ' translate-y-1.5': !open}"></span>
						</div>
					</button>

				</div>

				<div class="h-screen overflow-auto w-full max-w-[600px] lg:w-1/2 right-0 bg-blue text-cranleighclay z-50 invisible fixed top-0 pointer-events-auto border-l-2 border-white" data-js="main-nav" data-lenis-prevent>
					<div class="absolute children:h-[150vh] opacity-0 bottom-[20%] -right-1/4" data-js="bg-o">
						
					</div>
					<div class="absolute children:h-[150vh] opacity-0 bottom-[20%] -right-1/4" data-js="bg-white-o">
						
					</div>

					<div class=""></div>
					<div class=" z-20  relative py-[70px] pb-[50px] lg:pb-[70px] px-10 lg:px-[60px] ">
						<?php
						if (has_nav_menu('main_menu')) {
							wp_nav_menu(
								array(
									'theme_location' => 'main_menu',
									'menu_id' => 'main-menu',
									'menu_class' => 'children:mb-2 md:children:mb-4',
									'depth'   => 4,
									'walker'  => new Twk_Nav_Walker(),
									
								)
							);
						} ?>
					</div>
					<div class="px-10 lg:px-[60px]">
						<h3 class="text-copy16 leading-[16px] text-white uppercase mb-4"><?php the_field('our_schools_title' , 'options');?></h3>

						
						<?php if( have_rows('our_schools_links', 'options') ): ?>
							
							<?php while( have_rows('our_schools_links', 'options') ): the_row(); ?>
								<a class="flex justify-between items-center border-b  <?php if(get_sub_field('active') == true): echo 'border-yellow border-opacity-100'; else: echo 'border-opacity-30 border-white'; endif; ?> pb-[14px] mb-[14px]" <?php $link = get_sub_field('link'); if( $link ): $link_url = $link['url'];?>href="<?php echo esc_url( $link_url ); ?>"<?php endif; ?>>

									<h4 class="text-copy16 text-white"><?php the_sub_field('name');?></h4>
									
										
									

									<?php 
									$image = get_sub_field('icon');
									if( !empty( $image ) ): ?>
										<img class="w-4" src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
									<?php endif; ?>

									
									
								</a>
							<?php endwhile; ?>
							
						<?php endif; ?>
						 
						<div class="lg:flex flex-wrap mt-10 children:lg:w-1/2 children:uppercase children:font-aspectbold children:text-copy16 children:mb-3">
						<?php if( have_rows('nav_bottom_links', 'options') ): ?>
							
							<?php while( have_rows('nav_bottom_links', 'options') ): the_row(); ?>
							<?php 
								$link = get_sub_field('link');
								if( $link ): 
									$link_url = $link['url'];
									$link_title = $link['title'];
									$link_target = $link['target'] ? $link['target'] : '_self';
									?>
									<a class="block" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
								<?php endif; ?>
							<?php endwhile; ?>
							
						<?php endif; ?> 
						</div>
					</div>

					
					


					
				</div>
		<div id="content-wrap">
