import { $qs, $qsa } from '../../utils/QuerySelector';
import gsap from 'gsap';
import ScrollTrigger from "gsap/ScrollTrigger";
import DrawSVGPlugin from "gsap/DrawSVGPlugin";
gsap.registerPlugin(ScrollTrigger, DrawSVGPlugin);
import Swiper, { Navigation, Pagination, Scrollbar, Autoplay, FreeMode } from 'swiper';


function admission() {
  if($("[data-element='admission_img_wrap']").length) {
    $( window ).on("load", function() { 
        const svgwhite = $('#svg_line_white path');
        const svgblue = $('#svg_line_blue path');
        
        const heightadmission = $("[data-element='admission_wrap'").height() - $(window).height() + 64;
        
        const heightadmissionstep = $("[data-element='admission_step_wrap'").height();
        const amountofsteps =  $( "[data-element='admission_step']" ).length;

        const heighpersection = heightadmissionstep/amountofsteps;
        
       

        $( "[data-element='admission_step_n']" ).click(function( index ) {
            const stepcount = $(this).attr('data-count');
            $('html, body').animate({
				scrollTop: $( "[data-element='admission_step'][data-count='" + stepcount + "']").offset().top - $(window).height()/2 + 100
			}, 500);

        });
        



        let admissionfix = ScrollTrigger.create({
            trigger: "[data-element='admission_img_wrap']",
            pin: "[data-element='admission_img_wrap']",
            start: "top top",
            end: heightadmission
          });
       
        
        $( "[data-element='admission_step']" ).each(function( index ) {
            const step = $(this);
            const stepcount = step.attr('data-count');
            const img = $( "[data-element='admission_img'][data-count='" + stepcount + "']" );
            const number = $( "[data-element='admission_step_n'][data-count='" + stepcount + "']" );

            const marginb = 300 * 40 / heighpersection + 'px';
            number.css('margin-bottom', marginb);


            gsap.to(img, {
              scrollTrigger: {
                trigger: step,
                start: "center bottom", 
                end:"+="+""+$(this).height(),
                toggleActions: "play none none reverse",
                onEnter: function(){ 
                    number.addClass('text-cranleighclay bg-blue');
                    number.removeClass('text-blue bg-white');
                },
                onEnterBack: function(){ 
                    number.removeClass('text-cranleighclay bg-blue');
                    number.addClass('text-blue bg-white');
                },
                onLeaveBack: function(){ 
                  number.removeClass('text-cranleighclay bg-blue');
                  number.addClass('text-blue bg-white');
              },
                
               
              },
              duration:.2,
              autoAlpha:1,
              
              
            });
            
          }).promise().done( function(){
            const heightsection = $('#svg_line_blue').parent().height();
            svgblue.attr("d", "M0 0 v" + heightsection + " 0");
            svgwhite.attr("d", "M0 0 v" + heightsection + " 0");

            gsap.set(svgblue,{drawSVG:"0% 0%"});
            gsap.to(svgblue,{
                scrollTrigger: {
                    trigger: "[data-element='admission_step_wrap']",
                    start: "top bottom",
                    end:heightadmission,
                    scrub: 1
                },
                ease: "none",
                drawSVG:"0% 100%",
                
            });
         } );
         

         
         $( "[data-element='staff_carousel']" ).each(function( index ) {
          var slidenumb = $(this).find('.swiper-slide').length;
          if(slidenumb == 1) {
            $(this).addClass( "disabled" );
            $(this).removeClass( "swiper" );
          }
        });
        //staff stats
         var swiper = new Swiper(".swiper[data-element='staff_carousel']", {
             modules: [Navigation, Pagination, Scrollbar, Autoplay],
             loop: true,
             slidesPerView: 1,
            breakpoints: {
              1024: {
                spaceBetween: 32,
                slidesPerView: 3,
              }
            },
             autoplay: {
              delay: 2000,
            },
             pagination: {
               el: ".swiper-pagination",
               clickable: true,
             },
           });

    });

  }
   
}

export default admission;