@mixin tran-prefix($settings) {
    -webkit-transition: $settings;
    -moz-transition: $settings;
    -ms-transition: $settings;
    -o-transition: $settings;
    transition: $settings;
}

@mixin transform($attr) {
  -ms-transform: $attr; /* IE 9 */
   	-webkit-transform: $attr; /* Safari */
   	transform: $attr; /* Safari */
}

@mixin transform-origin($attr) {
  -ms-transform-origin: $attr; /* IE 9 */
   	-webkit-transform-origin: $attr; /* Safari */
   	transform-origin: $attr; /* Safari */
}
@mixin animation($settings) {
    -webkit-animation: $settings;
    -moz-animation: $settings;
    -ms-animation: $settings;
    -o-animation: $settings;
    animation: $settings;
}

@mixin keyframe($animation_name) {
    @-webkit-keyframes #{$animation_name} {
        @content;
    }

    @-moz-keyframes #{$animation_name} {
        @content;
    }

    @-o-keyframes #{$animation_name} {
        @content;
    }

    @keyframes #{$animation_name} {
        @content;
    }
}

////Pixels to EM mixin
@function px2rem($px) {
	@return ($px / $base-font-size) * 1rem;
}



////Font size and line height mixin
@mixin font-size($font-size, $line-height: false) {
	font-size: $font-size; /* #{$font-size} */
	font-size: px2rem($font-size); /* #{$font-size} */

	@if $line-height != false {
		line-height: ($line-height / $font-size); /* #{$line-height} */
	}
}


////ASPECT RATIO
@mixin aspect-ratio($width, $height) {
    position: relative;
    &:before {
        display: block;
        content: "";
        width: 100%;
        padding-top: ($height / $width) * 100%;
    }
    > div {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }
}

///ASPECT RATIO WITH CENTERED DIV
@mixin aspect-ratio-center($width, $height) {
    position: relative;
    &:before {
        display: block;
        content: "";
        width: 100%;
        padding-top: ($height / $width) * 100%;
    }
    > div {
        position: absolute;
        text-align: center;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-size: 100vw;
    }
}


////CENTERED CONTENT WITHIN RELATIVE PARENT
@mixin centered {
    position: absolute;
    text-align: center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-size: 100vw;
}

////PSEUDO ELEMENT FOR GRADIENT BACKGROUNDS AND COLOUR OVERLAYS
@mixin pseudo-elem($top, $right, $left, $bottom) {
    content: "";
    position: absolute;
    top: $top;
    bottom: $bottom;
    right: $right;
    left: $left;
    width: 100%;
    height: 100%;
}

///NORMALISING LISTS
@mixin beauty-lists {
    list-style: none;
    margin: 0;
    padding: 0;
    > li {
        padding: 0;
        text-indent: 0;
    }
}

