import { $qs, $qsa } from '../../utils/QuerySelector';
import gsap from 'gsap';
import Swiper, { Navigation, Pagination, Scrollbar, Autoplay, FreeMode } from 'swiper';



function stats() {
    $( "[data-element='stats_carousel']" ).each(function( index ) {
      var slidenumb = $(this).find('.swiper-slide').length;
      if(slidenumb == 1) {
        $(this).addClass( "disabled" );
        $(this).removeClass( "swiper" );
      }
    });
    //slider stats
     var swiper = new Swiper(".swiper[data-element='stats_carousel']", {
         modules: [Navigation, Pagination, Scrollbar, Autoplay],
         loop: true,
         autoplay: {
          delay: 2000,
        },
         pagination: {
           el: ".swiper-pagination",
           clickable: true,
         },
       });
    
 }
 
 export default stats;