import Swiper, { Navigation, Pagination, Scrollbar, Autoplay, FreeMode } from 'swiper';



function where_next() {
    $( window ).on("load", function() { 
        const paddingleft_where = $( "#content-wrap .container" ).offset().left;
        
        var swiper = new Swiper(".swiper[data-element='where_carousel']", {
            modules: [Navigation, Pagination, Scrollbar, Autoplay],
            loop: false,
            slidesPerView: 1.2,
            slidesOffsetBefore:0,
            slidesOffsetAfter:0,
            spaceBetween: 24,
            breakpoints: {
                1024: {
                  slidesPerView: 2.3,
                  spaceBetween: 58,
                  slidesOffsetBefore:paddingleft_where,
                }
              },
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        });
    });
    
 }
 
 export default where_next;


