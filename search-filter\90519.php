<?php // News 
?>
<div class="container px-4 md:px-10 mx-auto my-20 relative">
    <?php if ($query->have_posts()) : ?>
        <div class="flex flex-wrap">
            <?php $i = 0;
            while ($query->have_posts()) : $query->the_post();  ?>

                <div class="md:px-5 w-full md:w-1/2 xl:w-1/2 mb-10 border-b border-black/20 pb-8 md:border-none last:border-none last:pb-0">
                    <a href="<?php echo get_permalink($post->ID); ?>" class=" ">
                        <div class="aspect-w-3 aspect-h-2 relative flex items-end  mb-4 children:rounded-[10px] rounded-[10px] custom-shadow-2">
                            <?php echo twk_output_featured_image_with_fallback($post->ID, 'large', '', 'news') ?>  
                        </div>
                        <div class="font-bodybold text-green-light text-[15px] leading-[25px]"><?php echo get_the_date(); ?> <?php
                            $cat = null;
                            if (get_post_type() === 'post') :
                                $cat = get_the_category($post->ID);
                            else :
                                //$cat = get_the_terms($post->ID, 'blogcategory');
                            endif;
                            if ($cat) : ?> • <?php echo $cat[0]->name; ?><?php endif; ?></div>
                        <div class="font-display text-4xl text-white"><?php the_title();?></div>
                    </a>
                </div>


                

            <?php $i++;
            endwhile; ?>

            <?php
            $total_pages = $query->max_num_pages;
            $paged = isset($_GET['sf_paged']) ? $_GET['sf_paged'] : 1;

            ?>
            <div class="w-full mt-20 mb-20">
                <div class="flex justify-center text-black">

                <?php require locate_template( 'tpl/parts/_pagination.php' ); ?>
            </div>
        </div>
                  
                </div>
            </div>
        </div>




    <?php else : ?>
        <div class="text-center">
            <h2 class=" text-blue uppercase text-title30 lg:text-title mt-12 mb-3 ">Sorry, no results found</h2>
        </div>
    <?php endif; ?>
</div>