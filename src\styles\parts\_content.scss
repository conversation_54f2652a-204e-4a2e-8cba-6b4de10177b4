// Content styles.

::-moz-selection { /* Code for Firefox */
	color: white;
	background: $secondary;
	padding: 10px;
}

::selection {
	color: white;
	background: $secondary;
	padding: 10px;
}


#content-wrap {
    position: relative;
}


// Edit page link
a.post-edit-link {
	position: fixed;
	z-index: 999;
    bottom: 0;
    background: $primary;
    color: white;
    padding: 10px 15px;
}

// TYPOGRAPHY

.intro {
    @include font-size(18px, 24px);
	font-weight: bold;
}

@media( min-width: 768px ){
	.split-list{
		column-count: 2;
	}
}

p,
ul,
ol,
li {
    @include font-size(16px, 24px);

    a:not(.button) {
        color: $secondary;
        transition: color 0.3s linear;

        &:hover {
            color: darken($secondary, 10);
        }
    }
}

ul {  
    &.gallery {
        width: 100%;
        padding: 0;

        li {
            display: inline-block;
            width: 50%;
            margin: 0;
            padding-bottom: 20px;

            &:nth-child(2n - 1) {
                padding-right: 15px;
            }

            &:nth-child(2n) {
                padding-left: 15px;
            }

			@media (max-width: 575.98px) {
				width: 100%;
			}

            &:before {
                content: none;
            }

            div.gallery__image {
                width: 100%;
                min-height: 250px;
                max-height: 250px;
                background-size: cover;
                background-position: center center;
                background-repeat: no-repeat;
                margin: 0;
            }

            p {
                display: none;
                margin: 0;
            }
        }
    }
}


blockquote {
    position: relative;
    margin: 65px 0px 60px 0px;

    p {
        font-family: $sans;
        @include font-size(24px, 30px);
        color: $primary;
        margin: 0;
    }

    cite {
        font-family: $sans;
        @include font-size(11px, 16px);
        text-transform: uppercase;
        font-style: normal;
        letter-spacing: 0.85px;
        color: $secondary;
    }
}


// Images
img {
    max-width: 100%;
    height: auto;
}



//ALIGNMENTS
.alignleft {
    float: left;
    margin-right: 30px;
}

.alignright {
    float: right;
    margin-left: 30px;
}

.aligncenter {
    display: block;
    margin: 0 auto;
}

.block{
	// Custom list dot.
	/* ul:not(.slick-dots){
        padding: 0;
        li + li{
            margin-top: 12px;
        }
        li{
			position: relative;
            list-style: none;
            padding-left: 25px;
            &:before{
                position: absolute;
                top: calc(0.3em + 4px);
                left: 0px;
                content: '';
                height: 8px;
                width: 8px;
                background: $secondary;
                border-radius: 50%;
            }
        }
	} */
}
