/*
	We now manage margins and paddings using the BS utilities. Add more as necessary here: 
*/
$twk_custom_spacers:(
	0: 0px,
	sm: 15px, // default for acf sm
	md: 50px, // default for acf md
	lg: 80px, // default for acf lg
	15: 15px, 
	30: 30px, 
	40: 40px,
	50: 50px, 
);

@each $breakpoint in map-keys($grid-breakpoints) {
	@include media-breakpoint-up($breakpoint) {
		$infix: breakpoint-infix($breakpoint, $grid-breakpoints);

		@each $prop, $abbrev in (margin: m, padding: p) {
			@each $size, $length in $twk_custom_spacers {
				.#{$abbrev}#{$infix}-#{$size} { #{$prop}: $length !important; }
				.#{$abbrev}t#{$infix}-#{$size},
				.#{$abbrev}y#{$infix}-#{$size} {
					#{$prop}-top: $length !important;
				}
				.#{$abbrev}r#{$infix}-#{$size},
				.#{$abbrev}x#{$infix}-#{$size} {
					#{$prop}-right: $length !important;
				}
				.#{$abbrev}b#{$infix}-#{$size},
				.#{$abbrev}y#{$infix}-#{$size} {
					#{$prop}-bottom: $length !important;
				}
				.#{$abbrev}l#{$infix}-#{$size},
				.#{$abbrev}x#{$infix}-#{$size} {
					#{$prop}-left: $length !important;
				}
			}
		}


		// Negative margins
		//@each $size, $length in $twk_custom_spacers {
		//	@if $size != 0 {
		//	.m#{$infix}-n#{$size} { margin: -$length !important; }
		//	.mt#{$infix}-n#{$size},
		//	.my#{$infix}-n#{$size} {
		//		margin-top: -$length !important;
		//	}
		//	.mr#{$infix}-n#{$size},
		//	.mx#{$infix}-n#{$size} {
		//		margin-right: -$length !important;
		//	}
		//	.mb#{$infix}-n#{$size},
		//	.my#{$infix}-n#{$size} {
		//		margin-bottom: -$length !important;
		//	}
		//	.ml#{$infix}-n#{$size},
		//	.mx#{$infix}-n#{$size} {
		//		margin-left: -$length !important;
		//	}
		//	}
		//}
	}
}

@include media-breakpoint-down(md) {
	.pb-lg,
	.py-lg {
		padding-bottom: 50px !important;
	}

	.pt-lg,
	.py-lg {
		padding-top: 50px !important;
	}

	.mb-lg,
	.my-lg {
		margin-bottom: 50px !important;
	}

	.mt-lg,
	.my-lg {
		margin-top: 50px !important;
	}

	.mb-md,
	.my-md {
		margin-bottom: 30px !important;
	}

	.mt-md,
	.my-md {
		margin-top: 30px !important;
	}
}

@include media-breakpoint-down(sm) {
	.pb-lg,
	.py-lg {
		padding-bottom: 30px !important;
	}

	.pt-lg,
	.py-lg {
		padding-top: 30px !important;
	}

	.mb-lg,
	.my-lg {
		margin-bottom: 30px !important;
	}

	.mt-lg,
	.my-lg {
		margin-top: 30px !important;
	}

	.mb-md,
	.my-md {
		margin-bottom: 20px !important;
	}

	.mt-md,
	.my-md {
		margin-top: 20px !important;
	}
}

.my-no-bottom {
	margin-bottom: 0 !important;
}


.my-no-top {
	margin-top: 0 !important;
}

