<?php
/**
 * Template Name: Contact
 *
 * @package twkmedia
 */

get_header();

?>

<?php
if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();

		?>

		<?php include locate_template('src/components/contact/contact.php');?>
		

		

		<?php
	endwhile;
endif;
?>

<?php
get_footer();
?>


<script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyClkL7IcK0m42bXNgubXYN-6HtClrus7j4">
</script>
<script>
	//GOOGLE FORMS
	type="text/javascript">

	(function ($){
	$(document).ready(function(){

		//GOOGLE FORMS
		function initialize() {
			var image = '<?php echo get_template_directory_uri(); ?>/assets/images/pin-navy.svg';
			var icon = {
				url: image, // url
				scaledSize: new google.maps.Size(36, 50), // scaled size
				origin: new google.maps.Point(0, 0), // origin
				anchor: new google.maps.Point(18, 50) // anchor
			};
			var myLatlng = new google.maps.LatLng(<?php the_field('lat_contacttpl'); ?>, <?php the_field('long_contacttpl'); ?>);
			var iconPos = new google.maps.LatLng(<?php the_field('lat_contacttpl'); ?>, <?php the_field('long_contacttpl'); ?>);
			var draggable = true;
			if ($(window).width() < 800) {
				draggable = false;
			}
			var zoom = 14;
			var mapOptions = {
				zoom: zoom,
				center: myLatlng,
				scrollwheel: false,
				draggable: draggable,
				styles: 
				[
					{
						"featureType": "landscape",
						"stylers": 
							[
								{ "hue": "#FFBB00" },
								{ "saturation": 43.400000000000006 },
								{ "lightness": 37.599999999999994 },
								{ "gamma": 1 }
							]
					},
					{
						"featureType": "road.highway",
						"stylers": 
							[
								{ "hue": "#FFC200" },
								{ "saturation": -61.8 },
								{ "lightness": 45.599999999999994 },
								{ "gamma": 1 }
							]
					},
					{
						"featureType": "road.arterial",
						"stylers": 
							[
								{ "hue": "#FF0300" },
								{ "saturation": -100 },
								{ "lightness": 51.19999999999999 },
								{ "gamma": 1 }
							]
					},
					{
						"featureType": "road.local",
						"stylers": 
							[
								{ "hue": "#FF0300" },
								{ "saturation": -100 },
								{ "lightness": 52 },
								{ "gamma": 1 }
							]
					},
					{
						"featureType": "water",
						"stylers": 
							[
								{ "hue": "#0078FF" },
								{ "saturation": -13.200000000000003 },
								{ "lightness": 2.4000000000000057 },
								{ "gamma": 1 }
							]
					},
					{
						"featureType": "poi",
						"stylers": 
							[
								{ "hue": "#00FF6A" },
								{ "saturation": -1.0989010989011234 },
								{ "lightness": 11.200000000000017 },
								{ "gamma": 1 }
							]
					}
				]
			}
			var map = new google.maps.Map(document.getElementById('map-canvas'), mapOptions);
			var marker = new google.maps.Marker({
				position: iconPos,
				map: map,
				icon: icon
			});
		}

		window.addEventListener( 'load', initialize() )

	});
})(jQuery);
</script>
