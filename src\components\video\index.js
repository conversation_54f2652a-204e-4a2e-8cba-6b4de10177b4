import { $qs, $qsa } from '../../utils/QuerySelector';
import gsap from 'gsap';
import Swiper, { Navigation, Pagination, Scrollbar, Autoplay, FreeMode } from 'swiper';



function video() {
    if ($('[data-element="play_video"]').length) {

        $( '[data-element="play_video"]' ).on('click', function(e){
            e.preventDefault();
            document.getElementById('modal_video_iframe').src = 'https://player.vimeo.com/video/' + $(this).data('video') + '?autoplay=1';
            $('#modal_video').removeClass('invisible opacity-0');
            gsap.from('#modal_video_box', {scale:.8, autoAlpha:0, duration:.5, delay:.2});
        });
    
        $( '#close_modal_video' ).on('click', function(e){
            e.preventDefault();
            $('#modal_video').addClass('invisible opacity-0');
            document.getElementById('modal_video_iframe').src = "";
        });


        $( "[data-element='video_carousel']" ).each(function( index ) {
        var slidenumb = $(this).find('.swiper-slide').length;
        if(slidenumb == 1) {
            $(this).addClass( "disabled" );
            $(this).removeClass( "swiper" );
        }
        });
        //slider video_carousel
        var swiper = new Swiper(".swiper[data-element='video_carousel']", {
            modules: [Navigation, Pagination, Scrollbar, Autoplay],
            loop: true,
            autoplay: {
            delay: 4000,
            },
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
            });
        
    }
    
 }
 
 export default video;


