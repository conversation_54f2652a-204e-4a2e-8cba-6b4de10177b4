<?php

/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package twkmedia
 */

get_header();

?>

<?php if (have_posts()) : ?>

	<?php while (have_posts()) : the_post(); ?>

		<?php include locate_template('src/components/banner/banner.php'); ?>
		<div class="container lg:grid lg:grid-cols-12 px-10 lg:px-0">
			<main class="<?php if (!get_field('hide_sidebar')) : ?>col-start-1 col-span-7<?php else : echo 'col-span-12';
																						endif; ?>" id="main">

				<!-- Flexible Strips -->
				<?php
				if (post_password_required()) :
				?>

					<div class="container">
						<div class="mx-auto w-8/12">
							<div class="gform_wrapper text-center">
								<?php echo get_the_password_form(); ?>
							</div>
						</div>
					</div>
		

	<?php else :

					// check if the flexible content field has rows of data.
					if (have_rows('blocks')) :

						$blockCount = 0;

						// loop through the rows of data.
						while (have_rows('blocks')) :

							the_row();

							$layout = get_row_layout();
							//$layout = str_replace('_', '-', $layout);
							$blockId = $layout . '-' . $blockCount;

							include locate_template('src/components/' . $layout . '/' . $layout . '.php');

							$blockCount++;

						endwhile;

					endif;
				endif;

	?>
	<!-- END Flexible Strips -->

	</main>
	<?php if (!get_field('hide_sidebar')) : ?>
		<aside data-element="sidebar" class=" hidden lg:block relative z-20 col-start-9 col-span-4 children:mb-12 last:children:mb-0 <?php if (get_field('banner_type') == "style1") : ?>-translate-y-[156px]<?php endif; ?>">
			<?php if (have_rows('sidebar_content')) : ?>
				<?php while (have_rows('sidebar_content')) : the_row(); ?>
					<?php if (get_row_layout() == 'submenu') : ?>
						<div class="relative bg-blue p-10 ">
							<?php
							if ($post->post_parent) {
								$ancestors = get_post_ancestors($post->ID);
								$root = count($ancestors) - 1;
								$parent = $ancestors[$root];
							} else {
								$parent = $post->ID;
							}

							?>
							<?php  ?>
							<?php $pagetoexclude = get_field('hide_from_sidebar_menu', 'options'); ?>
							<h3 class=" text-titlesidebar uppercase text-cranleighclay mb-[18px]"><?php echo get_the_title($parent); ?></h3>
							<ul class="relative children:relative children:text-white children:list-none children:text-copy19 children:mb-3" data-element="sidebar_menu">

								<?php if ($pagetoexclude) : ?>
									<?php
									wp_list_pages(array(
										'title_li' => null,
										'child_of' => $parent,
										'depth' => 2,
										'sort_column' => 'menu_order',
										'exclude'      => implode(",", $pagetoexclude),
									));
									?>
								<?php else : ?>
									<?php
									wp_list_pages(array(
										'title_li' => null,
										'child_of' => $parent,
										'depth' => 2,
										'sort_column' => 'menu_order',
									));
									?>

								<?php endif; ?>

							</ul>
							<span class="currentmenu"></span>
						</div>
					<?php elseif (get_row_layout() == 'page_teaser') : ?>
						<div class="bg-cranleighclay p-10">
							<?php if (get_sub_field('title')) : ?>
								<h3 class=" text-titlesidebar uppercase text-blue mb-3"><?php echo get_sub_field('title'); ?></h3>
								<p class="text-copy16"><?php echo get_sub_field('copy'); ?></p>
								<?php
								$link = get_sub_field('cta');
								if ($link) :
									$link_url = $link['url'];
									$link_title = $link['title'];
									$link_target = $link['target'] ? $link['target'] : '_self';
								?>
									<a class="button-blue mt-8" href="<?php echo esc_url($link_url); ?>" target="<?php echo esc_attr($link_target); ?>"><?php echo esc_html($link_title); ?></a>
								<?php endif; ?>
							<?php else : ?>
								<h3 class=" text-titlesidebar uppercase mb-3"><?php the_field('page_teaser_title', 'options'); ?></h3>
								<p class="text-copy16"><?php the_field('page_teaser_copy', 'options'); ?></p>
								<?php
								$link = get_field('page_teaser_cta', 'options');
								if ($link) :
									$link_url = $link['url'];
									$link_title = $link['title'];
									$link_target = $link['target'] ? $link['target'] : '_self';
								?>
									<a class="button bg-blue text-cranleighclay after:bg-arrow-right-yellow mt-8" href="<?php echo esc_url($link_url); ?>" target="<?php echo esc_attr($link_target); ?>"><?php echo esc_html($link_title); ?></a>
								<?php endif; ?>

							<?php endif; ?>

						</div>
					<?php elseif (get_row_layout() == 'news') : ?>


						<?php
						$args = array(
							'post_type' => 'post',
							'posts_per_page'      => 1,
							'post__not_in'           => [get_the_ID()]
						);
						$query = new WP_Query($args);

						if ($query->have_posts()) : ?>
							<?php
							while ($query->have_posts()) : $query->the_post();  ?>
								<div class="bg-white">
									<div class="w-full">
										<a href="<?php echo get_permalink($post->ID); ?>" class=" ">
											<div class="children:aspect-[3/2] [&_img]:aspect-[3/2] [&_img]:object-cover children:w-full children:h-full children:object-cover children:object-center relative flex items-end mb-4">
												<?php if (get_the_post_thumbnail_url()) : ?>
													<?php echo twk_output_featured_image_with_fallback($post->ID, 'large', '', 'news') ?>
												<?php else : ?>
													<?php
													$pages_fallback_image = get_field('posts_fallback_image', 'options');
													if (!empty($pages_fallback_image)) : ?>
														<img class="" src="<?php echo esc_url($pages_fallback_image['url']); ?>" alt="<?php echo esc_attr($pages_fallback_image['alt']); ?>" />
													<?php endif; ?>
												<?php endif; ?>

											</div>
											<div class=" text-titlesm uppercase"><?php echo get_the_date(); ?> <?php
																																$cat = null;
																																if (get_post_type() === 'post') :
																																	$cat = get_the_category($post->ID);
																																else :
																																//$cat = get_the_terms($post->ID, 'blogcategory');
																																endif;
																																if ($cat) : ?> • <?php echo $cat[0]->name; ?><?php endif; ?></div>
											<div class="  text-titlesidebar uppercase mt-3"><?php the_title(); ?></div>
										</a>
									</div>
								</div>
							<?php endwhile; ?>
						<?php endif; ?>



					<?php endif; ?>
				<?php endwhile; ?>
			<?php endif; ?>
		</aside>
	<?php endif; ?>
	</div>

	<?php include locate_template('src/components/where_next/where_next.php'); ?>


<?php endwhile; ?>

<?php endif; ?>

<?php get_footer(); ?>