<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @link https://codex.wordpress.org/Creating_an_Error_404_Page
 *
 * @package twkmedia
 */

get_header();
?>



<main class="main bg-blue ">
	<div class="container min-h-screen flex justify-between flex-col">
		<div>
		<?php 
			$image = get_field('404_image','options');
			if( !empty( $image ) ): ?>
				<img class="w-[70%] lg:w-[600px] mt-[25vh] mx-auto" src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
			<?php endif; ?>
			<h1 class="bold text-titlemd text-center text-white mb-20 mt-8"><?php the_field('404_text','options');?></h1>
		</div>
		<div class="xl:w-9/12 lg:flex justify-between items-center bg-darkblue mx-auto p-10 md:p-14 text-center lg:text-left">
			<h2 class="lg:w-5/12 font-aspectbold text-titlemd lg:text-titlesidebar text-white uppercase mb-4 lg:mb-0"><?php the_field('404_bottom_title','options');?></h2>
			<div class="lg:w-5/12 md:flex flex-wrap">
			<?php if( have_rows('404_link','options') ): ?>
				<?php while( have_rows('404_link','options') ): the_row(); ?>
					<?php 
					$link = get_sub_field('link');
					if( $link ): 
						$link_url = $link['url'];
						$link_title = $link['title'];
						$link_target = $link['target'] ? $link['target'] : '_self';
						?>
						<a class="mx-auto table md:mx-0 md:w-1/2 text-yellow text-copy16 mb-3 last:mb-0" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
					<?php endif; ?>
				<?php endwhile; ?>
			<?php endif; ?>
			</div>
		</div>
	</div>

</main>

<?php
get_footer();
