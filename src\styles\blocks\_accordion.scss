// Block - Accordion
.accordion {
	&__title{
		padding-right: 50px; // avoid crashing with the plus/arrow icon
	}

	&__item{
		border-bottom: 1px solid $primary;
	}

	&__trigger {
		position: relative;
		padding: 28px 30px;
		cursor: pointer;
	}
	
	&__content--wrapper {
		.accordion__content{
			padding: 0 15px 0 30px;
		}
	}
	
	&__trigger-icon {
		position: absolute;
		top: 28px;
		right: 30px;
		width: 14px;
		height: 14px;
	}

	// Plus icon
	&__plus {
		position: absolute;
		top: 14px;
		right: 0;
		width: 14px;
		height: 14px;

		&:before,
		&:after{
			content: "";
			position: absolute;
			background-color: $primary;
			transition: transform 0.3s ease-in-out;
		}

		/* Vertical line */
		&:before{
			top: 0;
			left: 6px;
			width: 2px;
			height: 100%;
		}

		/* horizontal line */
		&:after{
			top: 6px;
			left: 0;
			width: 100%;
			height: 2px;
			transform-origin: center;
		}
	}


	// OPEN
	.open{
		// Arrow
		.accordion__trigger-icon svg{
			transform: rotate(180deg);
		}

		// Plus
		.accordion__plus {
			
			&:before {
				transform: rotate(-90deg);
			}
		}
	}
}
