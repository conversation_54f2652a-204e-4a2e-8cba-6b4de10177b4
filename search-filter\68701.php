<?php // Past Events 
?>
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-8 mb-10 lg:mb-12">
    <?php if ($query->have_posts()) : ?>
            <?php
            while ($query->have_posts()) : $query->the_post();  ?>
          
           <div class="relative mb-4 ">
                <?php if(get_the_post_thumbnail_url()): ?>
                    <img class="aspect-[3/2] object-cover" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
                <?php else: ?>
                    <img class="aspect-[3/2] object-cover" src="<?php the_field('events_fallback_image', 'options'); ?>" alt="<?php the_title();?>" />
                <?php endif; ?>
                <span class="block  text-titlesm uppercase mt-5"><?php the_field('cranevent_end_date');?></span>
                <h2 class="  text-titlemd uppercase mb-3 mt-2"><?php the_title(); ?></h2>
                <?php
                           $cat = get_the_terms($post->ID, 'event-type');
                           if ($cat) : ?><span class="block  text-weyblue text-titlesm uppercase mt-3"><?php echo $cat[0]->name; ?></span><?php endif; ?>
                <a class="get_popupevent absolute top-0 left-0 w-full h-full z-20 pointer-events-none" href="<?php the_permalink(); ?>" target="_self" data-postid="<?php echo get_the_ID();?>"></a>
            </div>
                

            <?php 
            endwhile; ?>
            

            

           
           
                  
      


    <?php else : ?>
        <div class="text-center">
            <h2 class="">Sorry, no results found</h2>
        </div>
    <?php endif; ?>
    </div>
    <?php
            $total_pages = $query->max_num_pages;
            $paged = isset($_GET['sf_paged']) ? $_GET['sf_paged'] : 1;

            ?>
            <div class="w-full mt-20 mb-20">
                <div class="flex justify-center text-black">

                <?php require locate_template( 'tpl/parts/_pagination.php' ); ?>
            </div>
</div>