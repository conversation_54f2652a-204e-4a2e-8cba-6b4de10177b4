{"version": 3, "sources": ["webpack://twk-boilerplate-template/./src/styles/varmix/_font.scss", "webpack://twk-boilerplate-template/./src/styles/varmix/_color.scss", "webpack://twk-boilerplate-template/./src/styles/editor-style.scss", "webpack://twk-boilerplate-template/./src/styles/parts/_titles.scss", "webpack://twk-boilerplate-template/./src/styles/varmix/_mixin.scss", "webpack://twk-boilerplate-template/./src/styles/parts/_button.scss", "webpack://twk-boilerplate-template/./src/styles/parts/_content.scss"], "names": [], "mappings": "AAAA;;;;;;;GAAA;ACsBC;EARA,sBAVa;ACMd;;ADaC;EALA,WAda;ACUd;;ADQC;EARA,sBAVa;ACcd;;ADKC;EALA,WAda;ACkBd;;ADAC;EARA,yBAVa;ACsBd;;ADHC;EALA,cAda;AC0Bd;;ADRC;EARA,yBAVa;AC8Bd;;ADXC;EALA,cAda;ACkCd;;ADZA;EACI;ACeJ;;AC1CA;EACC,oCHSO;AEoCR;ACvCI;EC+CH,eD9C0B,EC8CH;EACvB;EAGC;AFPF;ACxCI;EC2CH,eD1C0B,EC0CH;EACvB;EAGC;AFFF;ACzCI;ECuCH,eDtC0B,ECsCH;EACvB;EAGC;AFGF;AC1CI;ECmCH,eDlC0B,ECkCH;EACvB;EAGC;AFQF;AC3CI;EC+BH,eD9B0B,EC8BH;EACvB;EAGC;AFaF;ACvCA;EACC,gCHxBM;AEiEP;;ACrCA;EACC;ADwCD;;ACtCA;EACC;ADyCD;;AGlFA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHqFJ;;AIlGA;EACC;EACA,mBLFW;EKGX;AJqGD;;AIlGA;EACC;EACA,mBLRW;EKSX;AJqGD;;AIjGA;EACI;AJoGJ;;AI/FA;EACC;EACA;EACG;EACA,mBLxBM;EKyBN;EACA;AJkGJ;;AI7FA;EFsBC,eErBsB,EFqBC;EACvB;EAGC;EExBD;AJkGD;;AI/FA;EACC;IACC;OAAA;YAAA;EJkGA;AACF;AI/FA;;;;EFWC,eEPsB,EFOC;EACvB;EAGC;AFwFF;AIjGI;;;;EACI,cLhDI;EKiDJ;EAAA;AJsGR;AIpGQ;;;;EACI;AJyGZ;;AInGI;EACI;EACA;AJsGR;AIpGQ;EACI;EACA;EACA;EACA;AJsGZ;AIpGY;EACI;AJsGhB;AInGY;EACI;AJqGhB;AIlGG;EAdK;IAeJ;EJqGF;AACF;AInGY;EACI;AJqGhB;AIlGY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AJoGhB;AIjGY;EACI;EACA;AJmGhB;;AI5FA;EACI;EACA;AJ+FJ;AI7FI;EACI,gCNrGD;EI6CN,eEyD0B,EFzDH;EACvB;EAGC;EEsDM,cL/GE;EKgHF;AJiGR;AI9FI;EACI,gCN5GD;EI6CN,eEgE0B,EFhEH;EACvB;EAGC;EE6DM;EACA;EACA;EACA,cLxHI;AC0NZ;;AI5FA;EACI;EACA;AJ+FJ;;AIzFA;EACI;EACA;AJ4FJ;;AIzFA;EACI;EACA;AJ4FJ;;AIzFA;EACI;EACA;AJ4FJ;;AIzFA;EAEC;;;;;;;;;;;;;;;;;;;;KAAA;AJ+GD,C", "file": "css/editor-style.css", "sourcesContent": ["/* @font-face {\r\n    font-family: 'LaGiocondaOSTT';\r\n    src: url('../fonts/la-gioconda-os-tt.eot');\r\n    src: url('../fonts/la-gioconda-os-tt.eot?#iefix') format('embedded-opentype'), url('../fonts/la-gioconda-os-tt.woff2') format('woff2'), url('../fonts/la-gioconda-os-tt.woff') format('woff'), url('../fonts/la-gioconda-os-tt.ttf') format('truetype'), url('../fonts/la-gioconda-os-tt.svg#youworkforthem') format('svg');\r\n    font-weight: normal;\r\n\tfont-style: normal;\r\n\tfont-display: swap;\r\n} */\r\n\r\n$sans: \"ingra\", sans-serif;\r\n$serif: \"LaGiocondaOSTT\", serif;\r\n", "// Colors\r\n$primary: #13212e;    // Replace with primary color variable. ex. $blue\r\n$secondary: #05E5C8;  // Replace with secondary color variable. ex. $red\r\n\r\n$color_list : (\r\n\t'black': #000,\r\n\t'white': #fff,\r\n\t'primary': $primary,\r\n\t'secondary': $secondary,\r\n);\r\n\r\n\r\n// This mixin will generate background-color property for the colors in our list\r\n@mixin background-color($args) {\r\n\tbackground-color: $args;\r\n}\r\n// This mixin will generate color property for the colors in our list\r\n@mixin inner-color($args) {\r\n\tcolor: $args;\r\n}\r\n\r\n@each $name,$color in $color_list {\r\n\t.background-#{$name} { @include background-color($color); }\r\n\t.color-#{$name} { @include inner-color($color); }\r\n}\r\n\r\n.color-inherit {\r\n    color: inherit !important;\r\n}\r\n", "/* @font-face {\n    font-family: 'LaGiocondaOSTT';\n    src: url('../fonts/la-gioconda-os-tt.eot');\n    src: url('../fonts/la-gioconda-os-tt.eot?#iefix') format('embedded-opentype'), url('../fonts/la-gioconda-os-tt.woff2') format('woff2'), url('../fonts/la-gioconda-os-tt.woff') format('woff'), url('../fonts/la-gioconda-os-tt.ttf') format('truetype'), url('../fonts/la-gioconda-os-tt.svg#youworkforthem') format('svg');\n    font-weight: normal;\n\tfont-style: normal;\n\tfont-display: swap;\n} */\n.background-black {\n  background-color: #000;\n}\n\n.color-black {\n  color: #000;\n}\n\n.background-white {\n  background-color: #fff;\n}\n\n.color-white {\n  color: #fff;\n}\n\n.background-primary {\n  background-color: #13212e;\n}\n\n.color-primary {\n  color: #13212e;\n}\n\n.background-secondary {\n  background-color: #05E5C8;\n}\n\n.color-secondary {\n  color: #05E5C8;\n}\n\n.color-inherit {\n  color: inherit !important;\n}\n\n.title {\n  font-family: \"LaGiocondaOSTT\", serif;\n}\n.title--xs {\n  font-size: 16px; /* 16px */\n  font-size: 1rem; /* 16px */\n  line-height: 1.3125; /* 21px */\n}\n.title--sm {\n  font-size: 24px; /* 24px */\n  font-size: 1.5rem; /* 24px */\n  line-height: 1.3333333333; /* 32px */\n}\n.title--md {\n  font-size: 30px; /* 30px */\n  font-size: 1.875rem; /* 30px */\n  line-height: 0.9333333333; /* 28px */\n}\n.title--lg {\n  font-size: 42px; /* 42px */\n  font-size: 2.625rem; /* 42px */\n  line-height: 1.2380952381; /* 52px */\n}\n.title--xl {\n  font-size: 64px; /* 64px */\n  font-size: 4rem; /* 64px */\n  line-height: 1; /* 64px */\n}\n.text {\n  font-family: \"ingra\", sans-serif;\n}\n\n.font-sans {\n  font-family: \"ingra\", sans-serif !important;\n}\n\n.font-serif {\n  font-family: \"LaGiocondaOSTT\", serif !important;\n}\n\n.button, .buttonpdf {\n  position: relative;\n  display: table;\n  border-radius: 33px !important;\n  --tw-bg-opacity: 1;\n  background-color: rgb(2, 30, 66) !important;\n  padding-top: 21px !important;\n  padding-bottom: 21px !important;\n  padding-left: 35px !important;\n  padding-right: 75px !important;\n  font-size: 18px;\n  line-height: 20px;\n  letter-spacing: -0.29px;\n  margin: 0 0 20px 0;\n  color: #fff !important;\n  text-decoration: none;\n}\n\n::-moz-selection { /* Code for Firefox */\n  color: white;\n  background: #05E5C8;\n  padding: 10px;\n}\n\n::selection {\n  color: white;\n  background: #05E5C8;\n  padding: 10px;\n}\n\n#content-wrap {\n  position: relative;\n}\n\na.post-edit-link {\n  position: fixed;\n  z-index: 999;\n  bottom: 0;\n  background: #13212e;\n  color: white;\n  padding: 10px 15px;\n}\n\n.intro {\n  font-size: 18px; /* 18px */\n  font-size: 1.125rem; /* 18px */\n  line-height: 1.3333333333; /* 24px */\n  font-weight: bold;\n}\n\n@media (min-width: 768px) {\n  .split-list {\n    column-count: 2;\n  }\n}\np,\nul,\nol,\nli {\n  font-size: 16px; /* 16px */\n  font-size: 1rem; /* 16px */\n  line-height: 1.5; /* 24px */\n}\np a:not(.button),\nul a:not(.button),\nol a:not(.button),\nli a:not(.button) {\n  color: #05E5C8;\n  transition: color 0.3s linear;\n}\np a:not(.button):hover,\nul a:not(.button):hover,\nol a:not(.button):hover,\nli a:not(.button):hover {\n  color: rgb(3.9102564103, 179.0897435897, 156.4102564103);\n}\n\nul.gallery {\n  width: 100%;\n  padding: 0;\n}\nul.gallery li {\n  display: inline-block;\n  width: 50%;\n  margin: 0;\n  padding-bottom: 20px;\n}\nul.gallery li:nth-child(2n-1) {\n  padding-right: 15px;\n}\nul.gallery li:nth-child(2n) {\n  padding-left: 15px;\n}\n@media (max-width: 575.98px) {\n  ul.gallery li {\n    width: 100%;\n  }\n}\nul.gallery li:before {\n  content: none;\n}\nul.gallery li div.gallery__image {\n  width: 100%;\n  min-height: 250px;\n  max-height: 250px;\n  background-size: cover;\n  background-position: center center;\n  background-repeat: no-repeat;\n  margin: 0;\n}\nul.gallery li p {\n  display: none;\n  margin: 0;\n}\n\nblockquote {\n  position: relative;\n  margin: 65px 0px 60px 0px;\n}\nblockquote p {\n  font-family: \"ingra\", sans-serif;\n  font-size: 24px; /* 24px */\n  font-size: 1.5rem; /* 24px */\n  line-height: 1.25; /* 30px */\n  color: #13212e;\n  margin: 0;\n}\nblockquote cite {\n  font-family: \"ingra\", sans-serif;\n  font-size: 11px; /* 11px */\n  font-size: 0.6875rem; /* 11px */\n  line-height: 1.4545454545; /* 16px */\n  text-transform: uppercase;\n  font-style: normal;\n  letter-spacing: 0.85px;\n  color: #05E5C8;\n}\n\nimg {\n  max-width: 100%;\n  height: auto;\n}\n\n.alignleft {\n  float: left;\n  margin-right: 30px;\n}\n\n.alignright {\n  float: right;\n  margin-left: 30px;\n}\n\n.aligncenter {\n  display: block;\n  margin: 0 auto;\n}\n\n.block {\n  /* ul:not(.slick-dots){\n         padding: 0;\n         li + li{\n             margin-top: 12px;\n         }\n         li{\n  \t\tposition: relative;\n             list-style: none;\n             padding-left: 25px;\n             &:before{\n                 position: absolute;\n                 top: calc(0.3em + 4px);\n                 left: 0px;\n                 content: '';\n                 height: 8px;\n                 width: 8px;\n                 background: $secondary;\n                 border-radius: 50%;\n             }\n         }\n  } */\n}", ".title {\r\n\tfont-family: $serif;\r\n\r\n    &--2xs{\r\n        \r\n    }\r\n    \r\n    &--xs{\r\n        @include font-size(16px, 21px);\r\n    }\r\n    \r\n    &--sm{\r\n        @include font-size(24px, 32px);\r\n    } \r\n    \r\n    &--md{\r\n        @include font-size(30px, 28px);\r\n    } \r\n    \r\n    &--lg{\r\n        @include font-size(42px, 52px);\r\n    } \r\n    \r\n    &--xl{\r\n        @include font-size(64px, 64px);\r\n    }\r\n    \r\n    &--2xl{\r\n        \r\n    }\r\n}\r\n\r\n.text{\r\n\tfont-family: $sans;\r\n}\r\n\r\n// Utilities\r\n.font-sans{\r\n\tfont-family: $sans !important;\r\n}\r\n.font-serif{\r\n\tfont-family: $serif !important;\r\n}\r\n", "@mixin tran-prefix($settings) {\r\n    -webkit-transition: $settings;\r\n    -moz-transition: $settings;\r\n    -ms-transition: $settings;\r\n    -o-transition: $settings;\r\n    transition: $settings;\r\n}\r\n\r\n@mixin transform($attr) {\r\n  -ms-transform: $attr; /* IE 9 */\r\n   \t-webkit-transform: $attr; /* Safari */\r\n   \ttransform: $attr; /* Safari */\r\n}\r\n\r\n@mixin transform-origin($attr) {\r\n  -ms-transform-origin: $attr; /* IE 9 */\r\n   \t-webkit-transform-origin: $attr; /* Safari */\r\n   \ttransform-origin: $attr; /* Safari */\r\n}\r\n@mixin animation($settings) {\r\n    -webkit-animation: $settings;\r\n    -moz-animation: $settings;\r\n    -ms-animation: $settings;\r\n    -o-animation: $settings;\r\n    animation: $settings;\r\n}\r\n\r\n@mixin keyframe($animation_name) {\r\n    @-webkit-keyframes #{$animation_name} {\r\n        @content;\r\n    }\r\n\r\n    @-moz-keyframes #{$animation_name} {\r\n        @content;\r\n    }\r\n\r\n    @-o-keyframes #{$animation_name} {\r\n        @content;\r\n    }\r\n\r\n    @keyframes #{$animation_name} {\r\n        @content;\r\n    }\r\n}\r\n\r\n////Pixels to EM mixin\r\n@function px2rem($px) {\r\n\t@return ($px / $base-font-size) * 1rem;\r\n}\r\n\r\n\r\n\r\n////Font size and line height mixin\r\n@mixin font-size($font-size, $line-height: false) {\r\n\tfont-size: $font-size; /* #{$font-size} */\r\n\tfont-size: px2rem($font-size); /* #{$font-size} */\r\n\r\n\t@if $line-height != false {\r\n\t\tline-height: ($line-height / $font-size); /* #{$line-height} */\r\n\t}\r\n}\r\n\r\n\r\n////ASPECT RATIO\r\n@mixin aspect-ratio($width, $height) {\r\n    position: relative;\r\n    &:before {\r\n        display: block;\r\n        content: \"\";\r\n        width: 100%;\r\n        padding-top: ($height / $width) * 100%;\r\n    }\r\n    > div {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n    }\r\n}\r\n\r\n///ASPECT RATIO WITH CENTERED DIV\r\n@mixin aspect-ratio-center($width, $height) {\r\n    position: relative;\r\n    &:before {\r\n        display: block;\r\n        content: \"\";\r\n        width: 100%;\r\n        padding-top: ($height / $width) * 100%;\r\n    }\r\n    > div {\r\n        position: absolute;\r\n        text-align: center;\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n        background-size: 100vw;\r\n    }\r\n}\r\n\r\n\r\n////CENTERED CONTENT WITHIN RELATIVE PARENT\r\n@mixin centered {\r\n    position: absolute;\r\n    text-align: center;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    background-size: 100vw;\r\n}\r\n\r\n////PSEUDO ELEMENT FOR GRADIENT BACKGROUNDS AND COLOUR OVERLAYS\r\n@mixin pseudo-elem($top, $right, $left, $bottom) {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: $top;\r\n    bottom: $bottom;\r\n    right: $right;\r\n    left: $left;\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n///NORMALISING LISTS\r\n@mixin beauty-lists {\r\n    list-style: none;\r\n    margin: 0;\r\n    padding: 0;\r\n    > li {\r\n        padding: 0;\r\n        text-indent: 0;\r\n    }\r\n}\r\n\r\n", ".button, .buttonpdf {\r\n    position: relative;\r\n    display: table;\r\n    border-radius: 33px !important;\r\n    --tw-bg-opacity: 1;\r\n    background-color: rgb(2 30 66) !important;\r\n    padding-top: 21px !important;\r\n    padding-bottom: 21px !important;\r\n    padding-left: 35px !important; \r\n    padding-right: 75px !important;\r\n    font-size: 18px;\r\n    line-height: 20px;\r\n    letter-spacing: -0.29px;\r\n    margin:0 0 20px 0;\r\n    color:#fff !important;\r\n    text-decoration: none;\r\n\r\n   \r\n}", "// Content styles.\r\n\r\n::-moz-selection { /* Code for Firefox */\r\n\tcolor: white;\r\n\tbackground: $secondary;\r\n\tpadding: 10px;\r\n}\r\n\r\n::selection {\r\n\tcolor: white;\r\n\tbackground: $secondary;\r\n\tpadding: 10px;\r\n}\r\n\r\n\r\n#content-wrap {\r\n    position: relative;\r\n}\r\n\r\n\r\n// Edit page link\r\na.post-edit-link {\r\n\tposition: fixed;\r\n\tz-index: 999;\r\n    bottom: 0;\r\n    background: $primary;\r\n    color: white;\r\n    padding: 10px 15px;\r\n}\r\n\r\n// TYPOGRAPHY\r\n\r\n.intro {\r\n    @include font-size(18px, 24px);\r\n\tfont-weight: bold;\r\n}\r\n\r\n@media( min-width: 768px ){\r\n\t.split-list{\r\n\t\tcolumn-count: 2;\r\n\t}\r\n}\r\n\r\np,\r\nul,\r\nol,\r\nli {\r\n    @include font-size(16px, 24px);\r\n\r\n    a:not(.button) {\r\n        color: $secondary;\r\n        transition: color 0.3s linear;\r\n\r\n        &:hover {\r\n            color: darken($secondary, 10);\r\n        }\r\n    }\r\n}\r\n\r\nul {  \r\n    &.gallery {\r\n        width: 100%;\r\n        padding: 0;\r\n\r\n        li {\r\n            display: inline-block;\r\n            width: 50%;\r\n            margin: 0;\r\n            padding-bottom: 20px;\r\n\r\n            &:nth-child(2n - 1) {\r\n                padding-right: 15px;\r\n            }\r\n\r\n            &:nth-child(2n) {\r\n                padding-left: 15px;\r\n            }\r\n\r\n\t\t\t@media (max-width: 575.98px) {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\r\n            &:before {\r\n                content: none;\r\n            }\r\n\r\n            div.gallery__image {\r\n                width: 100%;\r\n                min-height: 250px;\r\n                max-height: 250px;\r\n                background-size: cover;\r\n                background-position: center center;\r\n                background-repeat: no-repeat;\r\n                margin: 0;\r\n            }\r\n\r\n            p {\r\n                display: none;\r\n                margin: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\nblockquote {\r\n    position: relative;\r\n    margin: 65px 0px 60px 0px;\r\n\r\n    p {\r\n        font-family: $sans;\r\n        @include font-size(24px, 30px);\r\n        color: $primary;\r\n        margin: 0;\r\n    }\r\n\r\n    cite {\r\n        font-family: $sans;\r\n        @include font-size(11px, 16px);\r\n        text-transform: uppercase;\r\n        font-style: normal;\r\n        letter-spacing: 0.85px;\r\n        color: $secondary;\r\n    }\r\n}\r\n\r\n\r\n// Images\r\nimg {\r\n    max-width: 100%;\r\n    height: auto;\r\n}\r\n\r\n\r\n\r\n//ALIGNMENTS\r\n.alignleft {\r\n    float: left;\r\n    margin-right: 30px;\r\n}\r\n\r\n.alignright {\r\n    float: right;\r\n    margin-left: 30px;\r\n}\r\n\r\n.aligncenter {\r\n    display: block;\r\n    margin: 0 auto;\r\n}\r\n\r\n.block{\r\n\t// Custom list dot.\r\n\t/* ul:not(.slick-dots){\r\n        padding: 0;\r\n        li + li{\r\n            margin-top: 12px;\r\n        }\r\n        li{\r\n\t\t\tposition: relative;\r\n            list-style: none;\r\n            padding-left: 25px;\r\n            &:before{\r\n                position: absolute;\r\n                top: calc(0.3em + 4px);\r\n                left: 0px;\r\n                content: '';\r\n                height: 8px;\r\n                width: 8px;\r\n                background: $secondary;\r\n                border-radius: 50%;\r\n            }\r\n        }\r\n\t} */\r\n}\r\n"], "sourceRoot": ""}