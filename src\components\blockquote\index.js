import { $qs, $qsa } from '../../utils/QuerySelector';
import gsap from 'gsap';
import Swiper, { Navigation, Pagination, Scrollbar, Autoplay, FreeMode } from 'swiper';



function blockquote() {
    if ($('[data-element="quote_carousel"]').length) {

   

        $( "[data-element='quote_carousel']" ).each(function( index ) {
        var slidenumb = $(this).find('.swiper-slide').length;
        if(slidenumb == 1) {
            $(this).addClass( "disabled" );
            $(this).removeClass( "swiper" );
        }
        });
        //slider quote_carousel
        var swiper = new Swiper(".swiper[data-element='quote_carousel']", {
            modules: [Navigation, Pagination, Scrollbar, Autoplay],
            loop: true,
            autoplay: {
            delay: 5000,
            },
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
            });
        
    }
    
 }
 
 export default blockquote;
