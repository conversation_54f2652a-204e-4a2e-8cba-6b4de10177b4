<?php
/**
 * Template part to display the block "Video".
 *
 * @package twkmedia
 */

?>
<?php if(get_sub_field('video_id')):?>
<div class="relative mb-10 lg:mb-12">
    <div data-element="play_video" data-video="<?php the_sub_field('video_id');?>" class="relative 
    after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-y-1/2 after:-translate-x-1/2 after:w-[56px] after:h-[56px] lg:after:w-[74px] lg:after:h-[74px] after:bg-play-icon-red after:bg-no-repeat after:bg-contain after:z-20 after:cursor-pointer
    before:content-[''] before:absolute before:bottom-0 before:left-0 before:w-full before:h-[33%] before:bg-gradient-to-b before:from-black/0 before:to-black/50">
    <?php if(get_sub_field('video_mp4_intro')):?>
        <video  class="w-full aspect-[16/9] object-cover" muted autoplay loop>
        <source src="<?php the_sub_field('video_mp4_intro');?>" type="video/mp4">
        </video>
    <?php else: ?>
    <?php 
        $video_thumbnail = get_sub_field('video_thumbnail');
        if( !empty( $video_thumbnail ) ): ?>
            <img class="w-full aspect-[16/9] object-cover" src="<?php echo esc_url($video_thumbnail['url']); ?>" alt="<?php echo esc_attr($video_thumbnail['alt']); ?>" />
        <?php endif; ?>
    <?php endif; ?>
    <?php if(get_sub_field('video_mp4_intro') || get_sub_field('video_thumbnail')):?>
    <?php else: ?>
        <img class="w-full aspect-[16/9] object-cover" src="<?php the_field('video_fallback_image', 'options');?>" alt="<?php the_sub_field('video_title'); ?>" />
    <?php endif; ?>

    


        <p class="absolute bottom-3 left-6 lg:bottom-8 lg:left-8 z-10 font-aspectbold text-copy16 text-white uppercase"><?php the_sub_field('video_title'); ?></p>
    </div>
</div>
<?php endif; ?>


<?php if( have_rows('video_carousel') ): ?>
    <div class="swiper relative mb-10 lg:mb-12" data-element="video_carousel">
        <div class="swiper-wrapper">
        <?php while( have_rows('video_carousel') ): the_row();?>
            <div class="swiper-slide">
                <div data-element="play_video" data-video="<?php the_sub_field('video_id');?>" class="relative 
                after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-y-1/2 after:-translate-x-1/2 after:w-[68px] after:h-[68px] lg:after:w-[74px] lg:after:h-[74px] after:bg-play-icon-red after:bg-no-repeat after:bg-contain after:z-20 after:cursor-pointer
              ">
                <div class="relative before:content-[''] before:absolute before:bottom-0 before:left-0 before:w-full before:h-[30%] before:bg-gradient-to-b before:from-black/0 before:to-black/60">
                    <?php if(get_sub_field('video_mp4_intro')):?>
                        <video  class="w-full aspect-[16/9] object-cover" muted autoplay>
                        <source src="<?php the_sub_field('video_mp4_intro');?>" type="video/mp4">
                        </video>
                    <?php else: ?>
                    <?php 
                        $video_thumbnail = get_sub_field('video_thumbnail');
                        if( !empty( $video_thumbnail ) ): ?>
                            <img class="w-full aspect-[16/9] object-cover" src="<?php echo esc_url($video_thumbnail['url']); ?>" alt="<?php echo esc_attr($video_thumbnail['alt']); ?>" />
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if(get_sub_field('video_mp4_intro') || get_sub_field('video_thumbnail')):?>
                    <?php else: ?>
                        <img class="w-full aspect-[16/9] object-cover" src="<?php the_field('video_fallback_image', 'options');?>" alt="<?php the_sub_field('video_title'); ?>" />
                    <?php endif; ?>

                </div>


                    <p class="absolute bottom-3 left-6 lg:bottom-8 lg:left-8 z-10 font-aspectbold text-copy16 text-white uppercase"><?php the_sub_field('video_title'); ?></p>
                </div>
            </div>
        <?php endwhile; ?>
        </div>
        <div class="swiper-pagination"></div>
    </div>
<?php endif; ?>




