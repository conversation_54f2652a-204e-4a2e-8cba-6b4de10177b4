<?php
/**
 * Template Name: Cranleigh Matters
 *
 * @package twkmedia
 */

get_header();

if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();
		?>

		<?php include locate_template('src/components/banner/banner.php'); ?>
		
		<div class="container px-10 lg:px-0 -mt-[30px] lg:mt-0">

			<div class="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-8 mb-10 lg:mb-12">
				

			<?php
			global $post;

			$myposts = get_posts( array(
				'posts_per_page' => 999,
				'post_type'        => 'cranleigh-matters',
				
			) );

			if ( $myposts ) {
				foreach ( $myposts as $post ) : 
					setup_postdata( $post ); ?>
					
					
				<?php
				endforeach;
				wp_reset_postdata();
			}
			?>
		<?php global $post;

$posts = get_posts( array(
    'post_type' => 'cranleigh-matters',
    'nopaging'  => true,
    'orderby'   => 'date',
    'order'     => 'DESC',
) );

$_year_mon = '';   
$_has_grp = false; 
foreach ( $posts as $post ) {
    setup_postdata( $post );

    $time = strtotime( $post->post_date );
    $year = date( 'Y', $time );
    $mon = date( 'F', $time );
    $year_mon = "$year";

    
    if ( $year_mon !== $_year_mon ) {
        
        
        $_has_grp = true;
		$year_txt = join("<span class='block font-aspectbold text-yellow '>", str_split($year, 2));
        echo '<div class="text-center">';
        echo "<span class='font-aspectregular text-blue text-titlexl'>$year_txt</span>";
		echo "</div>";

    }

    
    if ( $title = get_the_title() ) {?>
        <div class="relative mb-12 lg:mb-24 ">
			<?php if(get_the_post_thumbnail_url()): ?>			
			<img class="aspect-[1] object-cover w-full" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
			<?php else: ?>
				<div class="flex items-center justify-center aspect-[1] border border-dashed border-blue">
					<span class=" font-aspectbold text-titlesidebar uppercase">Coming soon!</span>
				</div>
			<?php endif;?>
			<h2 class="text-titlesidebar uppercase mt-3"><?php the_title(); ?></h2>
			<a class="absolute top-0 left-0 w-full h-full z-20" href="<?php echo get_field('url_matters');?>" target="_blank"></a>
		</div>
    <?php } else {
       
    }

    $_year_mon = $year_mon;
}



wp_reset_postdata(); ?>  



			</div>
		</div>

		
		<div class="mt-32">
			<?php include locate_template('src/components/where_next/where_next.php'); ?>
		</div>

		<?php
	endwhile;
endif;

get_footer();
