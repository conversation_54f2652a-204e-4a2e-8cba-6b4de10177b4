<?php
/**
 * Template part to display the block "Where next".
 *
 * @package twkmedia
 */

?> 
<?php if(!get_field('hide_where_next')):?>
    <div class="w-full overflow-hidden">
        <div class="-rotate-3 pl-6 lg:pl-0 mt-[72px] lg:mt-[80px]">
            <div class="container">
                <h2 class=" font-aspectlight text-[42px] leading-[48px] tracking-[-0.77px] lg:text-[120px] lg:leading-[140px] lg:tracking-[-1.67px] uppercase mb-4 pl-4 lg:pl-14">Where <span class="font-aspectbold">next?</span></h2>
            </div>
            <div data-element="wherenext_wrap">
                <?php if(get_field('select_where_next')):?>
                        <?php $featured_posts = get_field('select_where_next'); ?>
                <?php else: ?>
                    <?php $featured_posts = get_field('select_where_next','options'); ?>
                <?php endif;?>
                <div class="swiper" data-element="where_carousel">
                    <div class="swiper-wrapper pb-8">
                    <?php foreach( $featured_posts as $post ): 
                        setup_postdata($post); ?>
                        <div class="swiper-slide">
                            <div class="relative aspect-[17/11] overflow-hidden
                            after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-[60%] lg:after:h-[40%] after:bg-gradient-to-b after:from-black/0 after:to-black/70 lg:after:to-black/50
                            [&_img]:hover:scale-[1.3]">
                                <?php if(get_the_post_thumbnail_url()): ?>
                                    <?php echo twk_output_featured_image($post->ID, 'large', 'w-full h-full object-cover duration-[0.550s] ease-in-out', 'lazy') ?> 
                                <?php else: ?>
                                    <?php $image = get_field('pages_fallback_image' , 'options'); ?>
                                    <img class="w-full h-full object-cover duration-[0.550s] ease-in-out" src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                                <?php endif; ?>
                                <h3 class="absolute z-10 bottom-5 lg:bottom-8 left-[43px] font-aspectregular text-[30px] leading-[34px] md:text-[34px] md:leading-[36px] lg:text-[40px] lg:leading-[48px] tracking-[-0.97px] lg:tracking-[-1.14px] text-white uppercase "><?php the_title(); ?></h3>
                                <a class="absolute inset-0 h-full w-full z-10" href="<?php the_permalink();?>"></a>
                            </div>
                            
                        </div>
                    <?php endforeach; ?>
                    </div>
                    <div class="container w-full flex lg:justify-end children:bg-pagination-previous-navy children:w-[60px] children:h-[60px] children:lg:w-[65px] children:lg:h-[65px] children:bg-no-repeat children:bg-contain children:indent-[9999px] children:cursor-pointer rotate-3 lg:rotate-0">
                        <div class="swiper-button-prev mr-4"></div>
                        <div class="swiper-button-next rotate-180"></div>
                    </div>
                    
                </div>
                <?php 
                wp_reset_postdata(); ?>
            </div>



        </div>
    </div>
<?php endif; ?>






