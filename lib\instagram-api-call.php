<?php 

/* 
Generate your access token here: https://www.thewebkitchen.co.uk/instagram-token-generator/

Use like the following:

<?php
$feed_name       = get_sub_field( 'instagram_account' ) ? get_sub_field( 'instagram_account' ) : null;
$token           = $feed_name !== null ? get_field( 'instagram_access_token_' . $feed_name, 'option' ) : get_field( 'instagram_access_token_global', 'option' );
$instagram_error = false;

// try to get Instagram data.
try {
	$instagram_data = get_instagram_data( $token, 8, $feed_name );
} catch ( Exception $e ) {
	$instagram_error = $e->getMessage();
}
?>

<?php if ( ! $instagram_error ) : ?>
	<div class="instagram-feed">
		<?php
		foreach ( $instagram_data->data as $instagram_post ) :
			$image_src     = $instagram_post->media_type === 'VIDEO' ? $instagram_post->thumbnail_url : $instagram_post->media_url;
			$image_link    = $instagram_post->permalink;
			$image_caption = $instagram_post->caption;
			?>
			<div class="instagram-feed__post js-touch-trigger">
				<a href="<?php echo $image_link; ?>">
					<img class="" src="<?php echo $image_src; ?>" alt="<?php if ( $image_caption ) : echo mb_strimwidth( $image_caption, 0, 100, "..." ); endif; ?>"/>

					<div class="js-touch-hover">
						<p><?php if ( $image_caption ) : echo mb_strimwidth( $image_caption, 0, 360, "..." ); endif; ?></p>
					</div>
				</a>
			</div>
		<?php endforeach; ?>
	</div>

<?php elseif ( $instagram_error && is_user_logged_in() ) : // if instagram token is broke and user is logged in display error message (rather than just hiding it). ?>
	<section class="error-message">
		<div class="container">
			<div class="row">
				<div class="col-lg-12 content">
					<div class="error-message__container">
						<p><?php echo $instagram_error; ?></p>
						<p>There has been an error trying to get the Instagram posts. This is mostly likely because the access token entered in the backend for the instagram feed is missing or has expired.</p>
						<p>Please generate a new access token by logging in to the correct instagram account through <a href="https://www.thewebkitchen.co.uk/instagram-token-generator/">TWK’s Instagram Token Generator</a> and then input it into the correct custom field <a href="<?php echo home_url(); ?>/wp-admin/admin.php?page=theme-general-settings">here</a>.</p>
						<p class="error-message__info">You are only seeing this message because you are logged in.</p>
					</div>
				</div>
			</div>
		</div>
	</section>
<?php endif; ?>

*/

/**
 * Sets up the instagram related fields on the global settings page in the backend so that the developer doesn't have to.
 *
 * @return void
 */
function setup_instagram_acf_field_group() {
	$feeds = array(
		array(
			'name'     => 'global',
			'nicename' => 'Global',
		),
		/* array(
			'name'     => 'another_instagram_account',
			'nicename' => 'Another Instagram Account',
		), */
	);

	$fields_to_add = array();
	$i             = 0;

	// Add messages so they aren't repeated.
	$fields_to_add[] = array(
		'key'     => 'field_instagram_access_token_message_123456789',
		'label'   => '',
		'name'    => 'instagram_access_token_message',
		'type'    => 'message',
		'message' => 'Generate the Instagram tokens with TWK\'s <a href="https://www.thewebkitchen.co.uk/instagram-token-generator/" target="_blank">Instagram Token Generator</a> and enter them in the fields below for the developer to use to display your Instagram feeds.<br />If your tokens expire, simply generate new tokens and replace them below.',
		'wrapper' => array(
			'width' => '50%',
			'class' => '',
			'id'    => '',
		),
	);
	$fields_to_add[] = array(
		'key'     => 'field_instagram_token_last_refresh_message_123456789',
		'label'   => '',
		'name'    => 'instagram_token_last_refresh__message',
		'type'    => 'message',
		'message' => 'The content in these fields is automatically generated and is used to determine if the Instagram token needs to be refreshed or not.<br />Tokens should be automatically refreshed, so you can ignore this.',
		'wrapper' => array(
			'width' => '50%',
			'class' => '',
			'id'    => '',
		),
	);

	foreach ( $feeds as $feed ) {
		$fields_to_add[] = array(
			'key'     => 'field_instagram_access_token_123456789' . $i,
			'label'   => $feed['nicename'] . ' Access Token',
			'name'    => 'instagram_access_token_' . $feed['name'],
			'type'    => 'text',
			'wrapper' => array (
				'width' => '50%',
				'class' => '',
				'id'    => '',
			),
		);

		$fields_to_add[] = array(
			'key'      => 'field_instagram_token_last_refresh_123456789' . $i,
			'label'    => $feed['nicename'] . ' Last refresh',
			'name'     => 'instagram_token_last_refresh_' . $feed['name'],
			'type'     => 'text',
			'wrapper'  => array(
				'width' => '50%',
				'class' => '',
				'id'    => '',
			),
			'readonly' => true,
		);

		$i++;
	};

	/* if ( current_user_can( 'administrator' ) ) {
		print_r($fields_to_add);
	} */

	acf_add_local_field_group(
		array(
			'key'      => 'group_' . uniqid(),
			'title'    => 'Instagram Data',
			'fields'   => $fields_to_add,
			'location' => array(
				array(
					array(
						'param'    => 'options_page',
						'operator' => '==',
						'value'    => 'theme-general-settings',
					),
				),
			),
		)
	);
}
add_action( 'acf/init', 'setup_instagram_acf_field_group', 5, 1 );



use TWK\InstagramBasicDisplay\InstagramBasicDisplay;

/**
 * Refresh Instagram token
 *
 * @param [type] $instagram_class
 * @param [type] $token
 * @return void
 */
function refresh_token( $instagram_class, $token ) {
	return $instagram_class->refreshToken( $token, true );
}

/**
 * Check token refresh
 *
 * @param [type] $instagram_class
 * @param [type] $token
 * @param [type] $feed_name
 * @return void
 */
function check_token_refresh( $instagram_class, $token, $feed_name ) {

	$date_time_now = date( 'Y-m-d H:i:s' );

	if ( get_field( 'instagram_token_last_refresh_' . $feed_name, 'option' ) ) {

		$last_refresh_date = get_field( 'instagram_token_last_refresh_' . $feed_name, 'option' );

		// date in file is older than 30 days.
		if ( strtotime( $last_refresh_date ) < strtotime( '-1 day' ) ) {
			$refreshed_token = refresh_token( $instagram_class, $token );

			update_field( 'instagram_access_token_' . $feed_name, $refreshed_token, 'option' );
			update_field( 'instagram_token_last_refresh_' . $feed_name, $date_time_now, 'option' );
		}
	} else {
		update_field( 'instagram_token_last_refresh_' . $feed_name, $date_time_now, 'option' );
	}
}

/**
 * Get the instagram data
 *
 * @param [type] $access_token
 * @param [type] $count
 * @param [type] $feed_name
 * @return void
 */
function get_instagram_data( $access_token, $count, $feed_name ) {

	$data_file = 'instagram-data--' . $feed_name . '.json';

	if ( file_exists( $data_file ) && filemtime( $data_file ) > time() - 60 * 30 ) {
		$instagram_data = file_get_contents( $data_file );
		$instagram_data = json_decode( $instagram_data );
	} else {

		$instagram = new InstagramBasicDisplay( $access_token );

		// Get the users profile.
		$instagram_data = $instagram->getUserMedia( 'me', $count );

		// Update the cache file.
		file_put_contents( $data_file, json_encode( $instagram_data ) );

		// Check if the access token needs to be refreshed.
		check_token_refresh( $instagram, $access_token, $feed_name );
	}

	return $instagram_data;
}

