<?php
/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package twkmedia
 */

get_header();

?>

<?php if (have_posts()): ?>

	<?php while (have_posts()): the_post(); ?>

		
	
		
			<section class="bg-blue pt-[170px] lg:pt-[236px] pb-20 lg:pb-[138px] min-h-screen">
			
			<div class="container lg:flex justify-between items-start text-white px-10 lg:px-0">
				<div class="lg:w-6/12">
					<ul class="flex children: children:text-base children:uppercase">
						<li>
						<?php $categories = get_the_category();
							if ( ! empty( $categories ) ):
								echo esc_html( $categories[0]->name );	
							endif; 
						?>
						</li>
						<li class="list-disc ml-[30px] pl-2.5"><?php the_date();?></li>
					</ul>
					<h1 class=" text-title30 lg:text-titleh2 uppercase mt-4 lg:mt-12 mb-5"><?php the_title();?></h1>
					<div class="children:text-[21px] children:leading-[31px] children:lg:text-titlemd"><?php the_excerpt(); ?></div>
				</div>
				<div class="lg:w-5/12 mt-10 lg:mt-0">
					<?php if(get_the_post_thumbnail_url()): ?>
						<img class="aspect-[10/7] w-full object-cover object-center" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
					<?php else: ?>
						<?php 
						$pages_fallback_image = get_field('posts_fallback_image', 'options');
						if( !empty( $pages_fallback_image ) ): ?>
							<img class="aspect-[10/7] w-full object-cover object-center" src="<?php echo esc_url($pages_fallback_image['url']); ?>" alt="<?php echo esc_attr($pages_fallback_image['alt']); ?>" />
						<?php endif; ?>
					<?php endif; ?>
					
					<div class="lg:flex items-center justify-between mt-10 lg:mt-[77px]">
						<div class="flex items-center">
							<h4 class="  text-base uppercase"><?php the_field('share','options');?>:</h4>
							<ul class="flex items-center children:ml-5">
								<li><a href="https://twitter.com/intent/tweet?text=<?php echo urlencode(get_the_title()); ?>+<?php echo get_permalink(); ?>" target="_blank"><?php include locate_template('assets/images/social/twitter.svg');?></a></li>
								<li><a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>&title=<?php the_title(); ?>" target="_blank"><?php include locate_template('assets/images/social/facebook.svg');?></a></li>
								<li><a href="http://www.linkedin.com/shareArticle?mini=true&url=<?php echo urlencode(get_permalink()); ?>&title=<?php echo urlencode(get_the_title()); ?>" target="_blank"><?php include locate_template('assets/images/social/linkedin.svg');?></a></li>
								<li><a href="mailto:?body=<?php echo urlencode(get_permalink()); ?>&title=<?php echo urlencode(get_the_title()); ?>" target="_blank"><?php include locate_template('assets/images/mail-icon-white.svg');?></a></li>

		
							</ul>
						</div>
						<a href="#main" class="hidden lg:table scroll-read button after:rotate-90 hover:after:translate-x-0 hover:after:translate-y-[5px] "><?php the_field('read_article','options');?></a>
					</div>
					
				</div>
				
				
					
					
					
					
				
				
			</div>
			
			
		</section>
		

		<div class="container px-10 lg:px-0">
			<main class="wysiwyg lg:w-8/12 mx-auto mt-16" id="main">
			


			<!-- Flexible Strips -->
			<?php
								
			// check if the flexible content field has rows of data.
			if (have_rows('blocks')):
			
				$blockCount = 0;
			
				// loop through the rows of data.
				while (have_rows('blocks')):
			
					the_row();

					$layout = get_row_layout();
					//$layout = str_replace('_', '-', $layout);
					$blockId = $layout . '-' . $blockCount;

					include locate_template('src/components/' . $layout . '/' . $layout . '.php');
			
					$blockCount++;
			
				endwhile; ?>

			<?php else: ?>

			<?php the_content();?>

			<?php endif;
			
			?>
			<!-- END Flexible Strips -->
			<?php if ( is_singular( 'post' ) ) { ?>
				<?php 
				$link = get_field('back_to_all_news_link','options');
				if( $link ): 
					$link_url = $link['url'];
					$link_title = $link['title'];
					$link_target = $link['target'] ? $link['target'] : '_self';
					?>
					<a class="button pr-[35px] pl-[75px] mx-0 mt-10 after:right-auto after:left-5 after:rotate-180" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
				<?php endif; ?>
			<?php } ?>
			
			

		</main>
		<?php if ( is_singular( 'post' ) ) { ?>
		<div class="container mt-16">
			<h2 class="  text-center text-[40px] lg:text-[100px] lg:leading-[120px] uppercase mb-6 lg:mb-[50px] mt-20 lg:mt-[120px]">Related News</span></h2>
			<div class="container">
				<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
				<?php
				$featured_posts = get_field('related_vacancies');
				if( $featured_posts ): ?>
					<?php foreach( $featured_posts as $post ): 
						setup_postdata($post); ?>
						<div class="relative">
							<?php if(get_the_post_thumbnail_url()): ?>
								<img class="aspect-[10/7] w-full object-cover object-center mb-4" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
							<?php else: ?>
								<?php 
								$pages_fallback_image = get_field('posts_fallback_image', 'options');
								if( !empty( $pages_fallback_image ) ): ?>
									<img class="aspect-[10/7] w-full object-cover object-center mb-4" src="<?php echo esc_url($pages_fallback_image['url']); ?>" alt="<?php echo esc_attr($pages_fallback_image['alt']); ?>" />
								<?php endif; ?>
							<?php endif; ?>
							
							<div class="  text-titlesm uppercase"><?php echo get_the_date(); ?> <?php
                            $cat = null;
                            if (get_post_type() === 'post') :
                                $cat = get_the_category($post->ID);
                            else :
                                //$cat = get_the_terms($post->ID, 'blogcategory');
                            endif;
                            if ($cat) : ?> <span class="mx-1">•</span> <?php echo $cat[0]->name; ?><?php endif; ?></div>
                        	<div class="  text-[28px] leading-[32px] uppercase mt-3"><?php the_title();?></div>
							
							<a class="absolute inset-0 h-full w-full" href="<?php the_permalink();?>"></a>
						</div>
					<?php endforeach; ?>
					<?php 
					wp_reset_postdata(); ?>
			

				<?php else: ?>
					<?php 
					$args = array(
						'post_type' => 'post',
						'posts_per_page'      => 3,
						'post__not_in'           => [get_the_ID()]
					);
					$query = new WP_Query( $args );

					if ($query->have_posts()) : ?>
						<?php 
						while ($query->have_posts()) : $query->the_post();  ?>
						
						<div class="relative">
						<?php if(get_the_post_thumbnail_url()): ?>
								<img class="aspect-[10/7] w-full object-cover object-center mb-4" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
							<?php else: ?>
								<?php 
								$pages_fallback_image = get_field('posts_fallback_image', 'options');
								if( !empty( $pages_fallback_image ) ): ?>
									<img class="aspect-[10/7] w-full object-cover object-center mb-4" src="<?php echo esc_url($pages_fallback_image['url']); ?>" alt="<?php echo esc_attr($pages_fallback_image['alt']); ?>" />
								<?php endif; ?>
							<?php endif; ?>
							<div class="  text-titlesm uppercase"><?php echo get_the_date(); ?> <?php
                            $cat = null;
                            if (get_post_type() === 'post') :
                                $cat = get_the_category($post->ID);
                            else :
                                //$cat = get_the_terms($post->ID, 'blogcategory');
                            endif;
                            if ($cat) : ?> <span class="mx-1">•</span> <?php echo $cat[0]->name; ?><?php endif; ?></div>
                        <div class="  text-[28px] leading-[32px] uppercase mt-3"><?php the_title();?></div>
							
							<a class="absolute inset-0 h-full w-full" href="<?php the_permalink();?>"></a>
						</div>

						<?php endwhile; ?>
					<?php else : ?>
						<div class="text-center">
							<h2 class="">Sorry, no results found</h2>
						</div>
					<?php endif; ?>
				<?php endif; ?>
					
				</div>
			</div>
		</div>
		<?php } ?>
	</div>

	<?php endwhile; ?>
	
<?php endif; ?>

<?php get_footer(); ?>


