import { $qs, $qsa } from '../../utils/QuerySelector';
import gsap from 'gsap';

function closeAllAccordions(accordions) {
    [...accordions].forEach((accordion) => {
        const content = $qs('[data-target="accordion-content"]', accordion);
        accordion.dataset.state = 'closed';
        gsap.to(content, { height: 0, duration: 0.5 });
    });
}

function accordion() {
    const accordions = $qsa('[data-target="accordion"]');

    if (!accordions) return;

    [...accordions].forEach((accordion) => {
        const trigger = $qs('[data-target="accordion-trigger"]', accordion);
        const content = $qs('[data-target="accordion-content"]', accordion);

        trigger.addEventListener('click', () => {
            const { state } = accordion.dataset;

            closeAllAccordions(accordions);

            if (state === 'closed') {
                accordion.dataset.state = 'open';
                gsap.to(content, { height: 'auto', duration: 0.5 });
            } else {
                accordion.dataset.state = 'closed';
                gsap.to(content, { height: 0, duration: 0.5 });
            }
        });
    });
}

export default accordion;