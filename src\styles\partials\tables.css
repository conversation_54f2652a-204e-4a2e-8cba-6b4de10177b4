.tablepress::-webkit-scrollbar,
.table::-webkit-scrollbar {
	@apply h-1.5 rounded-[3px];
}
.tablepress::-webkit-scrollbar-track,
.table::-webkit-scrollbar-track {
	@apply bg-black/10 rounded-[3px];
}
.tablepress::-webkit-scrollbar-thumb,
.table::-webkit-scrollbar-thumb {
	@apply bg-blue rounded-[3px];
}
.tablepress::-webkit-scrollbar-thumb:hover,
.table::-webkit-scrollbar-thumb:hover {
	@apply bg-darkblue;
}

.tablepress,
table.table {
	@apply my-12 max-w-full block overflow-auto;
}

[data-target="accordion"] .tablepress,
[data-target="accordion"] table.table {
	@apply mt-5;
}

.tablepress tfoot th,
table.table tfoot th,
.tablepress thead th,
table.table thead th {
	@apply font-aspectbold text-[16px] leading-[16px] uppercase text-cranleighclay bg-blue border-solid border-r border-white last:border-none p-6 first:rounded-tl-[20px] last:rounded-tr-[20px];
	background: #0c223f !important;
	border-bottom: 0 !important;
}

.tablepress tbody td,
table.table tbody td {
	@apply text-[16px] leading-[16px] text-blue bg-white border-solid border-r border-[#0000001a] last:border-none p-6;
	border-bottom: 0 !important;
	border-top: 0 !important;
	border-left: 0 !important;
}
.tablepress tbody tr:last-child td,
table.table tbody tr:last-child td {
	@apply first:rounded-bl-[20px] last:rounded-br-[20px];
}
.table .odd td {
	background-color: #f9f9f9;
}