import { $qs, $qsa } from '../../utils/QuerySelector';
import gsap from 'gsap';
import ScrollTrigger from "gsap/ScrollTrigger";
import DrawSVGPlugin from "gsap/DrawSVGPlugin";
gsap.registerPlugin(ScrollTrigger, DrawSVGPlugin);

function history() {
    if($('#svg_line_history').length) {
    $( window ).on("load", function() {
        var heightsection = $('#svg_line_history').parent().height() + 160;
        $('#svg_line_history path').attr("d", "M0 0 v" + heightsection + " 0");

       
        gsap.set('#svg_line_history path',{drawSVG:"0% 0%"});
        gsap.to('#svg_line_history path',{
            scrollTrigger: {
                trigger: '#svg_line_history',
                start: 'top 80%',
                end:"bottom 80%",
                scrub: 1
            },
            ease: "none",
            drawSVG:"0% 100%",
            
        });

        $("[data-element='century']").each(function() {
            var $this = $(this);
          
            gsap.from($this, {
                scrollTrigger: {
                  trigger: $this,
                  start: 'top 50%',
                  scrub:1,
                  end:200,
                },
                autoAlpha:0,
                scale:0.9,
                y:"-=50px"
            });
          
        });

        $("[data-element='year']").each(function() {
            var $this = $(this).parent();
            var $thisnext = $(this).next();
            
            gsap.from($(this), {
                scrollTrigger: {
                  trigger: $this,
                  start: 'top 80%',
                  scrub:1,
                  end:40,
                },
                autoAlpha:0,
                x:"-=100%"
            });
            gsap.from($thisnext, {
                scrollTrigger: {
                  trigger: $this,
                  start: 'top 80%',
                  scrub:1,
                  end:80,
                },
                autoAlpha:0,
                x:"+=50px"
            });
        });

        
    
       

       

    });

}
   
}

export default history;