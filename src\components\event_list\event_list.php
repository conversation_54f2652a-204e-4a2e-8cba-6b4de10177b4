<?php
/**
 * Template part to display the block "Event list".
 *
 * @package twkmedia
 */

?>

<h2 class=" text-[28px] leading-[36px] tracking-[-0.75px] lg:text-titleh2 uppercase mb-6 lg:mb-10 mt-10 lg:mt-14"><?php echo get_sub_field('title');?></h2>
<div class="[&>div:nth-child(3)]:border-none">

<?php $manuallyselected = get_sub_field('select_events');?>
<?php $select_3_custom_events = get_sub_field('select_3_custom_events');?>



<?php if($select_3_custom_events):?>
			
				
            <?php foreach( $manuallyselected as $post ): 

                // Setup this post for WP functions (variable must be named $post).
                setup_postdata($post); ?>
                <div class="relative lg:flex items-start justify-between mb-[22px] pb-[22px] lg:mb-[44px] lg:pb-[44px] border-b border-blue">
                    <div class="lg:w-5/12">
                        <?php if(get_the_post_thumbnail_url()): ?>
                            <img class="aspect-[3/2] object-cover" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
                        <?php else: ?>
                            <img class="aspect-[3/2] object-cover" src="<?php the_field('events_fallback_image', 'options'); ?>" alt="<?php the_title();?>" />
                        <?php endif; ?>
                    </div>
                    <div class="lg:w-6/12">
                        <h2 class="  text-titlemd lg:text-titleh4 uppercase mb-3 mt-2 lg:mt-6"><?php the_title(); ?></h2>
                        <span class="block  text-titlesm lg:text-copy19 uppercase"><?php the_field('cranevent_start_date');?> <?php if(get_sub_field('cranevent_start_date') != get_sub_field('cranevent_end_date')):?>&bullet; <?php the_field('cranevent_end_date');?><?php endif;?></span>
                        
                        <?php
                                    $cat = get_the_terms($post->ID, 'event-type');
                                    if ($cat) : ?><span class="button mt-3 lg:mt-9">Learn more</span><?php endif; ?>
                        <a class="get_popupevent  absolute top-0 left-0 w-full h-full z-20 pointer-events-none" href="<?php the_permalink(); ?>" target="_self" data-postid="<?php echo get_the_ID();?>"></a>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <?php 
            // Reset the global post object so that the rest of the page works correctly.
            wp_reset_postdata(); ?>

    <?php else: ?>

<?php
$taxonomy_terms = get_sub_field('select_category');

$today = date('Ymd');
global $post;
if( $taxonomy_terms) {
    $args = array(
    'post_type' => 'cran-event',
    'posts_per_page'      => 3,
    'meta_key'          => 'cranevent_end_date',
    'orderby'           => 'meta_value',
    'order'             => 'ASC',
    'meta_query'    => array(
        array(
            'key'       => 'cranevent_end_date',
            'compare'   => '>=',
            'value'     => $today,
        ),
        
    ),
    'tax_query' => array(
        array(
            'taxonomy' => 'event-type', 
            'field'    => 'id',
            'terms'    => $taxonomy_terms,
            'operator'  => 'IN'
        ),
    )

);
} else {
    $args = array(
        'post_type' => 'cran-event',
        'posts_per_page'      => 3,
        'meta_key'          => 'cranevent_end_date',
        'orderby'           => 'meta_value',
        'order'             => 'ASC',
        'meta_query'    => array(
            array(
                'key'       => 'cranevent_end_date',
                'compare'   => '>=',
                'value'     => $today,
            ),
            
        )
        
    );
}


$query_event = new WP_Query($args);

if($query_event->have_posts()) : while($query_event->have_posts()) : $query_event->the_post(); 

?>
        
<div class="relative lg:flex items-start justify-between mb-[22px] pb-[22px] lg:mb-[44px] lg:pb-[44px] border-b border-blue">
    <div class="lg:w-5/12">
        <?php if(get_the_post_thumbnail_url()): ?>
            <img class="aspect-[3/2] object-cover" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
        <?php else: ?>
            <img class="aspect-[3/2] object-cover" src="<?php the_field('events_fallback_image', 'options'); ?>" alt="<?php the_title();?>" />
        <?php endif; ?>
    </div>
    <div class="lg:w-6/12">
        <h2 class="  text-titlemd lg:text-titleh4 uppercase mb-3 mt-2 lg:mt-6"><?php the_title(); ?></h2>
        <span class="block  text-titlesm lg:text-copy19 uppercase"><?php the_field('cranevent_start_date');?> <?php if(get_sub_field('cranevent_start_date') != get_sub_field('cranevent_end_date')):?>&bullet; <?php the_field('cranevent_end_date');?><?php endif;?></span>
        
        <?php
                    $cat = get_the_terms($post->ID, 'event-type');
                    if ($cat) : ?><span class="button mt-3 lg:mt-9">Learn more</span><?php endif; ?>
        <a class="get_popupevent  absolute top-0 left-0 w-full h-full z-20 pointer-events-none" href="<?php the_permalink(); ?>" target="_self" data-postid="<?php echo get_the_ID();?>"></a>
    </div>
</div>

<?php 

endwhile; endif; wp_reset_postdata();

?>

<?php endif; ?>
</div>