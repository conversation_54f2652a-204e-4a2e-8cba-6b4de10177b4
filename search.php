<?php
/**
 * The template for displaying search results pages.
 *
 * @package twkmedia
 */

get_header();

?>

<!-- BANNER -->
<section class="bg-blue pt-[170px] pb-[50px] lg:pb-20 mb-14">
    <div class="container text-center">
            <span class="font-aspectbold text-white text-base uppercase mb-6">Search results</span>
                        <h1 class="font-aspectbold text-white text-title46 lg:text-titlelg uppercase mt-6"><?php echo 'Your search results for "' . sanitize_text_field( get_query_var( 's' ) ) . '"'; ?></h1>
                
            </div>
</section>


<div class="container">
	<div class="row">
		<div class="col-md-8 offset-md-2">

			<?php
			if ( have_posts() ) :
				while ( have_posts() ) :
					the_post();
					?>

					<article <?php post_class( 'my-10 mb-20' ); ?> id="post-<?php the_ID(); ?>">
						<div class="container">
							<div class="w-8/12 mx-auto flex justify-between">
								
								<div class="flex justify-between w-full">
									<div class="hidden lg:block lg:w-3/12">
										
									<?php 
									$background_image_topbanner = get_field('background_image_topbanner');
									if( !empty( $background_image_topbanner ) ): ?>
										<img class="w-full object-cover" src="<?php echo esc_url($background_image_topbanner['url']); ?>" alt="<?php echo esc_attr($background_image_topbanner['alt']); ?>" />
										<?php elseif (has_post_thumbnail( $post->ID ) ): ?>
										
										<?php $image = wp_get_attachment_image_src( get_post_thumbnail_id( $post->ID ), 'single-post-thumbnail' ); ?>
										<img class="w-full object-cover" src="<?php echo $image[0]; ?>" alt="search" />
									<?php else: ?>
										
										<?php 
										$pages_fallback_image = get_field('pages_fallback_image', 'options');
										if( !empty( $pages_fallback_image ) ): ?>
											<img class="w-full object-cover" src="<?php echo esc_url($pages_fallback_image['url']); ?>" alt="<?php echo esc_attr($pages_fallback_image['alt']); ?>" />
										<?php endif; ?>
									<?php endif;?>
									</div>
									<div class="w-full lg:w-8/12">
										<?php if ( get_post_type( get_the_ID() ) == 'cranleigh-matters' )  {  ?>
											<a href="<?php echo get_field('url_matters');?>">
												<h2 class="font-futura text-titlemd uppercase mt-4 mb-4"><?php the_title(); ?></h2>
											</a>
											<div class="text-copy16 md:text-base"><?php the_excerpt(); ?></div>
											<a class="button table" href="<?php echo get_field('url_matters');?>">Learn more</a>
										<?php } else if ( get_post_type( get_the_ID() ) == 'policy' )  { ?>
											<?php
											$file = get_field('file');
											if( $file ): ?>
											<a href="<?php echo $file['url']; ?>">
												<h2 class="font-futura text-titlemd uppercase mt-4 mb-4"><?php the_title(); ?></h2>
											</a>
											<div class="text-copy16 md:text-base"><?php the_excerpt(); ?></div>
											<a class="button table" href="<?php echo $file['url']; ?>">Learn more</a>
											<?php endif; ?>

										<?php } else if ( get_post_type( get_the_ID() ) == 'sport' )  { ?>
											<a href="/school-life/co-curricular/sport/">
												<h2 class="font-futura text-titlemd uppercase mt-4 mb-4"><?php the_title(); ?></h2>
											</a>
											<div class="text-copy16 md:text-base"><?php the_excerpt(); ?></div>
											<a class="button table" href="/school-life/co-curricular/sport/">Learn more</a>
										
										<?php } else if ( get_post_type( get_the_ID() ) == 'department' )  { ?>
											<a href="/school-life/academics/departments/">
												<h2 class="font-futura text-titlemd uppercase mt-4 mb-4"><?php the_title(); ?></h2>
											</a>
											<div class="text-copy16 md:text-base"><?php the_excerpt(); ?></div>
											<a class="button table" href="/school-life/academics/departments/">Learn more</a>											
										<?php } else { ?>
											<a href="<?php the_permalink(); ?>">
												<h2 class="font-futura text-titlemd uppercase mt-4 mb-4"><?php the_title(); ?></h2>
											</a>
											<div class="text-copy16 md:text-base"><?php the_excerpt(); ?></div>
											<a class="button table" href="<?php the_permalink(); ?>">Learn more</a>
										<?php } ?>
									</div>
								</div>
							</div>
						</div>
					</article>

				<?php endwhile; ?>


			<?php else : ?>

				<h2 class="title title--medium">No results found.</h2>

			<?php endif; ?>

		</div>
	</div>

	<div class="container">
		<div class="row justify-content-center">
			<div class="col-md-8">
				<?php require locate_template( 'tpl/parts/_pagination.php' ); ?>
			</div>
		</div>
	</div>
</div>

<?php
get_footer();
