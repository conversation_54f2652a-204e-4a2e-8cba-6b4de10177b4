
<div class="container flex justify-between items-start flex-wrap lg:mt-24 px-6 lg:px-0
    [&:nth-child(2)]:children:lg:w-4/12 
    [&:nth-child(2)]:children:mt-14 
    [&:nth-child(2)]:children:lg:mt-28

    [&:nth-child(1)]:children:lg:w-7/12 
    [&_.box]:[&:nth-child(1)]:children:lg:w-4/5 
    [&_.box]:[&:nth-child(1)]:children:bg-white 
    [&_.box]:[&:nth-child(1)]:children:block 
    [&_.box]:[&:nth-child(1)]:children:relative 
    [&_.box]:[&:nth-child(1)]:children:lg:absolute 
    [&_.box]:[&:nth-child(1)]:children:bottom-0 
    [&_.box]:[&:nth-child(1)]:children:right-0 
    [&_.box]:[&:nth-child(1)]:children:lg:pl-12 
    [&_.box]:[&:nth-child(1)]:children:lg:pt-12
    [&:nth-child(1)]:children:mt-10 
    [&:nth-child(1)]:children:lg:mt-0

    [&:nth-child(3)]:children:mt-10 [&:nth-child(3)]:children:lg:mt-24 
    [&:nth-child(3)]:children:lg:w-5/12 
    [&:nth-child(3)]:children:lg:ml-[16%]"
>
   
   <?php 
        $select_news_newstab  = get_field('select_news_newstab');

        foreach( $select_news_newstab as $select_news_newstab_post ): 
            $permalink = get_permalink( $select_news_newstab_post->ID );
            $title = get_the_title( $select_news_newstab_post->ID );
            $date = get_the_date( 'j F Y', $select_news_newstab_post->ID );
            $custom_field = get_field( 'field_name', $select_news_newstab_post->ID );
            $cat = get_the_category($select_news_newstab_post->ID);
    ?>
            <div class="mt-10 lg:mt-0" data-anim="fade">
                <a href="<?= $permalink;?>" class="relative">
                    <div class="children:aspect-[3/2] [&_img]:aspect-[3/2] [&_img]:object-cover children:object-cover children:object-center relative flex items-end mb-4">
                        <?php echo twk_output_featured_image_with_fallback($post->ID, 'large', '', 'news') ?>  
                    </div>
                    <div class="box">
                        <div class=" font-aspectbold text-[12px] lg:text-titlesm uppercase ">
                            <?= $date; ?> 
                            <?php
                                $cat = null;
                                $cat = get_the_category($select_news_newstab_post->ID);
                                if ($cat) : ?> • <?php echo $cat[0]->name; ?><?php endif; 
                            ?>
                        </div>
                        <div class=" font-aspectbold text-titlemd uppercase mt-3 lg:text-[40px] lg:leading-[48px]"><?= $title; ?></div>
                    </div>
                </a>
            </div>
        <?php endforeach;  ?>

    <?php 
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => 3,
    );
    $query = new WP_Query( $args );

    if ($query->have_posts()) : 
    ?>
        <?php while ($query->have_posts()) : $query->the_post();  ?>
        <div class="mt-10 lg:mt-0" data-anim="fade">
            <a href="<?php echo get_permalink($post->ID); ?>" class=" ">
                <div class="children:aspect-[3/2] [&_img]:aspect-[3/2] [&_img]:object-cover children:object-cover children:object-center relative flex items-end mb-4">
                    <?php echo twk_output_featured_image_with_fallback($post->ID, 'large', '', 'news') ?>  
                </div>
                <div class="box">
                    <div class=" font-aspectbold text-titlesm uppercase"><?php echo get_the_date(); ?> <?php
                        $cat = null;
                        if (get_post_type() === 'post') :
                            $cat = get_the_category($post->ID);
                        else :
                            //$cat = get_the_terms($post->ID, 'blogcategory');
                        endif;
                        if ($cat) : ?> • <?php echo $cat[0]->name; ?><?php endif; ?></div>
                    <h2 class="text-titlesidebar uppercase mt-1.5 lg:mt-3  text-[40px] leading-[48px]"><?php the_title();?></h2>
                </div>
            </a>
        </div>
        <?php endwhile; ?>
            
    <?php endif; ?>
    <div class="bg-grey py-[47px] px-10 lg:w-4/12 mt-10 lg:mt-24">
        <h3 class="text-[28px] lg:text-titlesidebar uppercase mb-6"><?php the_field('follow_us','options');?></h3>
        <ul class="flex justify-between flex-wrap children:w-5/12 children:flex children:justify-start children:items-center">
            <li class="mb-6 md:mb-4 xl:mb-6"><div class="w-5 children:w-full"><?php include locate_template('assets/images/social/x-red.svg');?></div><a href="<?php the_field('twitter_url','options');?>" target="_blank" class="text-[0px] md:text-[13px] xl:text-copy16 ml-2 ">Cranleighschool</a></li>
            <li class="mb-6 md:mb-4 xl:mb-6"><div class="w-5 children:w-full"><?php include locate_template('assets/images/social/instagram-red.svg');?></div><a href="<?php the_field('instagram_url','options');?>" target="_blank" class="text-[0px] md:text-[13px] xl:text-copy16 ml-2 ">Cranleighschool</a></li>
            <li class="mb-6 md:mb-4 xl:mb-6"><div class="w-5 children:w-full"><?php include locate_template('assets/images/social/facebook-red.svg');?></div><a href="<?php the_field('facebook_url','options');?>" target="_blank" class="text-[0px] md:text-[13px] xl:text-copy16 ml-2 ">Cranleighschool</a></li>
            <li class="mb-6 md:mb-4 xl:mb-6"><div class="w-5 children:w-full"><?php include locate_template('assets/images/social/linkedin-red.svg');?></div><a href="<?php the_field('linkedin_url','options');?>" target="_blank" class="text-[0px] md:text-[13px] xl:text-copy16 ml-2 ">Cranleighschool</a></li>
        </ul>
    </div>
</div>