<?php // News 
?>
<div class="-ml-4 -mr-4">
    <?php if ($query->have_posts()) : ?>
        <div class="flex flex-wrap">
            <?php $i = 0;
            while ($query->have_posts()) : $query->the_post();  ?>

                <div class="w-full lg:w-1/3 mb-14 px-4">
                    <a href="<?php echo get_permalink($post->ID); ?>" class=" ">
                        <div class="children:aspect-[3/2] [&_img]:aspect-[3/2] [&_img]:object-cover children:object-cover children:object-center relative flex items-end mb-4">
                            <?php if(get_the_post_thumbnail_url()): ?>
                                <?php echo twk_output_featured_image_with_fallback($post->ID, 'large', '', 'news') ?>  
                            <?php else: ?>
                                <?php 
                                $pages_fallback_image = get_field('posts_fallback_image', 'options');
                                if( !empty( $pages_fallback_image ) ): ?>
                                    <img class="" src="<?php echo esc_url($pages_fallback_image['url']); ?>" alt="<?php echo esc_attr($pages_fallback_image['alt']); ?>" />
                                <?php endif; ?>
                            <?php endif; ?>
                            
                        </div>
                        <div class="  text-titlesm uppercase"><?php echo get_the_date(); ?> <?php
                            $cat = null;
                            if (get_post_type() === 'post') :
                                $cat = get_the_category($post->ID);
                            else :
                                //$cat = get_the_terms($post->ID, 'blogcategory');
                            endif;
                            if ($cat) : ?> <span class="mx-1">•</span> <?php echo $cat[0]->name; ?><?php endif; ?></div>
                        <div class="  text-[28px] leading-[32px] uppercase mt-3"><?php the_title();?></div>
                    </a>
                </div>
                <?php if ($i == 1) : ?>
                    <?php $currentpage = $_GET['sf_paged']; ?>
                    <?php $filteron =  $_GET['_sft_category'] ?>
                    <?php if($currentpage < 2 & $filteron == ""):?>
                        <div class="w-full lg:w-1/3 mb-14 px-4">
                        <?php
                        $feed_name       = get_sub_field('instagram_account') ? get_sub_field('instagram_account') : null;
                        $token           = $feed_name !== null ? get_field('instagram_access_token_' . $feed_name, 'option') : get_field('instagram_access_token_global', 'option');
                        $instagram_error = false;

                        // try to get Instagram data.
                        try {
                            $instagram_data = get_instagram_data($token, 3, $feed_name);
                        } catch (Exception $e) {
                            $instagram_error = $e->getMessage();
                        }

                        ?>

                            
                        <div class="relative swiper" data-element="instagram_carousel">
                            <div class="absolute left-5 bottom-5 flex items-center z-10">
                                <div class="w-5 children:w-full"><?php include locate_template('assets/images/social/instagram.svg');?></div><span class=" text-titlesm text-white uppercase ml-2">cranleighschool</span>
                            </div>
                            <div class="swiper-wrapper">
                                <?php if (!$instagram_error) : ?>
                                    <?php
                                    foreach ($instagram_data->data as $instagram_post) :
                                        $image_src     = $instagram_post->media_type === 'VIDEO' ? $instagram_post->thumbnail_url : $instagram_post->media_url;
                                        $image_link    = $instagram_post->permalink;
                                        $image_caption = $instagram_post->caption;
                                    ?>
                                        <div class="swiper-slide relative 
                                        after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-[25%] after:bg-gradient-to-b after:from-black/0 after:to-black/50">
                                            <?php echo $instagram_account;?>
                                            <a class="aspect-[1/1] w-full h-full block" href="<?php echo $image_link; ?>">
                                                <img class="object-cover w-full h-full" src="<?php echo $image_src; ?>" alt="<?php if ($image_caption) : echo mb_strimwidth($image_caption, 0, 100, "..."); endif; ?>" />
                                            </a>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: echo 'error'; endif;?>
                            </div>
                            <div class="swiper-pagination"></div>
                        </div>
                        
                        
                    </div>
                    <?php endif;?>
                <?php endif; ?>

                

                <?php $i++;
                endwhile; ?>

                <?php
                $total_pages = $query->max_num_pages;
                $paged = isset($_GET['sf_paged']) ? $_GET['sf_paged'] : 1;

                ?>
                <div class="w-full mt-10 lg:mt-20 mb-20">
                        <div class="flex justify-center text-black">

                        <?php require locate_template( 'tpl/parts/_pagination.php' ); ?>
                    </div>
                </div>
                  
                </div>
            </div>
        </div>




    <?php else : ?>
        <div class="text-center">
            <h2 class=" text-blue uppercase text-title30 lg:text-title mt-12 mb-3 ">Sorry, no results found</h2>
        </div>
    <?php endif; ?>
</div>