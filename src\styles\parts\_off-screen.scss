.off-screen {
    //@include transform(translate(0,0));
    opacity: 1;
    &.off-screen--from-left,
    &.off-screen--from-right,
    &.off-screen--fade {
        @include tran-prefix(all 0.4s ease-out);
    }
}

.off-screen--fade-up {
    @include transform(translateY(0));
    opacity: 1;
    @include tran-prefix(all 0.4s ease-out);
    &.off-screen--hide {
        @include transform(translateY(30px));
        opacity: 0;
    }
}

.off-screen--from-left {
    @include transform(translateX(0));
    opacity: 1;
    @include tran-prefix(all 0.4s ease-out);
    &.off-screen--hide {
        @include transform(translateX(70px));
        opacity: 0;
    }
}

.off-screen--hide {
    &.off-screen--from-left {
        @include transform(translateX(-150px));
        opacity: 0;
    }
    &.off-screen--from-right {
        @include transform(translateX(150px));
        opacity: 0;
    }
    &.off-screen--from-bottom {
        @include transform(translateY(150px));
        opacity: 0;
    }
    &.off-screen--fade {
        opacity: 0;
    }
}

.off-screen--alternate {
    &>div>div {
        @include transform(translate(0, 0));
        opacity: 1;
        @include tran-prefix(all 0.4s ease-out);
    }
    &>div.off-screen--hide {
        &:nth-child(even) {
            div:nth-child(1) {
                @include transform(translateX(150px));
                opacity: 0;
            }
            div:nth-child(2) {
                @include transform(translateX(-150px));
                opacity: 0;
            }
        }
        &:nth-child(odd) {
            div:nth-child(1) {
                @include transform(translateX(-150px));
                opacity: 0;
            }
            div:nth-child(2) {
                @include transform(translateX(150px));
                opacity: 0;
            }
        }
    }
}

.off-screen--sequential {
    &>* {
        @include transform(translateY(0));
        opacity: 1;
        @include tran-prefix(all 0.3s ease-out);
    }
    &.off-screen--hide {
        &>* {
            @include transform(translateY(30px));
            opacity: 0;
        }
    }
}

$elements: 15;  
@for $i from 0 to $elements {
    .off-screen--sequential {
		& > * {
			&:nth-child(#{$i + 1}) {
			transition-delay: (0.2s * $i);
			}
		}
    }
}