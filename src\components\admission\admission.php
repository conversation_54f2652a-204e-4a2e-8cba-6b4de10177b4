<?php
/**
 * Template part to display the admission tpl.
 *
 * @package twkmedia
 */

$featured_posts = get_field('admission_menu_admissiontpl');
?>

<?php $thispageid = get_the_ID(); ?>

		<div class="relative bg-blue">
			<div class="container pb-16 flex text-white px-6 lg:px-0" data-element="admission_wrap">
				<div class="lg:w-6/12 lg:pr-8">
					<h1 class=" text-title lg:text-titleh2 uppercase mt-[130px] lg:mt-[190px]"><?php the_field('title_admissiontpl');?></h1>
					<p class="leading-[30px] lg:text-titlemd mt-6"><?php the_field('intro_copy_admissiontpl');?></p>
					<div class="mt-10 mb-10 children:text-titlesm children:inline-table children:mr-4 children:mb-4 last:children:mr-0">
						<?php
						if( $featured_posts ): ?>
							<?php foreach( $featured_posts as $post ): 
								setup_postdata($post); ?>
								<a class="<?php if($post->ID == $thispageid): echo 'button'; else: echo 'button-white'; endif;?> py-3 px-[22px] text-titlesm after:content-none" href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
								
							<?php endforeach; ?>
							<?php 
							wp_reset_postdata(); ?>
						<?php endif; ?>
					</div>
					<div data-element="admission_step_wrap">
						<?php $countstage = 1; ?>
						<?php if( have_rows('stages_admissiontpl') ): ?>
							<?php while( have_rows('stages_admissiontpl') ): the_row();?>
								<div class="bg-white text-blue px-9 py-8 mb-6 lg:mb-12 last:mb-0" data-element="admission_step" data-count="<?= $countstage;?>">
									<span class=" text-titlesm lg:text-copy16 uppercase mb-2">Stage <?= $countstage; ?></span>
									<h3 class=" text-title30 lg:text-title uppercase mb-3"><?php echo get_sub_field('title');?></h3>
									<div class="wysiwyg"><?php echo get_sub_field('copy');?></div>
								</div>
								
							<?php $countstage++; endwhile; ?>
						<?php endif; ?>
					</div>


				</div>
				
			</div>
			<?php if( have_rows('stages_admissiontpl') ): ?>
			<div class="hidden absolute lg:flex items-end  w-[46%] h-screen top-0 right-0 " data-element="admission_img_wrap">
				<div class="absolute top-0 right-0 w-full h-full 
				after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-1/3 after:bg-gradient-to-b after:from-black/0 after:to-black/70">
					<?php if( have_rows('stages_admissiontpl') ): ?>
						<?php $countstage = 1; ?>
						<?php while( have_rows('stages_admissiontpl') ): the_row();?>
							<?php 
							$image = get_sub_field('image');
							if( !empty( $image ) ): ?>
								<img data-element="admission_img" data-count="<?= $countstage;?>" class="absolute top-0 right-0 w-full h-full object-cover object-center rounded-tl-[600px] opacity-0" src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
							<?php endif; ?>
						<?php $countstage++; endwhile; ?>
					<?php endif; ?>
				</div>
				<div class="absolute z-10 right-14 top-1/2 -translate-y-1/2">
					<svg id="svg_line_white" version="1.1" class="absolute top-0 left-[calc(50%_+_2px)] mr-px -translate-x-1/2 w-2 h-full" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" xml:space="preserve">
						<path class="path-vert" fill="#fff" stroke-width="5" stroke="#fff" d="M0 0 v6000 0"/>
					</svg>
					<svg id="svg_line_blue" version="1.1" class="absolute top-0 left-[calc(50%_+_2px)] -translate-x-1/2 w-2 h-full" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" xml:space="preserve">
						<path class="path-vert" fill="#0C223F" stroke-width="5" stroke="#0C223F" d="M0 0 v6000 0"/>
					</svg>
					<div class="relative z-10 ">
						<?php if( have_rows('stages_admissiontpl') ): ?>
							<?php $countstage = 1; ?>
							<?php while( have_rows('stages_admissiontpl') ): the_row();?>

								<span data-element="admission_step_n" data-count="<?= $countstage;?>" class="flex w-9 h-9 text-[18px] items-center justify-center  text-center text-blue bg-white rounded-full mb-[28px] last:mb-0 cursor-pointer"><?= $countstage;?></span>
							<?php $countstage++; endwhile; ?>
						<?php endif; ?>
					</div>
				</div>
				<div class="container pr-14 ">
					<div class="pb-16">
						<div class="flex justify-end">
							<?php 
							$link = get_field('book_a_visit_admissiontpl');
							if( $link ): 
								$link_url = $link['url'];
								$link_title = $link['title'];
								$link_target = $link['target'] ? $link['target'] : '_self';
								?>
								<a class="button-white mr-6" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
							<?php endif; ?>
							<?php 
							$link = get_field('apply_now_admissiontpl');
							if( $link ): 
								$link_url = $link['url'];
								$link_title = $link['title'];
								$link_target = $link['target'] ? $link['target'] : '_self';
								?>
								<a class="button" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
							<?php endif; ?>

						</div>
					</div>
				</div>
			</div> 
			<?php endif; ?>
		</div>
		
		
		
		

		<?php if(get_field('vimeo_id_admissiontpl')):?>
		<div class="container pt-10 pb-7 lg:pb-28">
			<h3 class=" text-[56px] md:text-[86px] lg:text-[124px] lg:leading-[150px] xl:text-titlexl uppercase ml-10 translate-y-[10px] lg:translate-y-[70px]"><?php the_field('video_title_1_admissiontpl');?></h3>
			<div class="relative w-10/12 mx-auto mb-12 ">
				<div data-element="play_video" data-video="<?php the_field('vimeo_id_admissiontpl');?>" class="relative cursor-pointer 
				after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-y-1/2 after:-translate-x-1/2 after:w-[62px] after:h-[62px] after:bg-play-icon-red after:bg-no-repeat after:bg-contain after:z-20
				before:content-[''] before:absolute before:w-full before:h-full before:bg-gradient-to-b before:from-white/0 before:to-white/50">
				<?php if(get_field('video_mp4_intro')):?>
					<video  class="w-full aspect-[16/9] object-cover" muted autoplay>
					<source src="<?php the_field('video_mp4_admissiontpl');?>" type="video/mp4">
					</video>
				<?php else: ?>
				<?php 
					$video_thumbnail = get_field('video_thumbnail_admissiontpl');
					if( !empty( $video_thumbnail ) ): ?>
						<?php echo twk_output_acf_image($video_thumbnail, 'large', 'w-full aspect-[16/9] object-cover' , 'lazy'); ?>
					<?php endif; ?>
				<?php endif; ?>
					<p class="absolute bottom-8 left-8 z-10  text-copy16 text-white uppercase"><?php the_field('video_title'); ?></p>
				</div>
			</div>
			<h3 class=" text-[56px] md:text-[86px] lg:text-[124px] lg:leading-[150px] xl:text-titlexl uppercase float-right mr-10 -translate-y-[50px] lg:-translate-y-[100px]"><?php the_field('video_title_2_admissiontpl');?></h3>
		</div>
		<?php endif;?>
		
		
		<?php
		$featured_posts = get_field('select_staff');
		if( $featured_posts ): ?>
		<div class="bg-blue pt-6 pb-9 lg:pt-[54px] lg:pb-8">
			<div class="container lg:flex px-10 lg:px-0">
			
				<div class="lg:w-3/12 mb-10 lg:mb-0">
                    <h2 class=" text-titlesidebar leading-[36px] xl:text-title text-white uppercase translate-y-5"><?php the_field('meet_staff_title_admissiontpl');?></h2>
                </div>
                <div class="lg:w-9/12 flex flex-wrap items-start">
				

				
					<div class="swiper" data-element="staff_carousel">
						<div class="swiper-wrapper pb-8">
						<?php foreach( $featured_posts as $post ): 
							setup_postdata($post); ?>
							<div class="swiper-slide bg-white grid grid-cols-3 min-h-[160px] md:min-h-[280px] lg:min-h-[189px]">
								<div class=" col-span-2 pt-7 pl-6 pr-4">
									<h3 class=" text-[20px] leading-[24px] uppercase "><?php the_title(); ?></h3>
									<p class="text-titlesm mt-4"><?php echo get_field('staff_leadjobtitle');?></p>
								</div>
								<div class="relative  h-full">
									<?php if(get_the_post_thumbnail_url()): ?>
									<img class="absolute inset-0 w-full h-full object-cover" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
									<?php else: ?>
										<img class="absolute inset-0 w-full h-full object-cover" src="<?php the_field('staff_fallback_image', 'options'); ?>" alt="<?php the_title();?>" />
									<?php endif; ?>
								</div>
								
							</div>
						<?php endforeach; ?>
						</div>
						<div class="swiper-pagination"></div>
					</div>
					<?php 
					// Reset the global post object so that the rest of the page works correctly.
					wp_reset_postdata(); ?>
				
                </div>
			</div>
		</div>
		<?php endif; ?>