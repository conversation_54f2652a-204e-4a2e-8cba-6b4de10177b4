{"key": "group_5e8c93a7e6d6c", "title": "Banner", "fields": [{"key": "field_5e8c93ee262d9", "label": "Banner Type", "name": "banner_type", "type": "radio", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"simple": "Simple", "media": "Media"}, "allow_null": 0, "other_choice": 0, "default_value": "simple", "layout": "horizontal", "return_format": "value", "save_other_choice": 0}, {"key": "field_5e8c93c0262d6", "label": "Alternative title", "name": "banner_alternative_title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5e8c93cd262d7", "label": "Intro text", "name": "banner_intro_text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5e8c955e262dd", "label": "Column", "name": "", "type": "column", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5e8c93ee262d9", "operator": "==", "value": "media"}]], "wrapper": {"width": "", "class": "", "id": ""}, "column-type": "1_2"}, {"key": "field_5e8c94a5262da", "label": "Banner Image", "name": "banner_image", "type": "image", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5e8c93ee262d9", "operator": "==", "value": "media"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5ed8f94c2a35d", "label": "Video Type", "name": "video_type", "type": "radio", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"file": "File", "url": "URL"}, "allow_null": 0, "other_choice": 0, "default_value": "", "layout": "horizontal", "return_format": "value", "save_other_choice": 0}, {"key": "field_5e8c94d7262db", "label": "Video Desktop", "name": "banner_video_desktop", "type": "file", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5e8c93ee262d9", "operator": "==", "value": "media"}, {"field": "field_5ed8f94c2a35d", "operator": "==", "value": "file"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_size": "", "max_size": "", "mime_types": ""}, {"key": "field_5e8c9525262dc", "label": "Video Mobile", "name": "banner_video_mobile", "type": "file", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5e8c93ee262d9", "operator": "==", "value": "media"}, {"field": "field_5ed8f94c2a35d", "operator": "==", "value": "file"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_size": "", "max_size": "", "mime_types": ""}, {"key": "field_5eda17fd39526", "label": "Video URL", "name": "banner_video_url", "type": "url", "instructions": "Only working for Vimeo Pro accounts.", "required": 0, "conditional_logic": [[{"field": "field_5e8c93ee262d9", "operator": "==", "value": "media"}, {"field": "field_5ed8f94c2a35d", "operator": "==", "value": "url"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_5e8c961e1c853", "label": "Column", "name": "", "type": "column", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5e8c93ee262d9", "operator": "==", "value": "media"}]], "wrapper": {"width": "", "class": "", "id": ""}, "column-type": "1_2"}, {"key": "field_5e8c96371c854", "label": "Media Type", "name": "banner_media_type", "type": "radio", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5e8c93ee262d9", "operator": "==", "value": "media"}]], "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"image": "<span class=\"media-choice media-choice--picture\" title=\"Picture\">Image</span>", "video": "<span class=\"media-choice media-choice--video\" title=\"Video\">Video</span>"}, "allow_null": 0, "other_choice": 0, "default_value": "", "layout": "horizontal", "return_format": "value", "save_other_choice": 0}, {"key": "field_5e8c967c1c855", "label": "Full height", "name": "banner_full_height", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_5e8c96961c856", "label": "Content position", "name": "banner_content_position", "type": "radio", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5e8c93ee262d9", "operator": "==", "value": "media"}]], "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"center": "<span class=\"border-choice border-choice--center\" title=\"Center\">Center</span>", "end": "<span class=\"border-choice border-choice--bottom\" title=\"Bottom\">Bottom</span>"}, "allow_null": 0, "other_choice": 0, "default_value": "bottom", "layout": "horizontal", "return_format": "value", "save_other_choice": 0}, {"key": "field_5e8c97bf1c857", "label": "Overlay", "name": "banner_overlay", "type": "checkbox", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5e8c93ee262d9", "operator": "==", "value": "media"}]], "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"full": "<span class=\"border-choice border-choice--full\" title=\"Full\">Full</span>", "top": "<span class=\"border-choice border-choice--top\" title=\"Top\">Top</span>", "bottom": "<span class=\"border-choice border-choice--bottom\" title=\"Bottom\">Bottom</span>"}, "allow_custom": 0, "default_value": false, "layout": "horizontal", "toggle": 0, "return_format": "value", "save_custom": 0}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}], [{"param": "post_type", "operator": "==", "value": "page"}]], "menu_order": 4, "position": "acf_after_title", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": 1, "description": "", "modified": 1591356476}