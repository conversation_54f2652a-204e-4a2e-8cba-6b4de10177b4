<?php
/**
 * Template part to display the block "Box txt-img".
 *
 * @package twkmedia
 */

?>

<?php //the_sub_field('');?>



<?php if( have_rows('boxes') ): ?>
    <div class="md:grid grid-cols-2 lg:grid-cols-3 gap-8 mb-10 lg:mb-12">
    <?php while( have_rows('boxes') ): the_row();         ?>

    <?php if(get_sub_field('video')):?>
    <div>
        <div data-element="play_video" data-video="<?php the_sub_field('vimeo_id');?>" class="relative 
        after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-y-1/2 after:-translate-x-1/2 after:w-[56px] after:h-[56px] lg:after:w-[74px] lg:after:h-[74px] after:bg-play-icon-red after:bg-no-repeat after:bg-contain after:z-20 after:cursor-pointer
        before:content-[''] before:absolute before:bottom-0 before:left-0 before:w-full before:h-[33%] before:bg-gradient-to-b before:from-black/0 before:to-black/50 [&_img]:hover:scale-[1.3] cursor-pointer">
            <div class="relative  mb-0 [&_img]:hover:scale-[1.3]">
                <?php 
                $image = get_sub_field('image');
                if( !empty( $image ) ): ?>
                <div class="aspect-[3/2] overflow-hidden cursor-pointer">
                    <?php echo twk_output_acf_image($image, 'large', 'w-full h-full object-cover duration-500 ease-in' , 'lazy'); ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <h2 class=" text-titlemd lg:text-titlesidebar leading-[36px] uppercase mb-1 mt-5"><?php echo get_sub_field('title'); ?></h2>
        <p class="text-copy16"><?php echo get_sub_field('copy'); ?></p>
            
    
    </div>
    <?php else :?>
        <div class="relative  mb-0 [&_img]:hover:scale-[1.3]">
            <?php 
            $image = get_sub_field('image');
            if( !empty( $image ) ): ?>
            <div class="aspect-[3/2] overflow-hidden">
                <?php echo twk_output_acf_image($image, 'large', 'w-full h-full object-cover duration-500 ease-in' , 'lazy'); ?>

            </div>
            <?php endif; ?>
            <h2 class=" text-titlemd lg:text-titlesidebar leading-[36px] uppercase mb-1 mt-5"><?php echo get_sub_field('title'); ?></h2>
            <p class="text-copy16"><?php echo get_sub_field('copy'); ?></p>
            <?php 
            $link = get_sub_field('link');
            if( $link ): 
                $link_url = $link['url'];
                $link_title = $link['title'];
                $link_target = $link['target'] ? $link['target'] : '_self';
                ?>
                <a class="absolute top-0 left-0 w-full h-full" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"></a>
            <?php endif; ?>
        </div>

    <?php endif; ?>
     

        
    <?php endwhile; ?>
    </div>
<?php endif; ?>


