<?php
/**
 * The template for displaying all single vacancies
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package twkmedia
 */

get_header();

?>

<?php if (have_posts()): ?>

	<?php while (have_posts()): the_post(); ?>

    <section class="bg-blue pt-[170px] pb-[50px] lg:pb-20 mb-14">
        <div class="container text-center px-10 lg:px-0">
                <span class=" text-white text-base uppercase mb-6"><?php the_field('vacancies_txt','options');?></span>
                <h1 class=" text-white text-title46 leading-[48px] lg:text-titlelg uppercase mt-3 lg:mt-6 mb-10"><?php the_title();?></h1>
                <div class="block lg:flex justify-center text-white  text-titlesm uppercase">
                    
                    <?php foreach ( get_the_terms( get_the_ID(), 'vacancy_type' ) as $term ) {
                        $short_title = get_field('short_title', $term);
                        echo '<span class="inline-table relative pl-[22px] lg:pl-5 mb-5 lg:mb-0 ml-5 after:bg-suitcase-icon-white after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-0 after:top-1/2 after:-translate-y-[58%] lg:after:-translate-y-1/2 after:w-[14px] after:h-[14px]">' . $short_title . '</span>';
                    } ?>
                    
                    <span class="inline-table relative pl-[22px] lg:pl-5 mb-5 lg:mb-0 ml-5 after:bg-clock-icon-white after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-0 after:top-1/2 after:-translate-y-[58%] lg:after:-translate-y-1/2 after:w-[14px] after:h-[14px] mx-6"><?php the_field('hrs_job'); ?></span>
                    <span class="inline-table relative pl-[22px] lg:pl-5 mb-5 lg:mb-0 after:bg-calendar-icon-white after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-0 after:top-1/2 after:-translate-y-[58%] lg:after:-translate-y-1/2 after:w-[14px] after:h-[14px]"><?php the_field('closing_date','options');?> <?php the_field('closing_date_job'); ?></span>
                </div>
        </div>
    </section>

    <section>
        <div class="container wysiwyg px-10 lg:px-0">
            <div class="lg:w-8/12 mx-auto">
                <?php the_field('content_job');?>
                
                <!-- <a class="button" href="/about-us/join-our-team/apply-now/?role=<?php the_title();?>" target="_self">Apply now</a> -->
                 <a class="button" href="https://cranleigh.ciphr-irecruit.com/applicants" target="_blank">Apply now</a> 
            </div>
        </div>
       
    </section>
	
   
    <section class="my-16 bg-cranleighclay py-9 mt-[100px]">
        <div class="container px-10 lg:px-0">
            <div class="lg:flex lg:w-10/12 justify-between items-center mx-auto">
                <div class="lg:w-4/12">
                    <h2 class="  text-title30 lg:text-titleh2 uppercase mb-4 lg:mb-0"><?php the_field('related_documents_title_job');?></h2>
                </div>
                <div class="lg:w-6/12 flex flex-wrap items-start">
                    <?php if( have_rows('related_documents_job') ): ?>
                        <?php while( have_rows('related_documents_job') ): the_row(); ?>
                        <?php
                        $file = get_sub_field('file');
                        if( $file ): ?>
                            <a target="_blank" class="relative min-w-[50%] text-copy16 after:content-[''] after:absolute after:w-4 after:h-4 after:top-1/2 after:-translate-y-1/2 after:left-0 after:bg-pdf-icon-navy after:bg-center after:bg-contain after:bg-no-repeat pl-6 odd:pr-8 mb-6 last:mb-0" href="<?php echo $file['url']; ?>"><?php echo $file['title']; ?></a>
                        <?php endif; ?>


                        <?php endwhile; ?>
                    <?php endif; ?>	
                </div>
            </div>
        </div>
    </section>

    <section>
        <h2 class=" text-center text-title30 lg:text-title uppercase mb-10"><?php the_field('related_vacancies','options');?></h2>
        <div class="container px-10 lg:px-0">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-8">
            <?php
            $featured_posts = get_field('related_vacancies');
            if( $featured_posts ): ?>
                <?php foreach( $featured_posts as $post ): 
                    setup_postdata($post); ?>
                    <?php foreach ( get_the_terms( get_the_ID(), 'vacancy_type' ) as $term ) {
                        $color = get_field('color', $term);
                        
                    } ?>
                    <div class="relative border border-blue py-[39px] px-[30px] hover:bg-<?= $color;?> <?php if($color == "grey"): echo 'hover:text-blue hover:border-grey'; else: echo 'hover:text-white [&_.icontochange]:after:hover:brightness-0 [&_.icontochange]:after:hover:invert'; endif; ?> duration-300 ease-in">
                         <div data-element="vacancy_wrap">
                            <h3 class=" text-titlesidebar uppercase mb-2"><?php the_title();?></h3>
                            <div class="children:text-copy16"><?php the_excerpt();?></div>
                        </div>
                        <div class="lg:flex justify-start items-center mt-7 mb-3">
                            <span class="relative block bg-<?= $color;?> 
                             text-titlesm uppercase py-[9px] pl-[35px] mb-3 lg:mb-0 after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-3 after:top-1/2 after:-translate-y-1/2 after:w-[14px] after:h-[14px]
                            <?php if($color == "grey"): echo 'text-blue pr-3 after:bg-suitcase-icon-navy '; else: echo 'text-white pr-3 after:bg-suitcase-icon-white '; endif; ?>
                            "><?php echo strip_tags(get_the_term_list( $post->ID, 'vacancy_type', '', ', ', ' ' )); ?></span>
                            <span class="icontochange block after:duration-300 after:ease-in relative  text-titlesm uppercase pl-5 lg:ml-5 after:bg-clock-icon-black after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-0 after:top-1/2 after:-translate-y-1/2 after:w-[14px] after:h-[14px]"><?php the_field('hrs_job'); ?></span>
                        </div>
                        <span class="icontochange block after:duration-300 after:ease-in relative  text-titlesm uppercase pl-5 after:bg-calendar-icon-black after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-0 after:top-[calc(50%_-_2px)] after:-translate-y-1/2 after:w-[14px] after:h-[14px]"><?php the_field('closing_date','options');?> <?php the_field('closing_date_job'); ?></span>
                        <a class="absolute inset-0 h-full w-full" href="<?php the_permalink();?>"></a>
                    </div>
                <?php endforeach; ?>
                <?php 
                wp_reset_postdata(); ?>
           

            <?php else: ?>
                <?php 
                $args = array(
                    'post_type' => 'vacancy',
                    'posts_per_page'      => 3,
                    'post__not_in'           => [get_the_ID()]
                );
                $query = new WP_Query( $args );

                if ($query->have_posts()) : ?>
                    <?php 
                    while ($query->have_posts()) : $query->the_post();  ?>
                    <?php foreach ( get_the_terms( get_the_ID(), 'vacancy_type' ) as $term ) {
                        $color = get_field('color', $term);
                        
                    } ?>
                    <div class="relative border border-blue py-[39px] px-[30px] hover:bg-<?= $color;?> <?php if($color == "grey"): echo 'hover:text-blue hover:border-grey'; else: echo 'hover:text-white [&_.icontochange]:after:hover:brightness-0 [&_.icontochange]:after:hover:invert'; endif; ?> duration-300 ease-in">
                        <div data-element="vacancy_wrap">
                            <h3 class=" text-titlemd lg:text-titlesidebar uppercase mb-2"><?php the_title();?></h3>
                            <div class="children:text-copy16"><?php the_excerpt();?></div>
                        </div>
                        <div class="flex justify-start items-center mt-7 mb-3">
                            <span class="relative bg-<?= $color;?> 
                             text-[13px] leading-[14px] md:text-titlesm uppercase py-[9px] pl-[35px] after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-3 after:top-1/2 after:-translate-y-1/2 after:w-[14px] after:h-[14px]
                            <?php if($color == "grey"): echo 'text-blue pr-3 after:bg-suitcase-icon-navy '; else: echo 'text-white pr-3 after:bg-suitcase-icon-white '; endif; ?>
                            "><?php echo strip_tags(get_the_term_list( $post->ID, 'vacancy_type', '', ', ', ' ' )); ?></span>
                            <span class="icontochange after:duration-300 after:ease-in relative  text-[13px] leading-[14px] md:text-titlesm uppercase pl-5 ml-5 after:bg-clock-icon-black after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-0 after:top-1/2 after:-translate-y-1/2 after:w-[14px] after:h-[14px]"><?php the_field('hrs_job'); ?></span>
                        </div>
                        <span class="block icontochange after:duration-300 after:ease-in relative  text-[13px] leading-[14px] md:text-titlesm uppercase pl-5 after:bg-calendar-icon-black after:bg-contain after:bg-no-repeat after:bg-center after:absolute after:left-0 after:top-[calc(50%_-_2px)] after:-translate-y-1/2 after:w-[14px] after:h-[14px]"><?php the_field('closing_date','options');?> <?php the_field('closing_date_job'); ?></span>
                <a class="absolute inset-0 h-full w-full" href="<?php the_permalink();?>"></a>
                    </div>

                    <?php endwhile; ?>
                <?php else : ?>
                    <div class="text-center">
                        <h2 class="">Sorry, no results found</h2>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
                
            </div>
        </div>
        
        <?php 
        $link = get_field('back_to_vacancies','options');
        if( $link ): 
            $link_url = $link['url'];
            $link_title = $link['title'];
            $link_target = $link['target'] ? $link['target'] : '_self';
            ?>
            <a class="table button pr-[35px] pl-[75px] mx-auto mt-10 after:right-auto after:left-8 after:rotate-180 ml-10 lg:ml-auto lg:mr-auto" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
        <?php endif; ?>
    </section>

    <script>
   
        function sameHeights () {
        var nodeList = document.querySelectorAll('[data-element="vacancy_wrap"]');
        var elems = [].slice.call(nodeList);

        var mq = window.matchMedia('(min-width: 1024px)')
   
        if (mq.matches) {
            var tallest = Math.max.apply(Math, elems.map(function(elem, index) {
                elem.style.minHeight = ''; // clean first
                return elem.offsetHeight;
            }));
            elems.forEach(function(elem, index, arr){ 
                elem.style.minHeight = tallest + 'px';
            });
            
            }
        }
        var resized = true;
        var timeout = null;
        var refresh = function(){
        if(resized) {
            requestAnimationFrame(sameHeights);
        }
            clearTimeout(timeout);
            timeout = setTimeout( refresh, 100);
        resized = false;
        };
        window.addEventListener('resize', function() { resized = true; });
        refresh();
        </script>
		

	<?php endwhile; ?>
	
<?php endif; ?>

<?php get_footer(); ?>


