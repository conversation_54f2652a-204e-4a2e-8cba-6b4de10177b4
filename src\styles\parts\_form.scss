/*
	FORM
*/

input.full-width{
	width: 100%;	
		margin-bottom: 15px;
	}
	
	input{
		border: none;
		padding: 20px 25px ;
		color: white;
		font-size: rem(18px) !important;
		&:focus{
			outline: none;	
		}
		&::-webkit-input-placeholder {
			color: white;
		}
		&:-moz-placeholder { /* Firefox 18- */
			color: white;
		}
		&::-moz-placeholder {  /* Firefox 19+ */
			color: white;
		}
		&:-ms-input-placeholder {  
			color: white;
		} 
	}
	
	select{
		width: 100%;
		-webkit-appearance: none;
		-moz-appearance:    none;
		appearance:         none;
		background-color: transparent;
		color: black;
		font-size: rem(14px);
		letter-spacing: 1px;
		text-transform: uppercase;
		border: none;
		padding: 10px 0;
		border-bottom: solid 1px #919295;
		padding-right: 0;
		border-radius: 0;
		max-width: 100%;
		&:focus{
			outline: none;
		}
	}
	