// Slider

// Slick slider - General.
.slick-prev,
.slick-next {
	&:before {
		content: '';
		display: inline-block;
	
		border-bottom: solid white 1px;
		border-left: solid white 1px;
		height: 12px;
		width: 12px;
	}
}

.slick-next {
	&:before {
		transform: rotate(-135deg);
	}
}
.slick-prev {
	&:before {
		transform: rotate(45deg);
	}
}

// Dots.
.slick-dots {
	position: absolute;
    bottom: 30px;
    right: 20px;
	width: auto;
	
	li{
		button{
			background-color: transparent;
	
			&:before{
				border: 1px solid $primary;
			}
		}
	
		&.slick-active{
			button:before{
				background-color: $primary;
			}
		}
	}
}


// Block - Gallery Slider.
.gallery-slider {
    margin-bottom: 65px;
    position: relative;

    .slick-slide {
        & > div {
            width: 100%;
            padding-bottom: 55%; // controls the height of the gallery slider
            position: relative;
        }

        img {
            position: absolute;
            height: 100%;
            width: 100%;
            object-fit: cover;

            &.contain {
                object-fit: contain;
            }
        }
    }

    &__full {
        position: relative;
        background: $black;

        &-nav {
            position: absolute;
            bottom: 23%;
            width: 100%;
            left: 0;
            text-align: center;
            z-index: 9;

            .pages {
                display: inline-block;
                color: white;
            }

            .slick-prev,
            .slick-next {
                position: relative;
                display: inline-block;
                left: auto;
                right: auto;
                top: auto;
                transform: none;

                &:before {
                    height: 9px;
                    width: 9px;
                }
            }
        }

    }

    &__thumb {

        .slick-next,
        .slick-prev {
            height: 100%;
            width: 36px;
            background-color: rgba(#242424, 0.45);
            background-color: #242424;
            z-index: 1;
            opacity: 0.65;
            transition: opacity 0.2s linear;

            &:hover {
                opacity: 1;
            }
        }

        .slick-prev {
            left: 0;
        }

        .slick-next {
            right: 0;
        }

        .slick-slide {
            cursor: pointer;

            & > div {
                &:before {
                    position: absolute;
                    content: '';
                    height: 100%;
                    width: 100%;
                    background: white;
                    opacity: 0.6;
                    z-index: 1;
                    transition: opacity 0.2s linear;
                }
            }

            &:hover {
                & > div {
                    &:before {
                        opacity: 0.3;
                    }
                }
            }

            &.slick-current {
                & > div {
                    &:before {
                        opacity: 0;
                    }
                }
            }
        }
    }
}



