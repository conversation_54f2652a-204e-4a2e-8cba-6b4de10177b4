/* Styles for dialog window */
.magnific-dialog {
	position: relative;
	background: white;
	padding: 50px 70px;
	width: 720px;
	max-width: 80vw;
	margin: 40px auto;
}

// Video Popup
.mfp-video-wrapper{
	.mfp-content{
		width: 90vw;
	}
}

/**
 * Fade-zoom animation for first dialog
 */

/* start state */
.my-mfp-zoom-in .magnific-dialog {
	opacity: 0;
	transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
	transform: scale(0.8); 
}

/* animate in */
.my-mfp-zoom-in.mfp-ready .magnific-dialog {
	opacity: 1;
	transform: scale(1); 
}

/* animate out */
.my-mfp-zoom-in.mfp-removing .magnific-dialog {
	transform: scale(0.8); 
	opacity: 0;
}

/* Dark overlay, start state */
.my-mfp-zoom-in.mfp-bg {
	opacity: 0;
	transition: opacity 0.3s ease-out;
}
/* animate in */
.my-mfp-zoom-in.mfp-ready.mfp-bg {
	opacity: 0.8;
}
/* animate out */
.my-mfp-zoom-in.mfp-removing.mfp-bg {
	opacity: 0;
}
