<?php
/**
 * Frontend functions.
 *
 * @package twkmedia
 */

if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Custom pagination.
 *
 * @param string  $pages Pages.
 * @param integer $range Range.
 * @return void
 */
function twk_custom_pagination( $pages = '', $range = 2 ) {
	$showitems = ( $range * 2 ) + 1;

	global $paged;
	if ( empty( $paged ) ) {
		$paged = 1;
	}

	if ( '' === $pages ) {
		global $wp_query;

		$pages = $wp_query->max_num_pages;

		if ( ! $pages ) {
			$pages = 1;
		}
	}

	if ( 1 !== $pages ) {
		echo "<div class='pagination'>";
		if ( $paged > 2 && $paged > $range + 1 && $showitems < $pages ) {
			echo "<a href='" . get_pagenum_link( 1 ) . "'>&laquo;</a>";
		}
		if ( $paged > 1 && $showitems < $pages ) {
			echo "<a href='" . get_pagenum_link( $paged - 1 ) . "'>&lsaquo;</a>";
		}

		for ( $i = 1; $i <= $pages; $i++ ) {
			if ( 1 !== $pages && ( ! ( $i >= $paged + $range + 1 || $i <= $paged - $range - 1 ) || $pages <= $showitems ) ) {
				echo ( $paged === $i ) ? "<span class='current'>" . $i . "</span>" : "<a href='" . get_pagenum_link( $i ) . "' class='inactive' >" . $i . "</a>";
			}
		}

		if ( $paged < $pages && $showitems < $pages ) {
			echo "<a href='" . get_pagenum_link( $paged + 1 ) . "'>&rsaquo;</a>";
		}
		if ( $paged < $pages - 1 && $paged + $range - 1 < $pages && $showitems < $pages ) {
			echo "<a href='" . get_pagenum_link( $pages ) . "'>&raquo;</a>";
		}
		echo "</div>\n";
	}
}


/**
 * Get Yoast primary category link.
 *
 * @return link
 */
function twk_get_primary_cat_link() {
	if ( class_exists( 'WPSEO_Primary_Term' ) ) {
		$wpseo_primary_term = new WPSEO_Primary_Term( 'category', get_the_id() );
		$wpseo_primary_term = $wpseo_primary_term->get_primary_term();
		$term               = get_term( $wpseo_primary_term );

		// Default to first category (not Yoast) if an error is returned.
		if ( ! is_wp_error( $term ) ) {
			$category_display = $term->name;
			$category_link    = get_category_link( $term->term_id );
			return '<a href="' . $category_link . '">' . $category_display . '</a>';
		}
	}
}


/**
 * It creates a way to have custom lengths excerpts - ex. echo twk_excerpt(32);
 * param $limit integer Number of words to show on the excerpt
 * return $excerpt The excerpt with the limited number of words
 *
 * @param integer $limit Limit of words to show on the excerpt.
 * @return String $excerpt
 */
function twk_excerpt( $limit, $post_id = null ) {
	if ( $post_id == null ){
		$post_id = get_the_ID();
	}

	$excerpt = explode( ' ', get_the_excerpt( $post_id ), $limit );

	if ( count( $excerpt ) >= $limit ) {
		array_pop( $excerpt );
		$excerpt = implode( ' ', $excerpt ) . '...';
	} else {
		$excerpt = implode( ' ', $excerpt );
	}

	$excerpt = preg_replace( '`[[^]]*]`', '', $excerpt );

	// If no excerpt we try with the flexible content.
	if ( $excerpt === '' ) {
		$fields = get_fields( $post_id );
		$first_content = null;

		$blocks = $fields['blocks'];
        $content_blocks = array();
        $first_content_block = null;

		if ( $blocks ) {
			foreach($blocks as $block) {
				
				if ( $block['acf_fc_layout'] === 'text' ) {
					$content_blocks[] = $block;
				}
			}
			
			$first_content_block = $content_blocks[0];
			$first_content = $first_content_block['wysiwyg'];
			$first_content = get_first_paragraph( $first_content );
	
			$text = strip_shortcodes( $first_content );
			$text = apply_filters('the_content', $text);
			$text = str_replace(']]>', ']]>', $text);
			$excerpt_more = apply_filters('excerpt_more', ' ' . '...');
			$text = wp_trim_words( $text, $limit, $excerpt_more );
	
			return $text;
		} else {
			return '';
		}
	}

	return $excerpt;
}

/**
 * Gets the first p tag from a string.
 *
 * @param string $string
 * @return string
 */
function get_first_paragraph( $string ) {
    $paragraph = substr( $string, 0, strpos( $string, "</p>" ) );
    
    return $paragraph;
}



/**
 * Creates an anchor tag with the appropriate link and data.
 *
 * @param array  $the_link ACF link field.
 * @param String $classes Classes to be added to the link.
 * @return String HTML
 */
function twk_compose_acf_link( $the_link, $classes = null ) {

	// Get the data.
	$link_url   = ( $the_link['url'] ) ? $the_link['url'] : '';                // URL.
	$link_title = ( $the_link['title'] ) ? $the_link['title'] : '';            // Title.
	if ( '' === $link_title ) {
		$link_title = get_the_title( url_to_postid( $link_url ) );
	}
	$link_target = ( $the_link['target'] ) ? $the_link['target'] : '_self';    // Target.

	// Use the data to create the link (html anchor).
	if ( '_blank' === $link_target ) {
		$return = '<a href="' . esc_url( $link_url ) . '" target="' . esc_attr( $link_target ) . '" rel="noopener noreferrer external" class="' . $classes . '">';
	} else {
		$return = '<a href="' . esc_url( $link_url ) . '" target="' . esc_attr( $link_target ) . '" class="' . $classes . '">';
	}
	if ( strpos( $classes, 'visually-hidden-link' ) > 0 ) {
		$return .= '<span class="visually-hidden">' . esc_html( $link_title ) . '</span>';
	} else {
		$return .= esc_html( $link_title );
	}
	$return .= '</a>';

	return $return;

}



/**
 * ACF date field change format
 *
 * @param String $field The ACF field.
 * @param String $format_in The format in which is saved to the database.
 * @param String $format_out The format we want to print out.
 * @return String
 */
function twk_acf_date_format( $field, $format_in = null, $format_out = null ) {
	if ( $format_in === null ) {
		$format_in = 'd/m/Y';
	}

	if ( $format_out === null ) {
		$format_out = 'd-m-Y';
	}

	$date = DateTime::createFromFormat( $format_in, $field );

	return $date->format( $format_out );
}
function my_load_ajax_content () {
	global $switched;
	switch_to_blog(1);
    $pid = intval($_POST['postid']);
		
    $the_query  = new WP_Query(array('p' => $pid, 'post_type' => 'any'));
	
    if ($the_query->have_posts()) {
		
        while ( $the_query->have_posts() ) {
            $the_query->the_post();
			
            ?>
			
				<div class="absolute bg-white right-0 top-0 h-full w-full md:w-9/12 lg:w-1/2 max-w-[725px] " >
					<span class="absolute w-[60px] h-[60px] bg-cranleighclay z-10 top-10 left-0 lg:-left-[60px] cursor-pointer
					after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2 after:w-7 after:h-7 after:bg-close-icon-navy after:bg-contain after:bg-no-repeat" data-element="close_popup"></span>
					<div data-lenis-prevent class="overflow-auto h-full">
						<img class="aspect-[3/2] object-cover w-full" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
						<div class="px-10">
							<h3 class=" text-blue uppercase text-titleh4 lg:text-title mt-12 mb-3 lg:mb-8"><?php the_title();?></h3>
							
						<?php
							if (have_rows('blocks')):
								
								$blockCount = 0;
							
								// loop through the rows of data.
								while (have_rows('blocks')):
							
									the_row();
									
									$layout = get_row_layout();
									$blockId = $layout . '-' . $blockCount;
									include locate_template('src/components/' . $layout . '/' . $layout . '.php');
									
									$blockCount++; 
							
								endwhile;

							endif;
							?>
						</div>
					</div>
				</div>
				
				<?php
        }
    }
    else {
       
    }
    wp_reset_postdata();

    
    // And must die() the function
    die();
	restore_current_blog();
}

add_action ( 'wp_ajax_nopriv_my_load_ajax_content', 'my_load_ajax_content' );
add_action ( 'wp_ajax_my_load_ajax_content', 'my_load_ajax_content' );


function my_load_ajax_content_prep () {
	global $switched;
	switch_to_blog(5);
    $pid = intval($_POST['postid']);
		
    $the_query  = new WP_Query(array('p' => $pid, 'post_type' => 'any'));
	
    if ($the_query->have_posts()) {
		
        while ( $the_query->have_posts() ) {
            $the_query->the_post();
			
            ?>
			
				<div class="absolute bg-white right-0 top-0 h-full w-full md:w-9/12 lg:w-1/2 max-w-[725px] " >
					<span class="absolute w-[60px] h-[60px] bg-cranleighclay z-10 top-10 left-0 lg:-left-[60px] cursor-pointer
					after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2 after:w-7 after:h-7 after:bg-close-icon-navy after:bg-contain after:bg-no-repeat" data-element="close_popup"></span>
					<div data-lenis-prevent class="overflow-auto h-full">
						<img class="aspect-[3/2] object-cover w-full" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
						<div class="px-10">
							<h3 class=" text-blue uppercase text-titleh4 lg:text-title mt-12 mb-3 lg:mb-8"><?php the_title();?></h3>
							
						<?php
							if (have_rows('blocks')):
								
								$blockCount = 0;
							
								// loop through the rows of data.
								while (have_rows('blocks')):
							
									the_row();
									
									$layout = get_row_layout();
									$blockId = $layout . '-' . $blockCount;
									include locate_template('src/components/' . $layout . '/' . $layout . '.php');
									
									$blockCount++; 
							
								endwhile;

							endif;
							?>
						</div>
					</div>
				</div>
				
				<?php
        }
    }
    else {
       
    }
    wp_reset_postdata();

    
    // And must die() the function
    die();
	restore_current_blog();
}

add_action ( 'wp_ajax_nopriv_my_load_ajax_content_prep', 'my_load_ajax_content_prep' );
add_action ( 'wp_ajax_my_load_ajax_content_prep', 'my_load_ajax_content_prep' );


function my_load_ajax_content_staff () {
	global $switched;

	$blog_id = $_GET['blog_id'];
	switch_to_blog($blog_id);
    $idpost = $_GET['idpost'];
    $the_query  = new WP_Query(array('p' => $idpost, 'post_type' => 'staff'));
	
    if ($the_query->have_posts()) {
		
        while ( $the_query->have_posts() ) {
            $the_query->the_post();
			
            ?>



				<div class="absolute bg-white right-0 top-0 h-full w-full md:w-9/12 lg:w-1/2 max-w-[725px] " >
					<span class="absolute w-[60px] h-[60px] bg-cranleighclay z-10 top-10 left-0 lg:-left-[60px] cursor-pointer
					after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2 after:w-7 after:h-7 after:bg-close-icon-navy after:bg-contain after:bg-no-repeat" data-element="close_popup"></span>
					<div data-lenis-prevent class="overflow-auto h-full">
						<div class="px-10 flex mt-12 mb-8 items-center">
							<div class="w-[109px] mr-4 lg:mr-8">
								<?php if(get_the_post_thumbnail_url()): ?>
								<img class="aspect-[1] object-cover w-full rounded-full" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
								<?php else: ?>
									<img class="aspect-[1] object-cover w-full rounded-full" src="<?php the_field('staff_fallback_image', 'options'); ?>" alt="<?php the_title();?>" />
								<?php endif; ?>
							</div>
							<div>
								<h3 class=" text-blue uppercase text-titleh4 lg:text-title"><?php the_title();?></h3>
								<p class="text-copy19"><?php echo get_field('staff_leadjobtitle');?></p>
							</div>
							
						</div>
						
						<div class="px-10 pb-10">
							
							<div class="wysiwyg">
								<?php the_content(); ?>
							</div>
							
							
							
							<?php if( has_term( 'school-governor', 'staff_categories' ) ) :?>
								<p class="mt-5 [&_a]:underline mb-10"><?php the_field('msg_schoolgov','options');?></p>
								<?php echo do_shortcode('[gravityform id="2" ajax="true" field_values="email_to=<EMAIL>"]'); ?>
							<?php else: ?>
								<?php if(get_field('staff_email_address')): ?>
								<h4 class=" text-title30 lg:text-title uppercase mt-8">Get in touch with</h4>
								<span class=" text-[22px] leading-[28px] uppercase block lg:mt-2 mb-[33px]"><?php the_title();?></span>
								
								<?php echo do_shortcode('[gravityform id="2" ajax="true" field_values="email_to='.get_field('staff_email_address').'"]'); ?>
								<?php endif; ?>
							<?php endif; ?>
					</div>
				</div>
				
				<?php
        }
    }
    else {
       
    }
    wp_reset_postdata();

    
    // And must die() the function
    die();
	restore_current_blog();
}

add_action ( 'wp_ajax_nopriv_my_load_ajax_content_staff', 'my_load_ajax_content_staff' );
add_action ( 'wp_ajax_my_load_ajax_content_staff', 'my_load_ajax_content_staff' );


function my_load_ajax_content_staff_inside () {
	global $switched;
	switch_to_blog(1);
	$idpost = $_GET['idpost'];
	
    $the_query  = new WP_Query(array('p' => $idpost, 'post_type' => 'staff'));

    if ($the_query->have_posts()) {
        while ( $the_query->have_posts() ) {
            $the_query->the_post();
            ?>
				<div class="absolute bg-white right-0 top-0 h-full w-full lg:w-[calc(50%_-_100px)] lg:max-w-[calc(725px_-_100px)]" >
					<span class="absolute w-[60px] h-[60px] bg-cranleighclay z-10 top-10 left-0 lg:-left-[60px] cursor-pointer
					after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2 after:w-7 after:h-7 after:bg-close-icon-navy after:bg-contain after:bg-no-repeat" data-element="close_popup_staff"></span>
					<div data-lenis-prevent class="overflow-auto h-full">
						<div class="px-10 flex mt-12 mb-8 items-center">
							<div class="w-[109px] mr-4 lg:mr-8">
								<?php if(get_the_post_thumbnail_url()): ?>
								<img class="aspect-[1] object-cover w-full rounded-full" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
								<?php else: ?>
									<img class="aspect-[1] object-cover w-full rounded-full" src="<?php the_field('staff_fallback_image', 'options'); ?>" alt="<?php the_title();?>" />
								<?php endif; ?>
							</div>
							<div>
								<h3 class=" text-blue uppercase text-titleh4 lg:text-title"><?php the_title();?></h3>
								<p class="text-copy19"><?php echo get_field('staff_leadjobtitle');?></p>
							</div>
							
						</div>
						
						<div class="px-10 pb-10">
							<div class="wysiwyg">
								<?php the_content(); ?>
							</div>
							<?php if(get_field('staff_email_address')): ?>
							<h4 class=" text-title30 lg:text-title uppercase mt-8">Get in touch with</h4>
							<span class=" text-[22px] leading-[28px] uppercase block lg:mt-2 mb-[33px]"><?php the_title();?></span>
							<?php echo do_shortcode('[gravityform id="2" ajax="true" field_values="email_to='.get_field('staff_email_address').'"]'); ?>
							
							<?php endif; ?>
					</div>
				</div>
				
				<?php
        }
    }
    else {
       
    }
    wp_reset_postdata();

    
    // And must die() the function
    die();
	restore_current_blog();
}

add_action ( 'wp_ajax_nopriv_my_load_ajax_content_staff_inside', 'my_load_ajax_content_staff_inside' );
add_action ( 'wp_ajax_my_load_ajax_content_staff_inside', 'my_load_ajax_content_staff_inside' );


function my_load_ajax_content_event () {
    $pid = intval($_POST['postid']);
	
    $the_query  = new WP_Query(array('p' => $pid, 'post_type' => 'cran-event'));

    if ($the_query->have_posts()) {
        while ( $the_query->have_posts() ) {
            $the_query->the_post();

			$event_start = get_field('cranevent_start_date');
			$event_start_time = get_field('cranevent_start_time');
			$event_end = get_field('cranevent_end_date');
			$event_end_time = get_field('cranevent_end_time');
			$location_event = get_field('cranevent_location');
			$cta_event = get_field('cta_event');
            ?>
				<div class="absolute bg-white right-0 top-0 h-full w-full md:w-9/12 lg:w-1/2 max-w-[725px] " >
					<span class="absolute w-[60px] h-[60px] bg-cranleighclay z-10 top-10 left-0 lg:-left-[60px] cursor-pointer
					after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2 after:w-7 after:h-7 after:bg-close-icon-navy after:bg-contain after:bg-no-repeat" data-element="close_popup"></span>
					<div data-lenis-prevent class="overflow-auto h-full">
						<div class="pl-20 lg:pl-10 px-10">
							
							<?php
                           
						   $cat = get_the_terms($post->ID, 'event-type');
						   if ($cat) : ?><span class="block  text-weyblue text-titlesm uppercase mt-10"><?php echo $cat[0]->name; ?></span><?php endif; ?>
							<h3 class=" text-blue uppercase text-title30 lg:text-title mt-4 mb-8"><?php the_title();?></h3>

							<div class="grid lg:grid-cols-2 lg:grid-rows-2 gap-4 lg:gap-8 border border-blue py-8 px-8 mb-14">
								<?php if($event_start):?>
								<div>
									<h3 class=" text-titlemd lg:text-titlesidebar  uppercase mb-2">Begins</h3>
									<p class="text-copy16"><?= $event_start?> <?php if($event_start_time):?> at <?= $event_start_time; ?><?php endif;?></p>
								</div>
								<?php endif;?>
								<?php if($event_end):?>
								<div>
									<h3 class=" text-titlemd lg:text-titlesidebar  uppercase mb-2">Ends</h3>
									<p class="text-copy16"><?= $event_end?> <?php if($event_end_time):?> at <?= $event_end_time; ?><?php endif;?></p>
								</div>
								<?php endif;?>
								<?php if($location_event):?>
								<div>
									<h3 class=" text-titlemd lg:text-titlesidebar  uppercase mb-2">Location</h3>
									<div class="text-copy16"><?= $location_event; ?></div>
								</div>
								<?php endif;?>
								<?php if($cta_event):?>
								<div>
								<?php 
									$link_url = $cta_event['url'];
									$link_title = $cta_event['title'];
									$link_target = $cta_event['target'] ? $cta_event['target'] : '_self';
									?>
									<a class="button" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
								</div>
								<?php endif;?>
								
							</div>
						<?php
							if (have_rows('event_blocks')):
								
								$blockCount = 0;
							
								// loop through the rows of data.
								while (have_rows('event_blocks')):
							
									the_row();

									$layout = get_row_layout();
									$blockId = $layout . '-' . $blockCount;
									include locate_template('src/components/events/' . $layout . '.php');
							
									$blockCount++; 
							
								endwhile;
							?>
							<?php else:

							 the_content();

							endif; 

							
							?>
						</div>
					</div>
				</div>
				
				<?php
        }
    }
    else {
       
    }
    wp_reset_postdata();

    
    // And must die() the function
    die();
}

add_action ( 'wp_ajax_nopriv_my_load_ajax_content_event', 'my_load_ajax_content_event' );
add_action ( 'wp_ajax_my_load_ajax_content_event', 'my_load_ajax_content_event' );



function my_load_ajax_content_event_trip () {
    $pid = intval($_POST['postid']);
	
    $the_query  = new WP_Query(array('p' => $pid, 'post_type' => 'chdbtrips'));

    if ($the_query->have_posts()) {
        while ( $the_query->have_posts() ) {
            $the_query->the_post();

			$evc_dep_date = get_field('evc_dep_date');
			$evc_arr_date = get_field('evc_arr_date');
			$evc_destination = get_field('evc_destination');
			$evc_regularity = get_field('evc_regularity');
			$evc_status = get_field('evc_status');
			$evc_about = get_field('evc_about');
			$evc_staff_ic = get_field('evc_staff_ic');
            ?>
				<div class="absolute bg-white right-0 top-0 h-full w-full md:w-9/12 lg:w-1/2 max-w-[725px] " >
					<span class="absolute w-[60px] h-[60px] bg-cranleighclay z-10 top-10 left-0 lg:-left-[60px] cursor-pointer
					after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2 after:w-7 after:h-7 after:bg-close-icon-navy after:bg-contain after:bg-no-repeat" data-element="close_popup"></span>
					<div data-lenis-prevent class="overflow-auto h-full">
						<div class="px-10 pl-20 lg:pl-10">
							
							
							<h3 class=" text-blue uppercase text-title30 lg:text-title mt-[52px] mb-8"><?php the_title();?></h3>

							<div class="grid grid-cols-1 lg:grid-cols-2 lg:grid-rows-2 gap-4 lg:gap-8 border border-blue py-8 px-8 mb-14">
								<?php if($evc_destination):?>
								<div>
									<h3 class=" text-titlemd lg:text-titlesidebar  uppercase mb-2">Destination</h3>
									<p class="text-copy16"><?= $evc_destination;?></p>
								</div>
								<?php endif;?>
								<?php if($evc_dep_date):?>
								<div>
									<h3 class=" text-titlemd lg:text-titlesidebar  uppercase mb-2">Dates</h3>
									<p class="text-copy16"><?= $evc_dep_date?> - <?= $evc_dep_date;?></p>
								</div>
								<?php endif;?>
								<?php if($evc_regularity):?>
								<div>
									<h3 class=" text-titlemd lg:text-titlesidebar  uppercase mb-2">Regularity</h3>
									<div class="text-copy16"><?= $evc_regularity; ?></div>
								</div>
								<?php endif;?>

								<?php if($evc_status):?>
								<div>
									<h3 class=" text-titlemd lg:text-titlesidebar  uppercase mb-2">Status</h3>
									<div class="text-copy16"><?= $evc_status; ?></div>
								</div>
								<?php endif;?>
								
								
							</div>
							
							<?php if($evc_about):?>
							<div>
								<h3 class=" text-titlemd lg:text-titlesidebar  uppercase mb-2">About the trip</h3>
								<div class="wysiwyg mb-14"><?= $evc_about; ?></div>
							</div>
							<?php endif;?>


							<?php
							
							if( $evc_staff_ic ): ?>
								<?php foreach( $evc_staff_ic as $featured_post ): 
									$permalink = get_permalink( $featured_post->ID );
									$title = get_the_title( $featured_post->ID );
									$the_post_thumbnail_url  = get_the_post_thumbnail_url($featured_post->ID);
									$staff_leadjobtitle = get_field( 'staff_leadjobtitle', $featured_post->ID );
									?>
									<div class="bg-blue lg:grid grid-cols-4 lg:min-h-[270px] w-full mb-6">
										<div class="relative hidden lg:block h-full col-span-2 xl:col-span-1" >
											<?php if($the_post_thumbnail_url): ?>
											<img class="absolute inset-0 w-full h-full object-cover" src="<?= $the_post_thumbnail_url; ?>" alt="<?= $title; ?>" />
											<?php else: ?>
												<img class="absolute inset-0 w-full h-full object-cover" src="<?php the_field('staff_fallback_image', 'options'); ?>" alt="<?= $title; ?>" />
											<?php endif; ?>
										</div>
										<div class="col-span-2 xl:col-span-3 py-12 px-9 pr-3 lg:flex justify-between flex-col">
											<div>
												<h3 class=" text-[32px] leading-[32px] uppercase text-white "><?= $title; ?></h3>
												<p class="text-copy16 text-white mt-1"><?= $staff_leadjobtitle; ?></p>
											</div>
											<div>
												<a class="get_popup_staff_inside button mt-4 lg:mt-0" href="<?= $permalink; ?>" target="_self" data-postid="<?php echo $featured_post->ID; ?>">View bio</a>
											</div>
										</div>
									
										
									</div>


								<?php endforeach; ?>
							<?php endif; ?>
							
						
						</div>
					</div>
				</div>
				
				<?php
        }
    }
    else {
       
    }
    wp_reset_postdata();

    
    // And must die() the function
    die();
}

add_action ( 'wp_ajax_nopriv_my_load_ajax_content_event_trip', 'my_load_ajax_content_event_trip' );
add_action ( 'wp_ajax_my_load_ajax_content_event_trip', 'my_load_ajax_content_event_trip' );







function my_enqueue() {
    // First register script
	//wp_enqueue_script( 'ajax-script', get_template_directory_uri() . '', array('jquery') );
	wp_localize_script( 'ajax-script', 'my_ajax_object', array( 'ajax_url' => admin_url( 'admin-ajax.php' ) ) );
    // Then enqueue script
    wp_enqueue_script( 'ajax-script' );
	gravity_form_enqueue_scripts(2, true);

}

add_action( 'wp_enqueue_scripts', 'my_enqueue' );



