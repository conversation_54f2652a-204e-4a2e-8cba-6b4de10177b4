<?php
/**
 * Template loop calendar.
 *
 * @package twkmedia
 */

?>

<?php
$dd      = null;
$spreads = array();

// Set day_count to make sure we go through every day of month.
// I.E There's events on 18th and 20th but not on the 19th.
// However spread event starts from 17th until the 20th means it won't on 19th because not in loop (see below).
$day_count = 1;
?>

<?php $event_archive_query = new WP_Query( $args ); ?>

<?php
if ( $event_archive_query->have_posts() ) :
	while ( $event_archive_query->have_posts() ) :
		$event_archive_query->the_post();
		?>

		<?php
		$start_date = tribe_get_start_date( $event_archive_query->ID, false, 'Y-m-d' );
		$end_date   = tribe_get_end_date( $event_archive_query->ID, false, 'Y-m-d' );
		$event_day  = tribe_get_start_date( $event_archive_query->ID, false, 'd' );
		?>


		<?php // SHOW EVENTS SPREADS ON MULTIPLE DAYS EVEN ON DAYS THAT DONT HAVE EVENT IN WP_QUERY. ?>
		<?php // Check if $spreads array is not empty and check if $day_count +1 is inf to new date. ?>
		<?php if ( ! empty( $spreads ) && ( (int) $day_count + 1 ) < (int) $event_day ) : ?>

			<?php $tmp = ( (int) $day_count + 1 ); // Set tmp variable which is equal to the day that has no event. ?>

			<?php while ( $tmp < (int) $event_day && ! empty( $spreads ) ) : // and loop through spread events while it's not day that has event. ?>

				<?php $tdate = tribe_get_start_date( $event_archive_query->ID, false, 'Y-m-' . $tmp ); // Get the date. ?>
				<?php $tdate = date( 'Y-m-d', strtotime( $tdate ) ); ?>

				<tr class="date">
					<td colspan="4">
						<?php echo date( 'l j F', strtotime( $tdate ) ); ?>
					</td>
				</tr>

				<?php // loop through spread events. ?>
				<?php foreach ( $spreads as $key => $value ) : ?>

					<?php $ev_id = $key; ?>
					<?php include locate_template( 'tpl/parts/row-calendar.php' ); ?>

					<?php // remove from array if it ends on one of those day. ?>
					<?php if ( strtotime( $value['end_date'] ) === strtotime( $tdate ) ) : ?>
						<?php unset( $spreads[ $key ] ); ?>
					<?php endif; ?>

				<?php endforeach; ?>

				<?php // Increment tmp value. ?>
				<?php $tmp++; ?>
			<?php endwhile; ?>

		<?php endif; ?>
		<?php // END SHOW EVENTS SPREADS ON MULTIPLE DAYS. ?>



		<?php if ( $start_date !== $dd ) : ?>

			<?php

				/*
				* THIS IS A DIRTY HACK - IF AN EVENTS IS SPREAD ON 2 MONTHS THEN SET THE START DATE TO FIRST DAY
				* OF THE CURRENT MONTH.
				*/

			if ( tribe_get_start_date( $event_archive_query->ID, false, 'm' ) !== $wmonth ) :

				$wmonth = tribe_get_end_date( $event_archive_query->ID, false, 'm' )

					/*
					$start_date = tribe_get_start_date($event_archive_query->ID, false, 'Y-'.$wmonth.'-01'); ?>
					<tr class="date <?php echo $start_date === date('Y-m-d')?'today':''; ?>">
						<td colspan="4"><?php echo date('l j F', strtotime($start_date)); ?></td>
					</tr>

				<?php else: ?>
					<tr class="date <?php echo $start_date === date('Y-m-d')?'today':''; ?>">
						<td colspan="4"><?php echo tribe_get_start_date($event_archive_query->ID, false, 'l j F'); ?></td>
					</tr>
					*/
				?>
			<?php endif; ?>

			<tr class="date <?php echo $start_date === date( 'Y-m-d' ) ? 'today' : ''; ?>">
				<td colspan="4"><?php echo tribe_get_start_date( $event_archive_query->ID, false, 'l j F' ); ?></td>
			</tr>

			<?php
			$dd        = $start_date;
			$day_count = (int) $event_day;
			?>

			<?php
			// SPREADED EVENTS - LOOP THROUGH ARRAY (created at the bottom).
			if ( $spreads ) :
				foreach ( $spreads as $key => $value ) :

					/* if(strtotime($value['end_date']) == strtotime($end_date)): */
					if ( strtotime( $value['end_date'] ) < strtotime( $dd ) ) :

						unset( $spreads[ $key ] );
						/*echo 'unset:' . $key . '(row 82)'; */

					else :
						$ev_id = $key;

						include locate_template( 'tpl/parts/row-calendar.php' );

						if ( strtotime( $value['end_date'] ) < strtotime( date( 'Y-m-d', strtotime( $dd . '+1 days' ) ) ) ) :

							unset( $spreads[ $key ] );

						endif;

					endif;

				endforeach;
			endif;
			// END SPREADED EVENTS.
			?>

		<?php endif; ?>


		<?php
		$ev_id = $event_archive_query->ID;
		$ev_id = url_to_postid( get_the_permalink() );

		include locate_template( 'tpl/parts/row-calendar.php' );
		?>

		<?php /* if ( $start_date !== $end_date && ! array_key_exists( get_the_ID(), $spreads ) ) : */ ?>
		<?php if ( strtotime( $start_date ) < strtotime( $end_date ) && ! array_key_exists( $ev_id, $spreads ) ) : ?>

			<?php
			$spreads[ $ev_id ]['title']      = get_the_title( $ev_id );
			$spreads[ $ev_id ]['start_date'] = $start_date;
			$spreads[ $ev_id ]['end_date']   = $end_date;
			?>

		<?php endif; ?>



		<?php
		// SHOW EVENTS IN SPREADS EVEN IF THERE NO MORE EVENTS IN WP_QUERY.
		// If this is the last event in wp_query but there's still events in $spreads… .
		if ( $event_archive_query->current_post + 1 === $event_archive_query->post_count ) :

			/* usort($spreads, 'spread_events_date_compare'); // Order array based on end_date */
			$count_spr = count( $spreads ); // Get numbers of events in the spreads variable.

			while ( $count_spr > 0 ) : // Loop through $spreads while there's value in it.

				$tmp = date( 'Y-m-d', strtotime( $dd . '+1 days' ) ); // Get value + 1 day.

				// If new date has same month then set $dd to $tmp.
				if ( date( 'm', strtotime( $tmp ) ) === date( 'm', strtotime( $dd ) ) ) {
					$dd = $tmp;
				} else {
					break;
				} // Otherwise stop the loop
				?>
				<tr class="date">
					<td colspan="4">
						<?php echo date( 'l j F', strtotime( $dd ) ); ?>
					</td>
				</tr>

				<?php
				foreach ( $spreads as $key => $value ) :

					$ev_id = $key;
					include locate_template( 'tpl/parts/row-calendar.php' );

					if ( strtotime( $value['end_date'] ) === strtotime( $dd ) ) :
						unset( $spreads[ $key ] );

						$count_spr--;
					endif;

				endforeach;

			endwhile;

		endif;
		// END SPREADS AFTER WP_QUERY.
		?>

	<?php endwhile; ?>


<?php else : ?>
	<tr>
		<td>No events found</td>
	</tr>


	<?php
endif;
