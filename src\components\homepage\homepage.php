<?php
/**
 * Template part to display the Homepage.
 *
 * @package twkmedia
 */

$title = get_sub_field('title');

?>

<section class="relative top-0 left-0 overflow-hidden flex lg:items-center min-h-screen bg-blue pt-[126px] py-20 lg:pt-20 [@media(max-height:750px)_and_(min-width:900px)]:pt-[140px]" data-element="home_banner">
    
    <a href="#home_slide" class="table scroll-to z-30 absolute left-1/2 -translate-x-1/2 lg:translate-x-0 lg:left-auto bottom-20 lg:right-20 button after:bg-arrow-right-gold after:rotate-90 hover:after:translate-x-0 hover:after:-translate-y-0.5 mb-14 lg:mb-0 mx-auto lg:mx-0 md:mt-10 lg:mt-0">Start exploring</a>
    
   
 

    <div class="z-10 absolute inset-0 w-full h-full pointer-events-none overflow-hidden opacity-0 duration-50000" data-target="video_home">
  
        <iframe class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2  w-[100vw] h-[56.25vw] min-h-[100vh] min-w-[177.77vh]" src="https://player.vimeo.com/video/<?php echo get_field('vimeo_id_home')."?dnt=1"; ?>&background=1&autoplay=1&loop=1&muted=1&playsinline=1&quality=720p&controls=0&sidedock=0&title=0"
            frameborder="0"
            allow="autoplay; fullscreen; picture-in-picture"
            allowfullscreen
        >
        </iframe>
    </div>
   
</section>


<section id="home_slide" data-element="home_slide_wrap" class="relative z-10 overflow-hidden">
    <div data-element="home_slide_img_wrap" class="hidden absolute lg:flex w-1/2 min-h-screen lg:h-screen top-0 right-0 z-10">
        <?php if( have_rows('slide_content') ): ?>
            <?php $count = 1; while( have_rows('slide_content') ): the_row(); $image = get_sub_field('image'); $link = get_sub_field('cta');?>
                
                    <?php if( !empty( $image ) ): ?>
                        <?php $zindex = 10 - $count; ?>
                        <img style="z-index:<?= $zindex;?>" data-count="<?= $count;?>" data-element="home_slide_img" class="absolute w-full min-h-screen lg:h-screen object-cover inset-0 " src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                    <?php endif; ?>
            
                
            <?php $count++; endwhile; ?>
        <?php endif; ?>
        <?php if( have_rows('slide_content') ): ?>
        <div data-element="home_slide_menu_wrap" class="absolute bottom-12 right-1/4 translate-x-1/4 inline-flex justify-center items-center bg-grey rounded-[26px] py-4 px-11 pb-0 z-20">
            <?php $count = 1; while( have_rows('slide_content') ): the_row(); $image = get_sub_field('image'); $link = get_sub_field('cta');?>
                
                <div data-element="home_slide_menu"  data-count="<?= $count;?>" data-border="<?php the_sub_field('bg_color');?>" class="font-aspectbold text-titlesm text-blue uppercase px-2 pb-2.5 text-center">
                    <?php the_sub_field('title_section');?>
                </div>
            <?php $count++; endwhile; ?>
            <span data-element="home_slide_menu_line" class="h-[5px] absolute duration-300 left-0 bottom-0"></span>
        </div>
    <?php endif; ?>
    </div> 
    <div class="" style="">
        <div class="container px-10 lg:px-0">
        <?php if( have_rows('slide_content') ): ?>
            <?php $count = 1; while( have_rows('slide_content') ): the_row(); $image = get_sub_field('image'); $link = get_sub_field('cta');?>
                
                <div data-count="<?= $count;?>" data-element="home_slide_bg" data-bg="<?php the_sub_field('bg_color');?>" class="relative  lg:w-5/12 min-h-screen lg:h-screen flex items-center justify-start py-10 lg:py-0 after:content-[''] after:absolute after:top-0 after:-left-[50vw] after:w-[150vw] after:h-full ">
                    <div class="relative z-10">
                        <h2 class="font-aspectregular text-title30 leading-[33px] lg:text-title text-white uppercase mb-5 <?php if(get_sub_field('bg_color') == "yellow"): echo 'text-blue'; endif; ?>"><?php echo get_sub_field('title');?></h2>
                    
                        <p class="text-white text-base <?php if(get_sub_field('bg_color') == "yellow"): echo 'text-blue'; endif; ?>"><?php echo get_sub_field('copy');?></p>
                        
                        <?php if( $link ): $link_url = $link['url'];$link_title = $link['title'];$link_target = $link['target'] ? $link['target'] : '_self';?>
                            <a class="button  text-white mt-12  <?php if(get_sub_field('bg_color') == "blue"): echo 'bg-cranleighclay text-black after:bg-arrow-right-gold'; else: echo 'bg-blue after:bg-arrow-right-yellow'; endif; ?>" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php $count++; endwhile; ?>
        <?php endif; ?>

        
        </div>
    </div>
    
   
</section>

<section class="mt-[100px] mb-[60px] overflow-hidden lg:overflow-visible" data-element="big_image_wrap_pin">
    <div class="container" data-element="big_image_wrap">
        <div  class="lg:w-10/12 mx-auto grid grid-cols-10 grid-rows-[80px_20px_20px_150px_0px] lg:grid-rows-[130px_90px_90px_130px_130px] gap-4 lg:gap-8
            [&:nth-child(1)]:children:col-start-4 [&:nth-child(1)]:children:col-span-4 [&:nth-child(1)]:children:row-start-2  [&:nth-child(1)]:children:row-span-3  
            [&:nth-child(2)]:children:col-start-6 [&:nth-child(2)]:children:col-span-2 [&:nth-child(2)]:children:row-start-1  [&:nth-child(2)]:children:row-span-1  
            [&:nth-child(3)]:children:col-start-1 [&:nth-child(3)]:children:col-span-3 [&:nth-child(3)]:children:row-start-1  [&:nth-child(3)]:children:row-span-3  [&:nth-child(3)]:children:lg:pt-20 [&:nth-child(3)]:children:lg:pl-24 
            [&:nth-child(4)]:children:col-start-8 [&:nth-child(4)]:children:col-span-3 [&:nth-child(4)]:children:row-start-2  [&:nth-child(4)]:children:row-span-3  [&:nth-child(4)]:children:lg:pr-24
            [&:nth-child(5)]:children:col-start-2 [&:nth-child(5)]:children:col-span-2 [&:nth-child(5)]:children:row-start-4  [&:nth-child(5)]:children:row-span-2 [&:nth-child(5)]:children:lg:pb-20 [&:nth-child(5)]:children:lg:pl-20
        ">
            <?php  $image = get_field('big_image_gallery');
            if( !empty( $image ) ): ?>
                <img data-element="big_image_gallery" class="relative h-full w-full object-cover z-10" src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
            <?php endif; ?>
            <?php if( have_rows('image_gallery') ): ?>
                <?php while( have_rows('image_gallery') ): the_row(); $image = get_sub_field('image');?>
                <?php 
                if( !empty( $image ) ): ?>
                    <?php echo twk_output_acf_image($image, 'large', 'h-full w-full object-cover' , 'lazy'); ?>
                <?php endif; ?>
                <?php endwhile; ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<section class="mb-20 xl:-mb-32 relative z-10 " data-element="home_img_gallery" data-anim="fade">
    <div class="container flex justify-start pt-0 lg:pt-6 px-10 lg:px-0 -mt-[30vmin] md:mt-0" >
        <div class="lg:w-7/12 lg:pr-20 wysiwyg lg:-translate-y-12">
            <?php the_field('copy_image_gallery'); ?>
        </div>
    </div>
</section>
<section class="relative overflow-hidden pb-[60px] lg:pb-[100px] mb-10"  data-element="sliding_image_wrap" data-anim="fade">
    
    
    <?php if( have_rows('stats_carousel_stats') ): ?>
        <div class="container flex justify-start lg:justify-end  px-10 lg:px-0 mt-16 lg:mt-0"  data-anim="fade">
            <div class="w-10/12 lg:w-4/12 bg-heathlandgreen py-[68px] lg:px-[68px]">
                <div class="swiper pb-10  cursor-default" data-element="stats_home" >
                    <div class="swiper-wrapper">
                    <?php while( have_rows('stats_carousel_stats') ): the_row();?>
                    
                        <div class="swiper-slide px-10 lg:px-0">
                            <h3 class="font-aspectregular text-titlestats xl:text-titlelg text-white uppercase text-center mb-5"><?php echo get_sub_field('title');?></h3>
                            <p class="font-aspectregular text-titlesm lg:text-copy16 text-white uppercase text-center"><?php echo get_sub_field('copy');?></p>
                        </div>
                    <?php endwhile; ?>
                    </div>
                    <div class="swiper-pagination"></div>
                </div>
                </div>
        </div>
    <?php endif; ?>
     


    <div class="absolute bottom-[10vw] md:bottom-[2vw]  lg:bottom-[5vw] left-0 w-[max-content] flex " data-element="sliding_image_lr" >
        <?php if( have_rows('sliding_images_stats') ): ?>
        <?php while( have_rows('sliding_images_stats') ): the_row(); $image = get_sub_field('image');?>
        <?php 
        if( !empty( $image ) ): ?>
            <?php echo twk_output_acf_image($image, 'large', 'w-[83vw] lg:w-[40vw] aspect-[16/9] object-cover mr-8' , 'lazy'); ?>
        <?php endif; ?>
        <?php endwhile; ?>
        <?php endif; ?>
    </div>
    </div>


</section>


<section>
    <div class="container overflow-hidden lg:overflow-visible -mt-8 lg:mt-0">
        <div class="lg:flex justify-between items-end lg:w-10/12 mx-auto">
            <div class="lg:w-6/12 wysiwyg px-10 lg:px-0" data-anim="fade">
                <?php the_field('copy_leadteam'); ?>
            </div>
            <div data-anim="grid_gallery" class="lg:w-6/12 grid grid-cols-6 grid-rows-3 gap-4 lg:gap-8 mt-10 lg:mt-0 children:opacity-0
            [&:nth-child(1)]:children:col-start-2 [&:nth-child(1)]:children:col-span-2 [&:nth-child(1)]:children:lg:pl-8 
            
            [&:nth-child(2)]:children:col-start-4 [&:nth-child(2)]:children:col-span-3 [&:nth-child(2)]:children:row-span-2 [&:nth-child(2)]:children:pr-8 [&:nth-child(2)]:children:lg:pt-10
            
            [&:nth-child(3)]:children:col-start-1 [&:nth-child(3)]:children:col-span-3 [&:nth-child(3)]:children:row-span-2 [&:nth-child(3)]:children:lg:pl-10 [&:nth-child(3)]:children:lg:pb-24

            [&:nth-child(4)]:children:col-start-4 [&:nth-child(4)]:children:col-span-3 [&:nth-child(4)]:children:lg:pb-10
            ">
                <?php if( have_rows('gallery_leadteam') ): ?>
                    <?php while( have_rows('gallery_leadteam') ): the_row(); $image = get_sub_field('image');?>
                    <?php 
                    if( !empty( $image ) ): ?>
                        <?php echo twk_output_acf_image($image, 'large', 'min-h-full w-full object-cover' , 'lazy'); ?>
                    <?php endif; ?>
                    <?php endwhile; ?>
                <?php endif; ?>
            </div>
            
        </div>
    </div>
</section>

<section class="mt-20 lg:mt-40" data-anim="fade">
    <div class="container px-10 lg:px-0">
        <div class="lg:flex justify-between items-end lg:w-10/12 mx-auto">
            <div class="relative lg:w-7/12 lg:pt-20">
                <h2 class="font-aspectregular text-blue uppercase text-title30 lg:text-title"><?php the_field('title_newstab'); ?></h2>
            </div>
            <?php $link = get_field('cta_newstab');
            if( $link ): 
                $link_url = $link['url'];
                $link_title = $link['title'];
                $link_target = $link['target'] ? $link['target'] : '_self';
                ?>
                <a class="button mt-6 lg:mt-0" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
            <?php endif; ?>
        </div>
        
    </div>
</section>

<?php include locate_template('src/components/homepage/news.php'); ?>