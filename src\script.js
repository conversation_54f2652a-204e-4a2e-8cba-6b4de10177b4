// Components
import accordion from './components/accordion';
import homepage from './components/homepage';
import testimonial from './components/testimonial';
import stats from './components/stats';
import video from './components/video';
import gallery from './components/gallery';
import video_gallery from './components/video_gallery';
import history from './components/history';
import houses from './components/houses';
import contact from './components/contact';
import admission from './components/admission';
import where_next from './components/where_next';
import departmentstaff from './components/department_staff';
import blockquote from './components/blockquote';


import Lenis from '@studio-freight/lenis'
const mediaQuery = window.matchMedia('(min-width: 1024px)')
 
const lenis = new Lenis({
  duration: .6,
  easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // https://www.desmos.com/calculator/brs54l4xou
  direction: 'vertical', // vertical, horizontal
  gestureDirection: 'vertical', // vertical, horizontal, both
  smooth: true,
  mouseMultiplier: 1,
  smoothTouch: false,
  touchMultiplier: 2,
  infinite: false,
})

//get scroll value
// lenis.on('scroll', ({ scroll, limit, velocity, direction, progress }) => {
//   console.log({ scroll, limit, velocity, direction, progress })
// })

function raf(time) {
  lenis.raf(time)
  requestAnimationFrame(raf)
}

requestAnimationFrame(raf)

// Packages
import 'magnific-popup';



function init() {
	accordion();
	
	if($("[data-element='home_banner']").length) {
		homepage();
		
	}
	testimonial();
	stats();
	video();
	gallery();
	video_gallery();
	history();
	houses();
	contact();
	admission();
	where_next();
	departmentstaff();
	blockquote();
}

init();


import Swiper, { Navigation, Pagination, Scrollbar, Autoplay, FreeMode } from 'swiper';
import gsap from 'gsap';
import ScrollTrigger from "gsap/ScrollTrigger";

jQuery(document).ready(function ($) {
    consoleMessage: {
        console.log('%cCreated by %cTWK%cwww.thewebkitchen.co.uk', 'background: #13212E; color: #FFFFFF; padding: 5px 0px 5px 10px; margin: 25px 0px;', 'background: #13212E; color: #05E5C8; padding: 5px 10px 5px 0px; font-weight: bold;', 'background: #FFFFFF; padding: 5px 10px;');
	}
	

	davids_additions: {
		$( window ).on("load", function() { 
			//console.info('video playloist main');
				  const control = document.querySelector('[data-name="select-fade-carousel"]');
				  if(control){
				  console.info(control);
					
					var swiper = new Swiper("[data-name='fade-carousel']", {
						modules: [Navigation, Pagination, Scrollbar, Autoplay],
						loop: false,
						slidesPerView: 1,
				   
						// navigation: {
						// 	nextEl: ".swiper-button-next",
						// 	prevEl: ".swiper-button-prev",
						// },
					});
			
					control.addEventListener('change', function() {
					  //console.log('You selected: ', this.value);
					  swiper.slideTo(this.value)
					});
				}
				});
	}
	
	menu: {
	
		


		gsap.set($(".sub-menu"), { autoAlpha: 0 });
	
		// $('[data-js="full-menu-toggle"]').on('click', function (e) {
		// 	console.info('open mneu');
		// 	gsap.to($('[data-js="main-nav"]'), { autoAlpha: 1 });
		// });
		// $('[data-js="full-menu-toggle"]').on('click', function (e) {
		// 	console.info('open mneu');
		// 	gsap.to($('[data-js="main-nav"]'), { autoAlpha: 1 });
		// });
	
		// Add needed classes to the menu - ex. current-page-ancestor
		let activeMenuItem = document.querySelector(".current-page-item");
	
		if (activeMenuItem) {
		  activeMenuItem.parentNode?.parentNode?.classList.add(
			"current-page-ancestor"
		  );
		}
	
		const hamburger = document.querySelector('[data-js="full-menu-toggle"]');
		const bgO = document.querySelector('[data-js="bg-o"]');
		const bgOwhite = document.querySelector('[data-js="bg-white-o"]');
		const menus = document.querySelector('[data-js="main-nav"]');
		const backLink           = document.querySelectorAll( '.sub-menu-0 .menu-back')
		let isMobile = false
			if ( $(window).width() < 768 ) {
				isMobile = true
			}
	
		
	
		const subMenus = document.querySelectorAll(
		  '[data-js="main-nav"] .sub-menu-0'
		);
		const mainMenuItems = document.querySelectorAll(
		  '[data-js="main-nav"] #main-menu > li.menu-item > .link-container'
		);
		
		const hasChildren = document.querySelectorAll(
		  '[data-js="main-nav"] #main-menu > .menu-item-has-children > .link-container > a'
		);
		const hasNoChildren = document.querySelectorAll(
		  '[data-js="main-nav"] .menu-item:not(.menu-item-has-children) > .link-container > a'
		);
		
		const hamlines = hamburger.querySelectorAll(".block span");
		const pagewrap = document.querySelector("#page-wrap");
	
		let hamtl = gsap.timeline({ paused: true });
		hamtl
		  .to(hamlines[1], { opacity: 0, duration: 0.3 }, "start")
		  .to(
			hamlines[0],
			{
			  rotateZ: 45,
			  y: "2px",
			  transformOrigin: "center center",
			  duration: 0.3,
			},
			"start"
		  )
		  .to(
			hamlines[2],
			{
			  rotateZ: -45,
			  y: "-2px",
			  transformOrigin: "center center",
			  duration: 0.3,
			},
			"start"
		  );
		

		$(document).click(function(event) { 
			var $target = $(event.target);
			if(!$target.closest('[data-js="main-nav"]').length && 
			$('#page-wrap').hasClass("open")  && (!$target.closest('[data-js="full-menu-toggle"]').length)) {
				hamburger.classList.remove("open");
			pagewrap.classList.remove("open");
			pagewrap.classList.remove("submenu-open");
				gsap.to('.link-container', {
					y:"0px",
					overwrite: true,
					duration:0.01
				});
			// Closing the menu
			menus.classList.remove("open");
			$("body").removeClass("h-full overflow-hidden");
			
			const linkContainer = document.querySelectorAll(".link-container");
			for (var i = 0; i < linkContainer.length; ++i) {
				//linkContainer[i].classList.remove("sub-menu-open");
			}
			gsap.to( subMenus, { x: '100vw', ease: 'power2', delay:.5 } )
			
			hamtl.reverse();

			let tl = gsap.timeline();
			tl.to(menus, { autoAlpha: 0 });
			}        
		});



		// Open and close menu - Hamburger clicks.
		hamburger.addEventListener(
		  "click",
		  function () {
			
		
				
			hamburger.classList.toggle("open");
			pagewrap.classList.toggle("open");
			pagewrap.classList.remove("submenu-open");
			if (hamburger.classList.contains("open")) {
				
			  // Opening the menu
			  menus.classList.add("open");
			  gsap.to(menus, { autoAlpha: 1 });
	
			  hamtl.play();
			  $("body").addClass("h-full overflow-hidden");
	
			  let innerPage = false;
			  mainMenuItems.forEach((menuItem) => {
				if (
				  menuItem.parentNode.classList.contains("current-page-ancestor")
				) {
				  innerPage = true;
				  menuItem.querySelector("a").click();
				  $('#main-menu .current-page-ancestor.current-page-parent > div a').trigger( "click" );				}
			  });
	
			  if (!innerPage) {
				let tl = gsap.timeline();
				tl.fromTo(
				  mainMenuItems,
				  { alpha: 0, y: 50 },
				  { alpha: 1, y: 0, stagger: 0.1 }
				)
				  
				  .fromTo(bgO, { opacity: 0 }, { opacity: 0.05 }, "-=1");

				 

					
			  } else {
				
			  }
			  
			  // Open submenu if in current subpage.
			} else {
				gsap.to('.link-container', {
					y:"0px",
					overwrite: true,
					duration:0.01
				  });
			  // Closing the menu
			  menus.classList.remove("open");
			  $("body").removeClass("h-full overflow-hidden");
			  const linkContainer = document.querySelectorAll(".link-container");
			  for (var i = 0; i < linkContainer.length; ++i) {
				//linkContainer[i].classList.remove("sub-menu-open");
			  }
			  gsap.to( subMenus, { x: '100vw', ease: 'power2', delay:.5 } )
			  
			  hamtl.reverse();
	
			  let tl = gsap.timeline();
			  tl.to(menus, { autoAlpha: 0 });
			}
		  },
		  false
		);
	
		// Open and close sub-menu - Menu item with children clicks.
		hasChildren.forEach((menuItem) => {
		  menuItem.addEventListener(
			"click",
			function (e) {
				
			  e.preventDefault();
			  // gsap.to(mainMenuItems, {opacity: 0.5, duration:0.3, overwrite: true})
			  

			  pagewrap.classList.add("submenu-open");
			  gsap.to(bgO, { opacity: 0, duration: 0.3 });
			  gsap.to(bgOwhite, { opacity: 0.05, duration: 0.3 });
			  gsap.to(menus, { backgroundColor: "#0C223F", duration: 0.3 });
	
			  let mainMenuItemsFiltered = [...mainMenuItems].filter(
				(mainMenuItem) => {
				  return (
					mainMenuItem.closest("li").classList !=
					menuItem.closest("li").classList
				  );
				}
			  );
			  const topvalue = $(this).offset().top - 70 - window.scrollY;
			  //gsap.to($(this), {opacity:0,  duration:.2, delay:.1})
			  //gsap.to( $(this), {opacity:1, duration:.4,delay:.5})

			 
			  
			  // gsap.set(menuItem.closest('.link-container'), {opacity: 1});
			  if (
				!menuItem
				  .closest(".link-container")
				  .classList.contains("sub-menu-open")
			  ) {
				const subMenu = menuItem
				  .closest(".menu-item")
				  .querySelector(".sub-menu-0");
				//const subMenuItems = subMenu.querySelectorAll("#main-menu > li > .sub-menu-0 > li.menu-item");
				
				

				// Opening the sub-menu
				let tl = gsap.timeline();
				if (mediaQuery.matches) {
					gsap.set(subMenu, { autoAlpha: 1, x:'50vw' });
					tl.to(subMenus, { autoAlpha: 0, x:'50vw' }, "<")
					.to(subMenu, { autoAlpha: 1, height:'100%', x:'0' }, "<")
				} else {
					gsap.set(subMenu, { autoAlpha: 1, x:'100vw' });
					tl.to(subMenus, { autoAlpha: 0, x:'100vw' }, "<")
					.to(subMenu, { autoAlpha: 1, height:'100%', x:'0' }, "<")
				}
				
				 
				
	
				const linkContainer = document.querySelectorAll(".link-container");
				for (var i = 0; i < linkContainer.length; ++i) {
				  linkContainer[i].classList.remove("sub-menu-open");
				}
				//menuItem.closest(".link-container").classList.add("sub-menu-open");
			  }
			},
			false
		  );
		});
	
		// Close menu before loading a new page - Menu item clicks.
		hasNoChildren.forEach((menuItem) => {
		  menuItem.addEventListener(
			"click",
			function (e) {
			  // Closing the menu before load (if open in same page)
			  if (!e.ctrlKey) {
				//gsap.to("body", { alpha: 0, duration: 0.4 });
				
			  }
			},
			false
		  );
		});
	
		backLink.forEach(back => {
				back.addEventListener('click', function () {
					pagewrap.classList.remove("submenu-open");
			const linkContainer = document.querySelectorAll(".link-container");
			for (var i = 0; i < linkContainer.length; ++i) {
			  //linkContainer[i].classList.remove("sub-menu-open");
			}
			gsap.to(linkContainer, {
			  opacity: 1,
			  duration: 0.3,
			  overwrite: true,
			});
			
			gsap.set(mainMenuItems, {
				y:"0px",
				overwrite: true,
			  });
			  		if (mediaQuery.matches) {
						gsap.to( subMenus, { x: '50vw', ease: 'power2' } )
					} else {
						gsap.to( subMenus, { x: '100vw', ease: 'power2' } )
					}
				})
			});
			
		
		gsap.set('.sub-menu-1', { height: 0});
		gsap.set('.sub-menu-2', { height: 0});

		$('.sub-menu-1').prev().find('a').click(function(e) {
			e.preventDefault();
			if($(this).hasClass('actv')){
				gsap.to($(this).parent().next(), { height: 0, autoAlpha:0, duration: 0.5});
			} else {
				gsap.to($(this).parent().next(), { height: 'auto', autoAlpha:1, duration: 0.5});
			}
			$(this).toggleClass('actv');
			
			
		});

	


		
	  }
	tinglemodal: {
		$( window ).on("load", function() {
			$('.tingle-modal').attr('data-lenis-prevent', '')
			$('.gform_previous_button').val('Previous')
		});
		
	}
	quicklinks: {
		document.querySelector('[data-element="quick_links"]').addEventListener('click', function () {
			var quicklinsmenu = $('[data-element="quick_links_menu"]');
			var state = $('[data-element="quick_links_menu"]').attr('data-state');
					$(this).toggleClass('actv');
					if (state === 'closed') {
						quicklinsmenu.attr('data-state', 'open');
						gsap.to(quicklinsmenu, { height: 'auto', duration: 0.5});
					} else {
						quicklinsmenu.attr('data-state', 'closed');
						gsap.to(quicklinsmenu, { height: 0, duration: 0.5});
					}
		});
	}
	noticepopup: {
		if($('[data-element="notice_popup"]').length) {
			document.querySelector('[data-element="notice_popup_close"]').addEventListener('click', function () {
				const quicklinsmenu = $('[data-element="notice_popup"]');
				gsap.to(quicklinsmenu, {x:"+=100%", duration:.5, ease: "power2.in"})
			});
		}
	}
	sidebarblockonscroll: {
		if($('[data-element="sidebar"]').length) {
			var sidebarblock = $('[data-element="sidebar"] > div:last-child');

			

		}
	}
	anchorlinks: {
		$(document).on('click', '.scroll-to', function (event) {
			event.preventDefault();

			$('html, body').animate({
				scrollTop: $($.attr(this, 'href')).offset().top
			}, 1500);
		});
		$(document).on('click', '.scroll-read', function (event) {
			event.preventDefault();

			$('html, body').animate({
				scrollTop: $($.attr(this, 'href')).offset().top - 64
			}, 1500);
		});
	}

	sidemenu: {
		if($('[data-element="sidebar_menu"]').length) {
			
			$('[data-element="sidebar_menu"] .page_item_has_children .children').each(function (i) {
				var sidbarlink = $(this);
				
				sidbarlink.attr('data-state','closed');
				gsap.set(sidbarlink, {height:0});
				
				$(this).prev().on('click', function(e){
					e.preventDefault();
					gsap.set('.currentmenu', {autoAlpha:0})
					var state = sidbarlink.attr('data-state');
					$(this).toggleClass('actv');
					if (state === 'closed') {
						sidbarlink.attr('data-state','open');
						gsap.to(sidbarlink, { height: 'auto', duration: 0.5,onUpdate: function(){
							if($('[data-element="sidebar_menu"] .current_page_item').parent().attr('data-state') === 'open') {
								if($('[data-element="sidebar_menu"] .current_page_parent').length) {
									if($('[data-element="sidebar_menu"] .current_page_item').parent().parent().parent().attr('data-state') === 'open') {
										var topcurrent = $('[data-element="sidebar_menu"] .current_page_item').offset().top - $('[data-element="sidebar_menu"]').offset().top + $('[data-element="sidebar_menu"]').prev().height() + 58;
										gsap.set('.currentmenu', {autoAlpha:1})
										$('.currentmenu').css('top', topcurrent)
									}
								} else {
									var topcurrent = $('[data-element="sidebar_menu"] .current_page_item').offset().top - $('[data-element="sidebar_menu"]').offset().top + $('[data-element="sidebar_menu"]').prev().height() + 58;
									gsap.set('.currentmenu', {autoAlpha:1})
									$('.currentmenu').css('top', topcurrent)
								}
							}
							if($('[data-element="sidebar_menu"] .current_page_item').parent().attr('data-element') === 'sidebar_menu') {
								var topcurrent = $('[data-element="sidebar_menu"] .current_page_item').offset().top - $('[data-element="sidebar_menu"]').offset().top + $('[data-element="sidebar_menu"]').prev().height() + 58;
								gsap.set('.currentmenu', {autoAlpha:1})
								$('.currentmenu').css('top', topcurrent)
							}
							
						}  });
						
					} else {
						sidbarlink.attr('data-state','closed');

						 gsap.to(sidbarlink, { height: 0, duration: 0.5,onUpdate: function(){
							if($('[data-element="sidebar_menu"] .current_page_item').parent().attr('data-state') === 'open') {
								if($('[data-element="sidebar_menu"] .current_page_parent').length) {
									if($('[data-element="sidebar_menu"] .current_page_item').parent().parent().parent().attr('data-state') === 'open') {
										var topcurrent = $('[data-element="sidebar_menu"] .current_page_item').offset().top - $('[data-element="sidebar_menu"]').offset().top + $('[data-element="sidebar_menu"]').prev().height() + 58;
										gsap.set('.currentmenu', {autoAlpha:1})
										$('.currentmenu').css('top', topcurrent)
									}
								} else {
									var topcurrent = $('[data-element="sidebar_menu"] .current_page_item').offset().top - $('[data-element="sidebar_menu"]').offset().top + $('[data-element="sidebar_menu"]').prev().height() + 58;
									gsap.set('.currentmenu', {autoAlpha:1})
									$('.currentmenu').css('top', topcurrent)
								}
							}
							if($('[data-element="sidebar_menu"] .current_page_item').parent().attr('data-element') === 'sidebar_menu') {
									
								var topcurrent = $('[data-element="sidebar_menu"] .current_page_item').offset().top - $('[data-element="sidebar_menu"]').offset().top + $('[data-element="sidebar_menu"]').prev().height() + 58;
								gsap.set('.currentmenu', {autoAlpha:1})
								$('.currentmenu').css('top', topcurrent)
								
							}
							
						}  });
						
						
					}
				});

			

			});

			$('[data-element="sidebar_menu"] .current_page_parent .children').attr('data-state','open');
			$('[data-element="sidebar_menu"] .current_page_parent a').addClass('actv');
			gsap.to($('[data-element="sidebar_menu"] .current_page_parent .children'), { height: 'auto', duration: 0.5,
			onComplete: function(){
					var topcurrent = $('[data-element="sidebar_menu"] .current_page_item').offset().top - $('[data-element="sidebar_menu"]').offset().top + $('[data-element="sidebar_menu"]').prev().height() + 58;
					gsap.set('.currentmenu', {autoAlpha:1})
					$('.currentmenu').css('top', topcurrent)
					
				} 
			});
			if($('[data-element="sidebar_menu"] .current_page_parent').length) {
				var currentpagepartent = $('.current_page_parent').parent();
				currentpagepartent.attr('data-state','open');
				gsap.to(currentpagepartent, { height: 'auto', duration: 0.5});
			}
		

			
		}
		
		
	}
	ajaxcontent: {
		function ajaxcontent(){
		$('.get_popup').removeClass('pointer-events-none');
		$('.get_popup_staff').removeClass('pointer-events-none');
		$('.get_popupevent').removeClass('pointer-events-none');
		$('.get_popupeventtrip').removeClass('pointer-events-none');
		$('.get_popup_prep').removeClass('pointer-events-none');
		$('.get_popup').on( 'click', function(e) {
            e.preventDefault();
			lenis.stop();
			$('body').addClass('overflow-hidden');
            var postid = $(this).attr('data-postid');
            
			$('#boxcontent').mouseup(function (e) {
				var container = $(".boxpop > div");
				if(!container.is(e.target) &&
				container.has(e.target).length === 0) {
					let tl = gsap.timeline();
					if (mediaQuery.matches) {
						tl.to('#boxcontent > div', { right:'-55vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					} else {
						tl.to('#boxcontent > div', { right:'-100vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					}
					lenis.start();
					$('body').removeClass('overflow-hidden');
				}
			});
            $.ajax({
                type: "POST",
                url: my_ajax_object.ajax_url,
                data: {
                    action: 'my_load_ajax_content',
                    postid: postid,
                }
            }).done(function (data) {
                // Just simple html() method with data will show all the content.
                $('#boxcontent').html(data);
				gsap.to('#boxcontent', {autoAlpha:1, duration:.3});
				if (mediaQuery.matches) {
					gsap.from('#boxcontent > div', { right:'-50vw', duration:.6, delay:.4})
				} else {
					gsap.from('#boxcontent > div', { right:'-100vw', duration:.6, delay:.4})
				}
				$(document).on("click", '[data-element="close_popup"]', function(event) { 
					let tl = gsap.timeline();
					if (mediaQuery.matches) {
						tl.to('#boxcontent > div', { right:'-55vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					} else {
						tl.to('#boxcontent > div', { right:'-100vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					}
					lenis.start();
				});
				init();
            });
        });
		
		$('.get_popup_prep').on( 'click', function(e) {
			
            e.preventDefault();
			lenis.stop();
			$('body').addClass('overflow-hidden');
            var postid = $(this).attr('data-postid');
            
			$('#boxcontent').mouseup(function (e) {
				var container = $(".boxpop > div");
				if(!container.is(e.target) &&
				container.has(e.target).length === 0) {
					let tl = gsap.timeline();
					if (mediaQuery.matches) {
						tl.to('#boxcontent > div', { right:'-55vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					} else {
						tl.to('#boxcontent > div', { right:'-100vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					}
					lenis.start();
					$('body').removeClass('overflow-hidden');
				}
			});
            $.ajax({
                type: "POST",
                url: my_ajax_object.ajax_url,
                data: {
                    action: 'my_load_ajax_content_prep',
                    postid: postid,
                }
            }).done(function (data) {
                // Just simple html() method with data will show all the content.
                $('#boxcontent').html(data);
				gsap.to('#boxcontent', {autoAlpha:1, duration:.3});
				if (mediaQuery.matches) {
					gsap.from('#boxcontent > div', { right:'-50vw', duration:.6, delay:.4})
				} else {
					gsap.from('#boxcontent > div', { right:'-100vw', duration:.6, delay:.4})
				}
				$(document).on("click", '[data-element="close_popup"]', function(event) { 
					let tl = gsap.timeline();
					if (mediaQuery.matches) {
						tl.to('#boxcontent > div', { right:'-55vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					} else {
						tl.to('#boxcontent > div', { right:'-100vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					}
					lenis.start();
				});
				init();
            });
        });
		


		$('.get_popup_staff').on( 'click', function(e) {
            e.preventDefault();
			lenis.stop();
			$('body').addClass('overflow-hidden');
            var postid = $(this).attr('data-postid');
			var blog_id = $(this).attr('data-blog_id');
			
            $('#boxcontent').mouseup(function (e) {
				var container = $("#boxcontent > div");
				if(!container.is(e.target) &&
				container.has(e.target).length === 0) {
					let tl = gsap.timeline();
					if (mediaQuery.matches) {
						tl.to('#boxcontent > div', { right:'-55vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					} else {
						tl.to('#boxcontent > div', { right:'-100vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					}
					lenis.start();
					$('body').removeClass('overflow-hidden');
				}
			});
			
            $.ajax({
                type: "GET",
                url: my_ajax_object.ajax_url,
				dataType : 'html',
                data: {
                    action: 'my_load_ajax_content_staff',
                    idpost: postid,
					blog_id: blog_id,
                }
				
            }).done(function (data) {
                // Just simple html() method with data will show all the content.
                $('#boxcontent').html(data);
				gsap.to('#boxcontent', {autoAlpha:1, duration:.3});
				if (mediaQuery.matches) {
					gsap.from('#boxcontent > div', { right:'-50vw', duration:.6, delay:.4})
				} else {
					gsap.from('#boxcontent > div', { right:'-100vw', duration:.6, delay:.4})
				}
				$(document).on("click", '[data-element="close_popup"]', function(event) { 
					let tl = gsap.timeline();
					if (mediaQuery.matches) {
						tl.to('#boxcontent > div', { right:'-55vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					} else {
						tl.to('#boxcontent > div', { right:'-100vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					}
					lenis.start();
				});
				init();
            });
        });

		


		

		$('.get_popupevent').on( 'click', function(e) {
            e.preventDefault();
			lenis.stop();
			$('body').addClass('overflow-hidden');
            var postid = $(this).attr('data-postid');
            $('#boxcontent').mouseup(function (e) {
				var container = $("#boxcontent > div");
				if(!container.is(e.target) &&
				container.has(e.target).length === 0) {
					let tl = gsap.timeline();
					if (mediaQuery.matches) {
						tl.to('#boxcontent > div', { right:'-55vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					} else {
						tl.to('#boxcontent > div', { right:'-100vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					}
					lenis.start();
					$('body').removeClass('overflow-hidden');
				}
			});
			
            $.ajax({
                type: "POST",
                url: my_ajax_object.ajax_url,
                data: {
                    action: 'my_load_ajax_content_event',
                    postid: postid,
                }
            }).done(function (data) {
                // Just simple html() method with data will show all the content.
                $('#boxcontent').html(data);
				gsap.to('#boxcontent', {autoAlpha:1, duration:.3});
				if (mediaQuery.matches) {
					gsap.from('#boxcontent > div', { right:'-50vw', duration:.6, delay:.4}) 
				} else {
					gsap.from('#boxcontent > div', { right:'-100vw', duration:.6, delay:.4}) 
				}
				$(document).on("click", '[data-element="close_popup"]', function(event) { 
					let tl = gsap.timeline();
					if (mediaQuery.matches) {
						tl.to('#boxcontent > div', { right:'-55vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					} else {
						tl.to('#boxcontent > div', { right:'-100vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					}
					lenis.start();
				});
				init();
            });
        });

		$('.get_popupeventtrip').on( 'click', function(e) {
            e.preventDefault();
			lenis.stop();
			$('body').addClass('overflow-hidden');
            var postid = $(this).attr('data-postid');
            $('#boxcontent').mouseup(function (e) {
				var container = $("#boxcontent > div");
				if(!container.is(e.target) &&
				container.has(e.target).length === 0) {
					let tl = gsap.timeline();
					if (mediaQuery.matches) {
						tl.to('#boxcontent > div', { right:'-55vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					} else {
						tl.to('#boxcontent > div', { right:'-100vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					}
					lenis.start();
					$('body').removeClass('overflow-hidden');
				}
			});
			
            $.ajax({
                type: "POST",
                url: my_ajax_object.ajax_url,
                data: {
                    action: 'my_load_ajax_content_event_trip',
                    postid: postid,
                }
            }).done(function (data) {
                // Just simple html() method with data will show all the content.
                $('#boxcontent').html(data);
				gsap.to('#boxcontent', {autoAlpha:1, duration:.3});
				if (mediaQuery.matches) {
					gsap.from('#boxcontent > div', { right:'-50vw', duration:.6, delay:.4})
				} else {
					gsap.from('#boxcontent > div', { right:'-100vw', duration:.6, delay:.4})
				}
				$(document).on("click", '[data-element="close_popup"]', function(event) { 
					let tl = gsap.timeline();
					if (mediaQuery.matches) {
						tl.to('#boxcontent > div', { right:'-55vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					} else {
						tl.to('#boxcontent > div', { right:'-100vw', duration:.5})
						.to('#boxcontent', {autoAlpha:0, duration:.3});
					}
					lenis.start();
				});
				init();
            });
        });


		
	}
	ajaxcontent();
	$(document).on("sf:ajaxfinish", ".searchandfilter", function(){
		console.log("ajax complete");
		ajaxcontent();
	});
	

		

	}
	logo_carousel: {
		var swiper = new Swiper("[data-element='logo_carousel']", {
			modules: [Navigation, Scrollbar, Autoplay, FreeMode],
			loop: true,
			freeMode: true,
			slidesPerView: 4,
			spaceBetween: 40,
			breakpoints: {
                1024: {
					slidesPerView: 5,
					spaceBetween: 200,
                }
              },
			loop: true,
			autoplay: {
				delay: 1,
				disableOnInteraction: false
			},
			freeMode: true,
			speed: 5000,
			freeModeMomentum: false
		  });
	}
	instagram_carousel: {
		if($('[data-element="instagram_carousel"]').length) {
			var swiper = new Swiper(".swiper[data-element='instagram_carousel']", {
				modules: [Navigation, Pagination, Scrollbar, Autoplay],
				loop: true,
				autoplay: {
				delay: 4000,
			},
				pagination: {
				el: ".swiper-pagination",
				clickable: true,
				},
			});
		}
	}
	copyclass: {
		$('picture').each(function(){
			const classpicture = $(this).attr('class').split(" ");
			$(this).find('img').addClass(classpicture)
		});
	}
	tab: {
		const tabs = document.querySelectorAll('[role="tab"]');
		const tabList = document.querySelector('[role="tablist"]');
		
		
		if (tabs) {
		// Add a click event handler to each tab
		tabs.forEach((tab) => {
			tab.addEventListener("click", function (e) {
			changeTabs(e.currentTarget);
			});
		});

		// Enable arrow navigation between tabs in the tab list
		let tabFocus = 0;

		if (tabList) {
			tabList.addEventListener("keydown", (e) => {
			// Move right
			if (e.keyCode === 39 || e.keyCode === 37) {
				tabs[tabFocus].setAttribute("tabindex", -1);
				if (e.keyCode === 39) {
				tabFocus++;
				// If we're at the end, go to the start
				if (tabFocus >= tabs.length) {
					tabFocus = 0;
				}
				// Move left
				} else if (e.keyCode === 37) {
				tabFocus--;
				// If we're at the start, move to the end
				if (tabFocus < 0) {
					tabFocus = tabs.length - 1;
				}
				}

				tabs[tabFocus].setAttribute("tabindex", 0);
				tabs[tabFocus].focus();
			}
			});
		}

		function changeTabs(target) {
			//control container - find tablist
			const controlList = target.closest('[role="tablist"]');

			const targetID = target.getAttribute("aria-controls");
			const lastIndex = targetID.lastIndexOf("-");
			const allContentSelector = targetID.slice(0, lastIndex);
			const contentList = document.querySelectorAll(
			'[id^="' + allContentSelector + '"]'
			);

			// Remove all current selected tabs
			controlList.querySelectorAll('[aria-selected="true"]').forEach((t) => {
			t.setAttribute("aria-selected", false);
			t.classList.remove("after:bg-cranleighclay");
			t.classList.add("opacity-50");
			
			});

			// Set this tab as selected
			target.setAttribute("aria-selected", true);
			target.classList.add("after:bg-cranleighclay");
			target.classList.remove("opacity-50");

			// Hide all tab panels
			contentList.forEach((p) => p.setAttribute("hidden", true));

			// Show the selected panel
			const selectedPanel = document.querySelector(`#${targetID}`);
			selectedPanel.removeAttribute("hidden");

			let tl = gsap.timeline({});
			
			tl.to(contentList, { autoAlpha: 0, duration: 0.3 })
			//.set(contentList, { height: 0 })
			.set(selectedPanel, { height: "auto" })
			.to(selectedPanel, { autoAlpha: 1, duration: 0.3 });
		}
		}
		if($("button[aria-selected='true']")) {
			$("button[aria-selected='true']").trigger( "click" ); 
		}
	}

	scrolltosearchfilter: {
		$( window ).on("load", function() {
			let url = window.location.href;
			if(url.includes('?_sft')){
				$('html, body').animate({
					scrollTop: $('form').offset().top
				}, 300);
			}
		});
	}
	externallink: {
		$.expr[':'].external = function(obj){
			return !obj.href.match(/^mailto\:/)
				   && (obj.hostname != location.hostname)
				   && !obj.href.match(/^javascript\:/)
				   && !obj.href.match(/^$/)
		};
		$('a:external').attr('target', '_blank');
	}
	loadingAnimations: {
		$.fn.isOnScreen = function () {
			var win = $(window);
			var viewport = {
				top: win.scrollTop()
			};
			viewport.bottom = viewport.top + win.height() - 80;
		
			var bounds = this.offset();
			bounds.bottom = bounds.top + this.outerHeight();
		
			return (!(viewport.bottom < bounds.top || viewport.top > bounds.bottom));
		};

		// First load Animation
		$('.off-screen').each(function (index) {
			if ($(this).isOnScreen()) {
				$(this).removeClass('off-screen--hide');
			}
		});

		// Animation on scroll
		$(window).scroll(function () {
			$('.off-screen').each(function (index) {
				if ($(this).isOnScreen()) {
					$(this).removeClass('off-screen--hide');
				}
			});
		});
	}
});


