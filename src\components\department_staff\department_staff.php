<?php
/**
 * Template part to display the block "Department Staff".
 *
 * @package twkmedia
 */

?>

<h4 class="font-aspectbold text-[28px] leading-[36px] tracking-[-0.75px] lg:text-titleh4 uppercase mb-6 mt-8"><?php echo get_sub_field('title');?></h4>


<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 mb-6">



<?php
$featured_posts = get_sub_field('select_staff');
if( $featured_posts ): ?>
    <?php foreach( $featured_posts as $featured_post ): 
        $permalink = get_permalink( $featured_post->ID );
        $title = get_the_title( $featured_post->ID );
        $the_post_thumbnail_url  = get_the_post_thumbnail_url($featured_post->ID);
        $staff_leadjobtitle = get_field( 'staff_leadjobtitle', $featured_post->ID );
        ?>
        <div class="bg-blue grid grid-cols-3 min-h-[160px] lg:min-h-[189px]">
            <div class="relative  h-full">
                <?php if($the_post_thumbnail_url): ?>
                <img class="absolute inset-0 w-full h-full object-cover" src="<?= $the_post_thumbnail_url; ?>" alt="<?= $title; ?>" />
                <?php else: ?>
                    <img class="absolute inset-0 w-full h-full object-cover" src="<?php the_field('staff_fallback_image', 'options'); ?>" alt="<?= $title; ?>" />
                <?php endif; ?>
            </div>
            <div class=" col-span-2 py-[18px] px-4 flex justify-between flex-col">
                <div>
                    <h3 class="text-[18px] leading-[22px] uppercase text-white "><?= $title; ?></h3>
                    <p class="text-titlesm text-white mt-1"><?= $staff_leadjobtitle; ?></p>
                </div>
                <div>
                    <a class="get_popup_staff_inside text-cranleighclay text-titlesm" href="<?= $permalink; ?>" target="_self" data-postid="<?php echo $featured_post->ID; ?>">Read bio</a>
                </div>
            </div>
           
            
        </div>


    <?php endforeach; ?>
<?php endif; ?>



</div>