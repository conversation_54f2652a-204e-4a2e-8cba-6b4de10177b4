/* @font-face {
    font-family: 'LaGiocondaOSTT';
    src: url('../fonts/la-gioconda-os-tt.eot');
    src: url('../fonts/la-gioconda-os-tt.eot?#iefix') format('embedded-opentype'), url('../fonts/la-gioconda-os-tt.woff2') format('woff2'), url('../fonts/la-gioconda-os-tt.woff') format('woff'), url('../fonts/la-gioconda-os-tt.ttf') format('truetype'), url('../fonts/la-gioconda-os-tt.svg#youworkforthem') format('svg');
    font-weight: normal;
	font-style: normal;
	font-display: swap;
} */
.background-black {
  background-color: #000;
}

.color-black {
  color: #000;
}

.background-white {
  background-color: #fff;
}

.color-white {
  color: #fff;
}

.background-primary {
  background-color: #13212e;
}

.color-primary {
  color: #13212e;
}

.background-secondary {
  background-color: #05E5C8;
}

.color-secondary {
  color: #05E5C8;
}

.color-inherit {
  color: inherit !important;
}

.title {
  font-family: "LaGiocondaOSTT", serif;
}
.title--xs {
  font-size: 16px; /* 16px */
  font-size: 1rem; /* 16px */
  line-height: 1.3125; /* 21px */
}
.title--sm {
  font-size: 24px; /* 24px */
  font-size: 1.5rem; /* 24px */
  line-height: 1.3333333333; /* 32px */
}
.title--md {
  font-size: 30px; /* 30px */
  font-size: 1.875rem; /* 30px */
  line-height: 0.9333333333; /* 28px */
}
.title--lg {
  font-size: 42px; /* 42px */
  font-size: 2.625rem; /* 42px */
  line-height: 1.2380952381; /* 52px */
}
.title--xl {
  font-size: 64px; /* 64px */
  font-size: 4rem; /* 64px */
  line-height: 1; /* 64px */
}
.text {
  font-family: "ingra", sans-serif;
}

.font-sans {
  font-family: "ingra", sans-serif !important;
}

.font-serif {
  font-family: "LaGiocondaOSTT", serif !important;
}

.button, .buttonpdf {
  position: relative;
  display: table;
  border-radius: 33px !important;
  --tw-bg-opacity: 1;
  background-color: rgb(2, 30, 66) !important;
  padding-top: 21px !important;
  padding-bottom: 21px !important;
  padding-left: 35px !important;
  padding-right: 75px !important;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: -0.29px;
  margin: 0 0 20px 0;
  color: #fff !important;
  text-decoration: none;
}

::-moz-selection { /* Code for Firefox */
  color: white;
  background: #05E5C8;
  padding: 10px;
}

::selection {
  color: white;
  background: #05E5C8;
  padding: 10px;
}

#content-wrap {
  position: relative;
}

a.post-edit-link {
  position: fixed;
  z-index: 999;
  bottom: 0;
  background: #13212e;
  color: white;
  padding: 10px 15px;
}

.intro {
  font-size: 18px; /* 18px */
  font-size: 1.125rem; /* 18px */
  line-height: 1.3333333333; /* 24px */
  font-weight: bold;
}

@media (min-width: 768px) {
  .split-list {
    -webkit-column-count: 2;
       -moz-column-count: 2;
            column-count: 2;
  }
}
p,
ul,
ol,
li {
  font-size: 16px; /* 16px */
  font-size: 1rem; /* 16px */
  line-height: 1.5; /* 24px */
}
p a:not(.button),
ul a:not(.button),
ol a:not(.button),
li a:not(.button) {
  color: #05E5C8;
  -webkit-transition: color 0.3s linear;
  transition: color 0.3s linear;
}
p a:not(.button):hover,
ul a:not(.button):hover,
ol a:not(.button):hover,
li a:not(.button):hover {
  color: rgb(3.9102564103, 179.0897435897, 156.4102564103);
}

ul.gallery {
  width: 100%;
  padding: 0;
}
ul.gallery li {
  display: inline-block;
  width: 50%;
  margin: 0;
  padding-bottom: 20px;
}
ul.gallery li:nth-child(2n-1) {
  padding-right: 15px;
}
ul.gallery li:nth-child(2n) {
  padding-left: 15px;
}
@media (max-width: 575.98px) {
  ul.gallery li {
    width: 100%;
  }
}
ul.gallery li:before {
  content: none;
}
ul.gallery li div.gallery__image {
  width: 100%;
  min-height: 250px;
  max-height: 250px;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  margin: 0;
}
ul.gallery li p {
  display: none;
  margin: 0;
}

blockquote {
  position: relative;
  margin: 65px 0px 60px 0px;
}
blockquote p {
  font-family: "ingra", sans-serif;
  font-size: 24px; /* 24px */
  font-size: 1.5rem; /* 24px */
  line-height: 1.25; /* 30px */
  color: #13212e;
  margin: 0;
}
blockquote cite {
  font-family: "ingra", sans-serif;
  font-size: 11px; /* 11px */
  font-size: 0.6875rem; /* 11px */
  line-height: 1.4545454545; /* 16px */
  text-transform: uppercase;
  font-style: normal;
  letter-spacing: 0.85px;
  color: #05E5C8;
}

img {
  max-width: 100%;
  height: auto;
}

.alignleft {
  float: left;
  margin-right: 30px;
}

.alignright {
  float: right;
  margin-left: 30px;
}

.aligncenter {
  display: block;
  margin: 0 auto;
}

.block {
  /* ul:not(.slick-dots){
         padding: 0;
         li + li{
             margin-top: 12px;
         }
         li{
  		position: relative;
             list-style: none;
             padding-left: 25px;
             &:before{
                 position: absolute;
                 top: calc(0.3em + 4px);
                 left: 0px;
                 content: '';
                 height: 8px;
                 width: 8px;
                 background: $secondary;
                 border-radius: 50%;
             }
         }
  } */
}

/*# sourceMappingURL=editor-style.css.map*/