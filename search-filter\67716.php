<?php // Staff 
?>
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 lg:gap-8 mb-10 lg:mb-12 px-6 lg:px-0">
    <?php if ($query->have_posts()) : ?>
            <?php
            while ($query->have_posts()) : $query->the_post();  ?>
          
           <div class="relative mb-0 lg:mb-4">
                <?php if(get_the_post_thumbnail_url()): ?>
                    <img class="aspect-[1] object-cover w-full" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
                <?php else: ?>
                    <img class="aspect-[1] object-cover w-full" src="<?php the_field('staff_fallback_image', 'options'); ?>" alt="<?php the_title();?>" />
                <?php endif; ?>
                <h2 class=" text-titlesm uppercase mt-4"><?php the_title(); ?></h2>
                <h3 class="text-titlesm mt-0.5 lg:mt-0"><?php echo get_field('staff_leadjobtitle');?></h3>
                <a class="get_popup_staff  absolute top-0 left-0 w-full h-full z-20 pointer-events-none" href="<?php the_permalink(); ?>" target="_self" data-postid="<?php echo get_the_ID();?>" data-blog_id="1"></a>
            </div>
                

            <?php 
            endwhile; ?>

            <?php
            $total_pages = $query->max_num_pages;
            $paged = isset($_GET['sf_paged']) ? $_GET['sf_paged'] : 1;

            ?>
            </div>
            <div class="w-full mt-20 mb-20">
                <div class="flex justify-center text-black">

                <?php require locate_template( 'tpl/parts/_pagination.php' ); ?>
            </div>
           
                  
      


    <?php else : ?>
        <div class="text-center">
            <h2 class="">Sorry, no results found</h2>
        </div>
    <?php endif; ?>
   
</div>