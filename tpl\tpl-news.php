<?php
/**
 * Template Name: News
 *
 * @package twkmedia
 */

get_header();

if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();
		?>

		<h1 class="font-aspectbold text-title46 md:text-[10vw] lg:text-[11vw] lg:leading-[11vw] uppercase text-center mt-28 md:mt-[20vw] lg:mt-[10vw] mb-7 lg:mb-0 lg:translate-y-[5vw]"><?php the_field('latest_news');?></h1>
		<div class="container px-10 lg:px-0">
			<?php
			$featured_posts = get_field('featured_post');
			if( $featured_posts ): ?>
				<?php foreach( $featured_posts as $post ): 
					setup_postdata($post); ?>
					<div class="lg:w-11/12 lg:flex justify-between items-center mx-auto">
						<a href="<?php echo get_permalink($post->ID); ?>" class="w-7/12">
							<div class="children:aspect-[3/2] [&_img]:aspect-[3/2] [&_img]:object-cover children:object-cover children:object-center relative flex items-end mb-4">
								<?php if(get_the_post_thumbnail_url()): ?>
									<?php echo twk_output_featured_image_with_fallback($post->ID, 'large', '', 'news') ?>  
								<?php else: ?>
									<?php 
									$pages_fallback_image = get_field('blog_fallback_image', 'options');
									if( !empty( $pages_fallback_image ) ): ?>
										<img class="" src="<?php echo esc_url($pages_fallback_image['url']); ?>" alt="<?php echo esc_attr($pages_fallback_image['alt']); ?>" />
									<?php endif; ?>
								<?php endif; ?>
							</div>
						</a>
						<div class="lg:pl-8 xl:pl-16 lg:w-7/12 xl:w-5/12 mt-0 lg:mt-[3vw] xl:mt-0">
							<div class=" font-aspectbold text-copy16 lg:text-base uppercase">
								FEATURED
							</div>
							<div class=" font-aspectbold text-title30  lg:text-titleh4 xl:text-title uppercase mt-3 mb-3">
							<?php if(get_field('override_title')):?>
								<?php the_field('override_title');?>
							<?php else :?>
								<?php the_title();?>
							<?php endif; ?>
							</div>
							<div class=" "><?php echo twk_excerpt(16);?></div>
							<a href="<?php echo get_permalink($post->ID); ?>" class="relative table text-base text-black pr-8 mt-4 after:content-[''] after:absolute after:top-1/2 after:right-1 after:-translate-y-1/2 after:w-4 after:h-2.5 after:bg-arrow-right-gold after:bg-contain after:bg-center after:bg-no-repeat after:duration-300  after:ease-in hover:after:translate-x-5">Read the article</a>
						</div>
                    
					</div>
				<?php endforeach; ?>
				<?php 
				// Reset the global post object so that the rest of the page works correctly.
				wp_reset_postdata(); ?>
			<?php endif; ?>
		</div>
		<section class=" mt-10  lg:mt-14 lg:mb-14 ">

			<div class="container bg-blue py-5 px-10 mb-12">
				<div class="flex justify-between items-end">
				<?php echo do_shortcode('[searchandfilter id="66427"]'); ?>
				</div>
			</div>

			<div class="container px-10 lg:px-0">
				
				<?php echo do_shortcode('[searchandfilter id="66427" show="results"]'); ?>
			</div>

		</section>

		

		<div class="lg:mt-32">
			<?php include locate_template('src/components/where_next/where_next.php'); ?>
		</div>


		<?php
	endwhile;
endif;

get_footer();
