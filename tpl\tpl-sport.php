<?php
/**
 * Template Name: Sport page
 *
 * @package twkmedia
 */

get_header();

if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();
		?>

		<?php include locate_template('src/components/banner/banner.php'); ?>

		<div class="container px-10 lg:px-0">

			<div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 lg:gap-8 mb-10 lg:mb-12">
				

			<?php
			global $post;

			$myposts = get_posts( array(
				'posts_per_page' => 999,
				'post_type'        => 'sport',
				'orderby'          => 'title',
				'order'          => 'ASC', 
			) );

			if ( $myposts ) {
				foreach ( $myposts as $post ) : 
					setup_postdata( $post ); ?>
					
					<div class="relative mb-4 
					after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-[40%] lg:after:h-[40%]  xl:after:h-[40%] after:bg-gradient-to-b after:from-black/0 after:to-black/70 lg:after:to-black/70">
						<img class="aspect-[3/2] object-cover" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title();?>" />
						<h2 class="absolute bottom-0 left-0 font-aspectregular text-white text-titlemd uppercase z-10 px-6 py-5"><?php the_title(); ?></h2>
						<a class="get_popup absolute top-0 left-0 w-full h-full z-20 pointer-events-none" href="<?php the_permalink(); ?>" target="_self" data-postid="<?php echo get_the_ID();?>"></a>
					</div>
				<?php
				endforeach;
				wp_reset_postdata();
			}
			?>
				


			</div>
		</div>

		<div id="boxcontent" class="boxpop fixed inset-0 bg-black/50 h-screen w-screen z-[60] invisible opacity-0"></div>
		
		<div class="mt-32">
			<?php include locate_template('src/components/where_next/where_next.php'); ?>
		</div>
		


		<?php
	endwhile;
endif;

get_footer();
