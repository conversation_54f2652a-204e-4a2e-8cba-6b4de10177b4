module.exports = {
    content: ["./**/*.php"],
    theme: {
        colors: {
            'white': '#FFF',
            'black': '#000',
            'blue': '#021E42',
            'darkblue': '#06111F',
            'yellow': '#ffc107',
            'red': '#dc3545',
            'weyblue': '#7D9CC0',
            'cranleighclay': '#C09C83',
            'grey': '#D0D0CE',
            'heathlandgreen': '#7F9C90',
        },
        fontFamily: {
            // New font hierarchy
            heading: ['anth', 'sans-serif'],
            subheading: ['proxima-nova', 'sans-serif'], 
            body: ['proxima-nova', 'sans-serif'],
            
            // Legacy font classes for backward compatibility
            aspectbold: ['proxima-nova', 'sans-serif'],
            aspectlight: ['proxima-nova', 'sans-serif'],
            aspectregular: ['anth', 'sans-serif'],
        },
        fontSize: {
            'base': ['18px', {
                lineHeight: '28px',
                letterSpacing: 'normal'
            }],
            'cta': ['18px', {
                lineHeight: '20px',
                letterSpacing: '-0.29px'
            }],
            'titlesm': ['14px', {
                lineHeight: '16px',
                letterSpacing: 'normal'
            }],
            'copy16': ['16px', {
                lineHeight: '22px',
                letterSpacing: 'normal'
            }],
            'copy19': ['19px', {
                lineHeight: '22px',
                letterSpacing: '-0.3px'
            }],
            'titlemd': ['24px', {
                lineHeight: '31px',
                letterSpacing: '-0.25px'
            }],
            'title30': ['30px', {
                lineHeight: '36px',
                letterSpacing: '-0.48px'
            }],
            'titlesidebar': ['32px', {
                lineHeight: '38px',
                letterSpacing: '-0.5px'
            }],
            'titleh4': ['36px', {
                lineHeight: '40px',
                letterSpacing: '-0.75px'
            }],
            'title': ['44px', {
                lineHeight: '48px',
                letterSpacing: '-0.75px'
            }],
            'titlestats': ['48px', {
                lineHeight: '52px',
                letterSpacing: '-0.8px'
            }],
            'titleh2': ['60px', {
                lineHeight: '60px',
                letterSpacing: '-1px'
            }],
            'title46': ['46px', {
                lineHeight: '52px',
                letterSpacing: '-0.77px'
            }],
            'titlelg': ['90px', {
                lineHeight: '90px',
                letterSpacing: '-1.5px'
            }],
            'titlexl': ['172px', {
                lineHeight: '172px',
                letterSpacing: '-4px'
            }],
            'titlefooter': ['24px', {
                lineHeight: '28px',
                letterSpacing: '-0.21px'
            }],
            'textfooter': ['16px', {
                lineHeight: '24px',
                letterSpacing: 'normal'
            }],
        },
        lineHeight: {
            '1': '20px',
            '2': '25px',
            '3': '26px',
            '4': '28px',
            '5': '30px',
            '6': '40px',
            '7': '42px',
            '8': '46px',
            '9': '50px',
            '10': '60px',
            '11': '70px',
            '12': '72px',
            '13': '130px',
            '14': '160px'
        },
        letterSpacing: {
          
        },
        aspectRatio: {

        },
        container: {
            center: true,
            screens: {
                'sm': '640px',
                'md': '768px',
                'lg': '1024px',
                'xl': '1280px',
                '2xl': '1340px'
            }
        },
        spacing: {
            px: "1px",
            0: "0",
            0.5: "2px",
            1: "4px",
            1.5: "6px",
            2: "8px",
            2.5: "10px",
            3: "12px",
            3.5: "14px",
            4: "16px",
            5: "20px",
            6: "24px",
            7: "28px",
            8: "32px",
            9: "36px",
            10: "40px",
            11: "44px",
            12: "48px",
            14: "56px",
            16: "64px",
            20: "80px",
            24: "96px",
            28: "112px",
            32: "128px",
            36: "144px",
            40: "160px",
            44: "176px",
            48: "192px",
            52: "208px",
            56: "224px",
            60: "240px",
            64: "256px",
            72: "288px",
            80: "320px",
            96: "384px",
          },
         
                    extend: {
            fontWeight: {
                'regular': 400,
                'bold': 700,
            },
            
            backgroundImage: {
                'event-circle' : "url('/wp-content/themes/cranleigh/assets/images/event-circle.svg')",
                'accordion-minus-red' : "url('/wp-content/themes/cranleigh/assets/images/accordion-minus-red.svg')",
                'accordion-plus-red' : "url('/wp-content/themes/cranleigh/assets/images/accordion-plus-red.svg')",
                'accordion-minus-weyblue' : "url('/wp-content/themes/cranleigh/assets/images/accordion-minus-weyBlue.svg')",
                'accordion-plus-weyblue' : "url('/wp-content/themes/cranleigh/assets/images/accordion-plus-weyBlue.svg')",
                'arrow-down-white' : "url('/wp-content/themes/cranleigh/assets/images/arrow-down-white.svg')",
                'arrow-right-black' : "url('/wp-content/themes/cranleigh/assets/images/arrow-right-black.svg')",
                'arrow-right-white' : "url('/wp-content/themes/cranleigh/assets/images/arrow-right-white.svg')",
                'arrow-right-yellow' : "url('/wp-content/themes/cranleigh/assets/images/arrow-right-yellow.svg')",
                'arrow-right-gold' : "url('/wp-content/themes/cranleigh/assets/images/arrow-right-gold.svg')",
                'button-envelope-icon-black' : "url('/wp-content/themes/cranleigh/assets/images/button-envelope-icon-black.svg')",
                'button-pin-icon-black' : "url('/wp-content/themes/cranleigh/assets/images/button-pin-icon-black.svg')",
                'envelope-yellow-navy' : "url('/wp-content/themes/cranleigh/assets/images/envelope-yellow-navy.svg')",
                'event-block-triangle' : "url('/wp-content/themes/cranleigh/assets/images/event-block-triangle.svg')",
                'pagination-previous-navy' : "url('/wp-content/themes/cranleigh/assets/images/pagination-previous-navy.svg')",
                'phone-yellow' : "url('/wp-content/themes/cranleigh/assets/images/phone-yellow.svg')",
                'pin-navy' : "url('/wp-content/themes/cranleigh/assets/images/pin-navy.svg')",
                'play-icon-red' : "url('/wp-content/themes/cranleigh/assets/images/play-icon-red.svg')",
                'quote-mark-red' : "url('/wp-content/themes/cranleigh/assets/images/quote-mark-red.svg')",
                'quote-mark-white' : "url('/wp-content/themes/cranleigh/assets/images/quote-mark-white.svg')",
                'quote-mark-yellow' : "url('/wp-content/themes/cranleigh/assets/images/quote-mark-yellow.svg')",
                'search-icon-white' : "url('/wp-content/themes/cranleigh/assets/images/search-icon-white.svg')",
                'sidebar-menu-arrow-yellow' : "url('/wp-content/themes/cranleigh/assets/images/sidebar-menu-arrow-yellow.svg')",
                'sidebar-menu-arrow-cranleighclay' : "url('/wp-content/themes/cranleigh/assets/images/sidebar-menu-arrow-cranleighClay.svg')",
                'staff-icon' : "url('/wp-content/themes/cranleigh/assets/images/staff-icon.svg')",
                'timeline-fade-out' : "url('/wp-content/themes/cranleigh/assets/images/timeline-fade-out.svg')",
                'where-next-arrow-left-navy' : "url('/wp-content/themes/cranleigh/assets/images/where-next-arrow-left-navy.svg')",
                'menu-minus-yellow' : "url('/wp-content/themes/cranleigh/assets/images/menu-minus-yellow.svg')",
                'menu-plus-yellow' : "url('/wp-content/themes/cranleigh/assets/images/menu-plus-yellow.svg')",
                'menu-minus-cranleighclay' : "url('/wp-content/themes/cranleigh/assets/images/menu-minus-cranleighClay.svg')",
                'menu-plus-cranleighclay' : "url('/wp-content/themes/cranleigh/assets/images/menu-plus-cranleighClay.svg')",
                'quicklinks-arrow-navy' : "url('/wp-content/themes/cranleigh/assets/images/quicklinks-arrow-navy.svg')",
                'calendar-icon-black' : "url('/wp-content/themes/cranleigh/assets/images/calendar-icon-black.svg')",
                'calendar-icon-white' : "url('/wp-content/themes/cranleigh/assets/images/calendar-icon-white.svg')",
                'clock-icon-black' : "url('/wp-content/themes/cranleigh/assets/images/clock-icon-black.svg')",
                'clock-icon-white' : "url('/wp-content/themes/cranleigh/assets/images/clock-icon-white.svg')",
                'email-icon-white' : "url('/wp-content/themes/cranleigh/assets/images/email-icon-white.svg')",
                'icon-search-white' : "url('/wp-content/themes/cranleigh/assets/images/icon-search-white.svg')",
                'icon-search-blue' : "url('/wp-content/themes/cranleigh/assets/images/icon-search-blue.svg')",
                'pdf-icon-navy' : "url('/wp-content/themes/cranleigh/assets/images/pdf-icon-navy.svg')",
                'phone-icon-white' : "url('/wp-content/themes/cranleigh/assets/images/phone-icon-white.svg')",
                'suitcase-icon-navy' : "url('/wp-content/themes/cranleigh/assets/images/suitcase-icon-navy.svg')",
                'suitcase-icon-white' : "url('/wp-content/themes/cranleigh/assets/images/suitcase-icon-white.svg')",
                'close-icon-navy' : "url('/wp-content/themes/cranleigh/assets/images/close-icon-navy.svg')",
                'bio-icon-navy' : "url('/wp-content/themes/cranleigh/assets/images/bio-icon-navy.svg')",
                'close-icon-white' : "url('/wp-content/themes/cranleigh/assets/images/close-icon-white.svg')",
                'close-icon-homepage-notification-navy' : "url('/wp-content/themes/cranleigh/assets/images/close-icon-homepage-notification-navy.svg')",
                'exclamation-mark-icon-navy' : "url('/wp-content/themes/cranleigh/assets/images/exclamation-mark-icon-navy.svg')",

                
                



            }
        }
    },
    safelist: [
        'bg-red',
        'bg-blue',
        'bg-cranleighclay',
        'bg-weyblue',
        'bg-cranleighclay',
        'bg-grey',
        'bg-heathlandgreen',
        'hover:bg-grey',
        'hover:bg-red',
        'hover:bg-blue',
        'hover:bg-weyblue',
        'hover:bg-cranleighclay',
        'hover:bg-heathlandgreen',
        'text-weyblue',
        'text-cranleighclay',
        'text-heathlandgreen',
    ],
    plugins: [
        function ({ addVariant }) {
            addVariant("children", "& > *");
        },
        require("tailwindcss-debug-screens"),
    ],
}

