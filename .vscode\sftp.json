{"protocol": "sftp", "secure": true, "secureOptions": {"rejectUnauthorized": false}, "uploadOnSave": true, "watcher": {"files": "{assets/css/*.css,assets/js/*.js,assets/css/*.map}", "autoUpload": true, "autoDelete": true}, "profiles": {"dev": {"host": "thewebkitchen-dev.nh-serv.co.uk", "port": 22, "username": "USERNAME", "password": "PASSWORD", "remotePath": "./public/wp-content/themes/THEME_NAME"}, "stage": {"host": "cranleighstage.sftp.wpengine.com", "port": 2222, "username": "cranleighstage-twk", "password": "aEZsDpvkyk7-W3hGxUyG", "remotePath": "/wp-content/themes/cranleigh"}, "prod": {"host": "SEERVER", "port": 22, "username": "USERNAME", "password": "PASSWORD", "remotePath": "./public/wp-content/themes/THEME_NAME"}}, "defaultProfile": "stage", "ignore": [".vscode", ".npmrc", ".git", ".giti<PERSON>re", ".DS_Store", ".npmrc", "node_modules", "TODO.md", "deploy.js", "cssEditorStyles.js", "package.json", "package-lock.json", "webpack.config.js", "tailwind.config.js", "twk-boilerplate.config.js"]}