// Pagination.
.pagination{
	margin: 30px 0;

	&__pages,
	.prev,
	.next{
		font-family: $sans;
		@include font-size(20px, 36px);
		letter-spacing: 0.25px;
		color: black;
	}

	.page-numbers {
		display: inline-block;
		padding: 4px 13px;
		border: 4px solid transparent;
		margin: 5px;

		&:hover, &:focus, &:active{
			border: 4px solid rgba($blue, .37);
		}
	}
	.page-numbers.current {
		border: 4px solid rgba($blue, .37);
	}

	.prev,
	.next,
	.placeholder{
		padding-left: 25px;
		position: relative;
		display: inline-block;
		width: 140px;

		/* &::before{
			content: '';
			width: 20px;
			height: 12px;
			background: url( '../img/arrow-prev.svg' ) no-repeat;
			position: absolute;
			top: 16px;
			left: 0;
			transform: rotate(90deg);
		} */
	}
	.next{
		padding-right: 25px;
		text-align: right;

		/* &::before{
			left: auto;
			right: 0;
			transform: rotate(-90deg);
		} */
	}
	.placeholder{
		&::before{
			display: none;
		}
	}
}
