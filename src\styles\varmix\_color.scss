// Colors
$primary: #13212e;    // Replace with primary color variable. ex. $blue
$secondary: #05E5C8;  // Replace with secondary color variable. ex. $red

$color_list : (
	'black': #000,
	'white': #fff,
	'primary': $primary,
	'secondary': $secondary,
);


// This mixin will generate background-color property for the colors in our list
@mixin background-color($args) {
	background-color: $args;
}
// This mixin will generate color property for the colors in our list
@mixin inner-color($args) {
	color: $args;
}

@each $name,$color in $color_list {
	.background-#{$name} { @include background-color($color); }
	.color-#{$name} { @include inner-color($color); }
}

.color-inherit {
    color: inherit !important;
}
