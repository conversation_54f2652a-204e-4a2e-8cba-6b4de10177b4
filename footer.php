<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content-wrap div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package twkmedia
 */

?>



</div>
<!-- END DIV MAIN CONTENT -->
<section class=" relative bg-white pt-20" data-element="footer_logo_carousel">
	<div class="bg-grey overflow-hidden py-[30px]">
	<?php if( have_rows('icon_carousel', 'options') ): ?>
		<div class="" data-element="logo_carousel">
			<div class="swiper-wrapper">
					<?php while( have_rows('icon_carousel', 'options') ): the_row(); ?>
						<div class="swiper-slide  flex items-center justify-center">
							<div class="w-[200px]" style="height:<?php the_sub_field('height');?>px">
								<?php 
								$image = get_sub_field('logo');
								if( !empty( $image ) ): ?>
									<img class="w-full h-full object-contain " src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
								<?php endif; ?>
							</div>
						</div>
					<?php endwhile; ?>
					
			</div>
			
		</div>
		
	<?php endif; ?>
	</div>
</section>

<div id="boxcontent_staff" class="boxpop fixed inset-0 bg-black/50 h-screen w-screen z-[70] invisible opacity-0"></div>
<?php if( have_rows('blocks') ): ?>
    <?php while( have_rows('blocks') ): the_row(); ?>
        <?php if( get_row_layout() == 'event_list' ): ?>
			<div id="boxcontent" class="boxpop fixed inset-0 bg-black/50 h-screen w-screen z-[60] invisible opacity-0"></div>
        <?php endif; ?>
    <?php endwhile; ?>
<?php endif; ?>

<footer class="relative bg-blue pt-12 lg:pt-[76px] pb-8">
	<div class="container px-[30px] lg:px-0 text-left lg:text-left">
		<div class="md:flex mb-12 lg:mb-[50px] justify-between flex-wrap lg:flex-nowrap">
			<div class="block w-full lg:w-3/12 lg:pr-8 mb-[45px] lg:mb-0">
				<?php 
				$footer_logo = get_field('footer_logo' , 'options');
				if( !empty( $footer_logo ) ): ?>
					<img class="h-[116px] lg:h-[151px] w-auto" src="<?php echo esc_url($footer_logo['url']); ?>" alt="<?php echo esc_attr($footer_logo['alt']); ?>" />
				<?php endif; ?>
				<div class="lg:w-3/12 flex flex-wrap lg:flex-nowrap items-center mt-6 justify-start lg:justify-start">
				<?php

					$social_feeds = array('twitter', 'instagram',  'facebook', 'linkedin',  'youtube');

					foreach ($social_feeds as $social_feed) {
						$url = $social_feed . '_url';
						$get_page = 'option';

						if (get_field($url, $get_page)) {
							echo '<a class="mr-3 mb-3 lg:mb-0 last:mr-0 ease-in duration-300 hover:opacity-80 children:w-[34px] children:h-[34px] " href="' . esc_url( get_field( $url, $get_page ) ) . '">';
							include locate_template('assets/images/social/' . $social_feed . '_circle.svg');
							echo '</a>';
						}
					}
					?>
				</div>
			</div>
			
			<div class="md:w-4/12 lg:w-2/12 md:pr-8">
				<h3 class="font-aspectbold text-titlefooter text-white uppercase mt-6 lg:mt-0 mb-3 lg:mb-[30px]"><?php the_field('footer_contact_title','options');?></h3>
				<p class="text-white text-textfooter"><?php the_field('address','options');?></p>
				<p class="text-white text-textfooter mt-2"><?php the_field('phone_number','options');?></p>
				<p class="text-white text-textfooter mt-2"><a href="mailto:<?php the_field('email','options');?>"><?php the_field('email','options');?></a></p>
			</div>

			<div class="md:w-4/12 lg:w-3/12 md:pr-8">
				<h3 class="font-aspectbold text-titlefooter text-white uppercase mt-6 lg:mt-0 mb-3 lg:mb-[30px]"><?php the_field('our_schools_title_footer','options');?></h3>
				<?php if( have_rows('our_schools_links_footer', 'options') ): ?>
					<?php while( have_rows('our_schools_links_footer', 'options') ): the_row();?>
					<?php 
					$link = get_sub_field('link');
					if( $link ): 
						$link_url = $link['url'];
						$link_title = $link['title'];
						$link_target = $link['target'] ? $link['target'] : '_self';
						?>
						<a class="font-aspectbold block text-textfooter text-white uppercase mb-2 lg:mb-4" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
					<?php endif; ?>
					<?php endwhile; ?>
				<?php endif; ?>
				
			</div>

			<div class="md:w-4/12">
				<h3 class="font-aspectbold text-titlefooter text-white uppercase mt-6 lg:mt-0 mb-3 lg:mb-[30px]"><?php the_field('quick_links_title','options');?></h3>
				<div class="grid lg:grid-cols-2 gap-2 lg:gap-4">
					<?php if( have_rows('quick_links_footer', 'options') ): ?>
						<?php while( have_rows('quick_links_footer', 'options') ): the_row();?>
						<?php 
						$link = get_sub_field('link');
						if( $link ): 
							$link_url = $link['url'];
							$link_title = $link['title'];
							$link_target = $link['target'] ? $link['target'] : '_self';
							?>
							<a class="font-aspectbold block text-textfooter text-white uppercase" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
						<?php endif; ?>
						<?php endwhile; ?>
					<?php endif; ?>
				</div>
			</div>


		</div>
		<div class="container lg:flex justify-between items-end">
			<div class="lg:w-3/12">
				<p class="text-white text-titlesm leading-[24px] lg:leading-[16px]"><?php the_field('footer_copy' ,'options');?></p>
			</div>
			<div class="lg:w-4/12 [&_form]:relative [&_.screen-reader-text]:hidden [&_.search-field]:text-textfooter [&_.search-field]:w-full [&_.search-field]:outline-none [&_.search-field]:py-[13px] [&_.search-field]:pl-6 [&_.search-field]:pr-14
			[&_.search-submit]:absolute [&_.search-submit]:top-0 [&_.search-submit]:right-0 [&_.search-submit]:h-full [&_.search-submit]:indent-[9999px] [&_.search-submit]:block [&_.search-submit]:w-14 [&_.search-submit]:bg-icon-search-blue 
			[&_.search-submit]:bg-[29px] [&_.search-submit]:bg-no-repeat [&_.search-submit]:bg-center [&_.search-submit]:cursor-pointer
			">
				<?php get_search_form(); ?>

			</div>
		</div>
		<div class="container lg:flex justify-between items-center border-t border-white/[.49] text-left mt-[35px] lg:mt-20 pt-5 lg:pt-6">
			<div class="text-white lg:flex">
				<ul class="lg:flex">
				<li class="font-regular text-[13px] leading-1  mr-6 mb-[9px] lg:mb-0">&copy; <?php echo date( 'Y' ); ?> <?php the_field('copyright','options');?> </li>
				<?php if( have_rows('legal_menu', 'options') ): ?>
					<?php while( have_rows('legal_menu', 'options') ): the_row(); ?>
						<li class="font-regular text-[13px] leading-1  text-white mr-6 last:mr-0 lg:pl-6 relative after:content-none lg:after:content-[''] after:w-1 after:h-1 after:absolute after:bottom-1/2 after:left-0 after:translate-y-1/2 after:bg-white after:rounded-full  ">
							<?php 
							$link = get_sub_field('link');
							if( $link ): 
								$link_url = $link['url'];
								$link_title = $link['title'];
								$link_target = $link['target'] ? $link['target'] : '_self';
								?>
								<a class="ease-in duration-300 hover:opacity-80" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
							<?php endif; ?>
						</li>		
					<?php endwhile; ?>
				<?php endif; ?>
				</ul>
			</div>

			<div class="text-white mt-[9px] lg:mt-0">
				<p class="font-regular text-[13px] leading-1  ease-in duration-300 hover:opacity-80"><a href="https://www.thewebkitchen.co.uk/" target="_blank" rel="external" class="color-inherit">Web design</a> by <a href="https://www.thewebkitchen.co.uk/" target="_blank" rel="external" class="color-inherit">TWK</a></p>
			</div>
		</div>
	</div>
</footer>

<!-- video modal -->
<div id="modal_video" class="fixed w-full h-full inset-0 flex items-center justify-center invisible opacity-0 bg-black/80 z-[999] duration-300">
	<div id="modal_video_box" class="relative w-[92vw] lg:w-[90vw] bg-black px-2 lg:px-[57px] py-4 lg:py-10 lg:h-[90vh] aspect-[1/1] lg:aspect-auto">
		<div class="flex justify-center items-center w-full h-full">
			<iframe class="w-full h-full" allow="autoplay; fullscreen" allowfullscreen id="modal_video_iframe"></iframe>
		</div>
		<span id="close_modal_video" class="absolute -top-[12px] -right-[10px] w-[24px] h-[24px] bg-close-icon-white bg-no-repeat bg-contain bg-center -indent-[99999px] cursor-pointer">close</span>
	</div>
</div>

<?php if(get_field('show_notificationpop', 'options')): ?>
	<div data-element="notice_popup" class="fixed bottom-0 right-0 w-full lg:w-[590px] bg-cranleighclay px-8 pt-7 pb-8 z-50 opacity-0 invisible">
		<span data-element="notice_popup_close"  class="absolute top-8 right-8 w-[18px] h-[18px] bg-close-icon-homepage-notification-navy bg-contain bg-center bg-no-repeat"></span>
		<?php if(get_field('subtitle_notificationpop','options')):?>
			<p class="font-aspectbold text-titlesm uppercase"><?php the_field('subtitle_notificationpop','options');?></p>
		<?php endif; ?>
		<?php if(get_field('title_notificationpop','options')):?>
			<h4 class="font-aspectbold text-titlesidebar uppercase mb-4"><?php the_field('title_notificationpop','options');?></h4>
		<?php endif; ?>
		<?php if(get_field('copy_notificationpop','options')):?>
			<p class="text-copy16"><?php the_field('copy_notificationpop','options');?></p>
		<?php endif; ?>

		
		<?php 
		$link = get_field('cta_notificationpop','options');
		if( $link ): 
			$link_url = $link['url'];
			$link_title = $link['title'];
			$link_target = $link['target'] ? $link['target'] : '_self';
			?>
			<a class="relative inline-table text-cta text-black bg-white py-[15px] px-[26px] rounded-[33px] mt-7
        	duration-300  hover:bg-blue hover:text-cranleighclay" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
		<?php endif; ?>

			
	</div>

	<?php // Cookies.
	$cookie_name = 'twk_popup_cookie';
	if ( '' === $cookie_name || NULL === $cookie_name ) {
		$cookie_name = 'twk_cookies_popup_00001';
	} ?>

	<script>
		function defer(method) {
			if (window.jQuery) {
				method();
			} else {
				setTimeout(function() { defer(method) }, 50);
			}
		}
		defer(function () {
			(function ($){

				$(window).on('load', function() {
					function getCookie(name) {
						var dc = document.cookie;
						var prefix = name + "=";
						var begin = dc.indexOf("; " + prefix);
						if (begin == -1) {
							begin = dc.indexOf(prefix);
							if (begin != 0) return null;
						}
						else
						{
							begin += 2;
							var end = document.cookie.indexOf(";", begin);
							if (end == -1) {
							end = dc.length;
							}
						}
						// because unescape has been deprecated, replaced with decodeURI
						//return unescape(dc.substring(begin + prefix.length, end));
						return decodeURI(dc.substring(begin + prefix.length, end));
					}
					function setCookie(cname, cvalue, exdays) {
						var d = new Date();
						d.setTime(d.getTime() + (exdays*24*60*60*1000));
						var expires = "expires="+ d.toUTCString();

						if ( exdays == 0 ){
							document.cookie = cname + "=" + cvalue + ";path=/";
						} else {
							document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
						}
					}

					let cookie_name                       = "<?php echo esc_html( $cookie_name ); ?>"; // controls whether we need to show the notification popup or not.
					let popup_notification_cookies_name   = getCookie(cookie_name);				 // Checks if the message was already displayed and closed.
					let cookie_expire                     = "<?php echo get_field( 'expiry_time_notificationpop', 'option' ); ?>";

					if ( ! popup_notification_cookies_name ){ 
						$('[data-element="notice_popup"]').removeClass('opacity-0')
						$('[data-element="notice_popup"]').removeClass('invisible')
					} 
					

					// Close popup.
					$('[data-element="notice_popup_close"]').on('click', function(event){
						event.preventDefault();

						setCookie( cookie_name, 'true', cookie_expire);  // Once its shown, it won't show up again until the cookie expires.

						
					});

					// Accepts the popup cookie and go to link. (Closes the notification popup but going to the link).
					$('[data-element="notice_popup"] a').on('click', function(event){
						event.preventDefault();

						setCookie( cookie_name, 'true', cookie_expire);

						window.location = $(this).attr("href");

					});

				});

			})(jQuery);
		});
	</script>


<?php endif; ?>

</div>
<!-- JS ARE LOADED HERE WITH FUNCTION.PHP -->
<?php wp_footer(); ?>


</body>

</html>