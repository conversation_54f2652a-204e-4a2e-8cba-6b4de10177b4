{"key": "group_5e4fcd306a7a3", "title": "Clone - Columns", "fields": [{"key": "field_5e4fcd39d2378", "label": "Columns", "name": "bs_columns", "type": "radio", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"6": "<PERSON>rrow", "8": "Normal", "10": "Wide"}, "allow_null": 0, "other_choice": 0, "default_value": 10, "layout": "horizontal", "return_format": "value", "save_other_choice": 0}, {"key": "field_5e5004516162a", "label": "Row Alignment", "name": "bs_row", "type": "radio", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"justify-content-start": "<span class=\"alignment-choice alignment-choice--left\">Left align</span>", "justify-content-center": "<span class=\"alignment-choice alignment-choice--center\">Center align</span>", "justify-content-end": "<span class=\"alignment-choice alignment-choice--right\">Right align</span>"}, "allow_null": 0, "other_choice": 0, "default_value": "justify-content-center", "layout": "horizontal", "return_format": "value", "save_other_choice": 0}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 9, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": 0, "description": "", "modified": 1589809863}