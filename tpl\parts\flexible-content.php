<?php

/**
 * Template part to display the flexible content.
 *
 * @package twkmedia
 */

if (post_password_required()) :
?>

	<div class="container">
		<div class="row justify-content-center">
			<div class="col-md-8">
				<div class="gform_wrapper">
					<?php echo get_the_password_form(); ?>
				</div>
			</div>
		</div>
	</div>

	<?php else :
	// check if the flexible content field has rows of data.
	if (have_rows('blocks')) :
		// loop through the rows of data.
		$i = 0;
		while (have_rows('blocks')) :
			the_row();
			$i++;

			$layout = get_row_layout();
			$layout = str_replace('_', '-', $layout);

			// Block outer.
			$background_color = get_sub_field('background_color');
			$margin_sizes     = get_sub_field('margin_size');
			$padding_size     = get_sub_field('padding_size');
			$margin_class     = '';

			if ($margin_sizes) {
				foreach ($margin_sizes as $margin_size) {
					$margin_class .= ' my-' . $margin_size;
				}
			}
	?>

			<section id="<?php echo (get_sub_field('block_custom_id') ? esc_attr(get_sub_field('block_custom_id')) : esc_attr('block-' . $i)); ?>" class="
				block block--<?php echo $layout; ?>
				background-<?php echo $background_color; ?> <?php echo (($background_color !== 'transparent' && $background_color !== false) ? 'color-white' : ''); ?>
				<?php echo $margin_class; ?> py-<?php echo $padding_size; ?>
			">

				<?php include locate_template('tpl/blocks/block-' . $layout . '.php'); ?>

			</section>

<?php
		endwhile;

	endif;
endif;
