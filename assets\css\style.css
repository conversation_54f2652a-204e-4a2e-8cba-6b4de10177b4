/* Magnific Popup CSS */

.mfp-bg {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1042;
  overflow: hidden;
  position: fixed;
  background: #0b0b0b;
  opacity: 0.8;
}

.mfp-wrap {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1043;
  position: fixed;
  outline: none !important;
  -webkit-backface-visibility: hidden;
}

.mfp-container {
  text-align: center;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  padding: 0 8px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

.mfp-container:before {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.mfp-align-top .mfp-container:before {
  display: none;
}

.mfp-content {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0 auto;
  text-align: left;
  z-index: 1045;
}

.mfp-inline-holder .mfp-content,
.mfp-ajax-holder .mfp-content {
  width: 100%;
  cursor: auto;
}

.mfp-ajax-cur {
  cursor: progress;
}

.mfp-zoom-out-cur,
.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
  cursor: -webkit-zoom-out;
  cursor: zoom-out;
}

.mfp-zoom {
  cursor: pointer;
  cursor: -webkit-zoom-in;
  cursor: zoom-in;
}

.mfp-auto-cursor .mfp-content {
  cursor: auto;
}

.mfp-close,
.mfp-arrow,
.mfp-preloader,
.mfp-counter {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
      user-select: none;
}

.mfp-loading.mfp-figure {
  display: none;
}

.mfp-hide {
  display: none !important;
}

.mfp-preloader {
  color: #ccc;
  position: absolute;
  top: 50%;
  width: auto;
  text-align: center;
  margin-top: -0.8em;
  left: 8px;
  right: 8px;
  z-index: 1044;
}

.mfp-preloader a {
  color: #ccc;
}

.mfp-preloader a:hover {
  color: #fff;
}

.mfp-s-ready .mfp-preloader {
  display: none;
}

.mfp-s-error .mfp-content {
  display: none;
}

button.mfp-close,
button.mfp-arrow {
  overflow: visible;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
  display: block;
  outline: none;
  padding: 0;
  z-index: 1046;
  -webkit-box-shadow: none;
          box-shadow: none;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
}

button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

.mfp-close {
  width: 44px;
  height: 44px;
  line-height: 44px;
  position: absolute;
  right: 0;
  top: 0;
  text-decoration: none;
  text-align: center;
  opacity: 0.65;
  padding: 0 0 18px 10px;
  color: #fff;
  font-style: normal;
  font-size: 28px;
  font-family: Arial, Baskerville, monospace;
}

.mfp-close:hover,
.mfp-close:focus {
  opacity: 1;
}

.mfp-close:active {
  top: 1px;
}

.mfp-close-btn-in .mfp-close {
  color: #333;
}

.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
  color: #fff;
  right: -6px;
  text-align: right;
  padding-right: 6px;
  width: 100%;
}

.mfp-counter {
  position: absolute;
  top: 0;
  right: 0;
  color: #ccc;
  font-size: 12px;
  line-height: 18px;
  white-space: nowrap;
}

.mfp-arrow {
  position: absolute;
  opacity: 0.65;
  margin: 0;
  top: 50%;
  margin-top: -55px;
  padding: 0;
  width: 90px;
  height: 110px;
  -webkit-tap-highlight-color: transparent;
}

.mfp-arrow:hover,
.mfp-arrow:focus {
  opacity: 1;
}

.mfp-arrow:before,
.mfp-arrow:after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  left: 0;
  top: 0;
  margin-top: 35px;
  margin-left: 35px;
  border: medium inset transparent;
}

.mfp-arrow:after {
  border-top-width: 13px;
  border-bottom-width: 13px;
  top: 8px;
}

.mfp-arrow:before {
  border-top-width: 21px;
  border-bottom-width: 21px;
  opacity: 0.7;
}

.mfp-arrow-left {
  left: 0;
}

.mfp-arrow-left:after {
  border-right: 17px solid #fff;
  margin-left: 31px;
}

.mfp-arrow-left:before {
  margin-left: 25px;
  border-right: 27px solid #3f3f3f;
}

.mfp-arrow-right {
  right: 0;
}

.mfp-arrow-right:after {
  border-left: 17px solid #fff;
  margin-left: 39px;
}

.mfp-arrow-right:before {
  border-left: 27px solid #3f3f3f;
}

.mfp-iframe-holder {
  padding-top: 40px;
  padding-bottom: 40px;
}

.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 90vw;
}

.mfp-iframe-holder .mfp-close {
  top: -40px;
}

.mfp-iframe-scaler {
  width: 100%;
  height: 0;
  overflow: hidden;
  padding-top: 56.25%;
}

.mfp-iframe-scaler iframe {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
          box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
  background: #000;
}

/* Main image in popup */

img.mfp-img {
  width: auto;
  max-width: 100%;
  height: auto;
  display: block;
  line-height: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 40px 0 40px;
  margin: 0 auto;
}

/* The shadow behind the image */

.mfp-figure {
  line-height: 0;
}

.mfp-figure:after {
  content: "";
  position: absolute;
  left: 0;
  top: 40px;
  bottom: 40px;
  display: block;
  right: 0;
  width: auto;
  height: auto;
  z-index: -1;
  -webkit-box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
          box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
  background: #444;
}

.mfp-figure small {
  color: #bdbdbd;
  display: block;
  font-size: 12px;
  line-height: 14px;
}

.mfp-figure figure {
  margin: 0;
}

.mfp-bottom-bar {
  margin-top: -36px;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  cursor: auto;
}

.mfp-title {
  text-align: left;
  line-height: 18px;
  color: #f3f3f3;
  word-wrap: break-word;
  padding-right: 36px;
}

.mfp-image-holder .mfp-content {
  max-width: 100%;
}

.mfp-gallery .mfp-image-holder .mfp-figure {
  cursor: pointer;
}

@media screen and (max-width: 800px) and (orientation: landscape), screen and (max-height: 300px) {
  /**
       * Remove all paddings around the image on small screen
       */

  .mfp-img-mobile .mfp-image-holder {
    padding-left: 0;
    padding-right: 0;
  }

  .mfp-img-mobile img.mfp-img {
    padding: 0;
  }

  .mfp-img-mobile .mfp-figure:after {
    top: 0;
    bottom: 0;
  }

  .mfp-img-mobile .mfp-figure small {
    display: inline;
    margin-left: 5px;
  }

  .mfp-img-mobile .mfp-bottom-bar {
    background: rgba(0, 0, 0, 0.6);
    bottom: 0;
    margin: 0;
    top: auto;
    padding: 3px 5px;
    position: fixed;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
  }

  .mfp-img-mobile .mfp-bottom-bar:empty {
    padding: 0;
  }

  .mfp-img-mobile .mfp-counter {
    right: 5px;
    top: 3px;
  }

  .mfp-img-mobile .mfp-close {
    top: 0;
    right: 0;
    width: 35px;
    height: 35px;
    line-height: 35px;
    background: rgba(0, 0, 0, 0.6);
    position: fixed;
    text-align: center;
    padding: 0;
  }
}

@media all and (max-width: 900px) {
  .mfp-arrow-left {
    -webkit-transform-origin: 0;
    -ms-transform-origin: 0;
        transform-origin: 0;
  }

  .mfp-arrow-right {
    -webkit-transform-origin: 100%;
    -ms-transform-origin: 100%;
        transform-origin: 100%;
  }

  .mfp-container {
    padding-left: 6px;
    padding-right: 6px;
  }
}

/**
 * Fade-zoom animation for first dialog
 */

/* start state */

.my-mfp-zoom-in .mfp-content {
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
  -webkit-transform: scale(0.8);
      -ms-transform: scale(0.8);
          transform: scale(0.8);
}

/* animate in */

.my-mfp-zoom-in.mfp-ready .mfp-content {
  opacity: 1;
  -webkit-transform: scale(1);
      -ms-transform: scale(1);
          transform: scale(1);
}

/* animate out */

.my-mfp-zoom-in.mfp-removing .mfp-content {
  -webkit-transform: scale(0.8);
      -ms-transform: scale(0.8);
          transform: scale(0.8);
  opacity: 0;
}

/* Dark overlay, start state */

.my-mfp-zoom-in.mfp-bg {
  opacity: 0;
  -webkit-transition: opacity 0.3s ease-out;
  transition: opacity 0.3s ease-out;
}

/* animate in */

.my-mfp-zoom-in.mfp-ready.mfp-bg {
  opacity: 0.8;
}

/* animate out */

.my-mfp-zoom-in.mfp-removing.mfp-bg {
  opacity: 0;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::-ms-backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: currentColor;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  -webkit-font-feature-settings: normal;
          font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  -webkit-font-feature-settings: normal;
          font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  -webkit-font-feature-settings: inherit;
          font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input:-ms-input-placeholder, textarea:-ms-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::-ms-input-placeholder, textarea::-ms-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

html {
  font-family: "proxima-nova", sans-serif;
  font-weight: 400;
  font-size: 21px;
  line-height: 32px;
  letter-spacing: normal;
  -webkit-font-smoothing: antialiased;
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1340px) {
  .container {
    max-width: 1340px;
  }
}

.button {
  position: relative;
  display: inline-table;
  border-radius: 33px;
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
  padding-top: 13px;
  padding-bottom: 13px;
  padding-left: 28px;
  padding-right: 69px;
  font-size: 15px;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.button::after {
  position: absolute;
  top: 50%;
  right: 40px;
  height: 10px;
  width: 16px;
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/arrow-right-gold.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  --tw-content: '';
  content: var(--tw-content);
}

.button:hover::after {
  content: var(--tw-content);
  --tw-translate-x: 20px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (min-width: 768px) {
  .button {
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 35px;
    padding-right: 75px;
    font-size: 17px;
  }
}

@media (min-width: 1024px) {
  .button {
    padding-top: 21px;
    padding-bottom: 21px;
    font-size: 18px;
    line-height: 20px;
    letter-spacing: -0.29px;
  }
}

.button-blue {
  position: relative;
  display: table;
  border-radius: 33px;
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
  padding-top: 13px;
  padding-bottom: 13px;
  padding-left: 28px;
  padding-right: 69px;
  font-size: 15px;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.button-blue::after {
  position: absolute;
  top: 50%;
  right: 40px;
  height: 10px;
  width: 16px;
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/arrow-right-yellow.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  --tw-content: '';
  content: var(--tw-content);
}

.button-blue:hover::after {
  content: var(--tw-content);
  --tw-translate-x: 20px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (min-width: 768px) {
  .button-blue {
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 35px;
    padding-right: 75px;
    font-size: 18px;
    line-height: 20px;
    letter-spacing: -0.29px;
  }
}

@media (min-width: 1024px) {
  .button-blue {
    padding-top: 21px;
    padding-bottom: 21px;
  }
}

.button-white {
  position: relative;
  display: table;
  border-radius: 33px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity, 1));
  padding-top: 13px;
  padding-bottom: 13px;
  padding-left: 28px;
  padding-right: 69px;
  font-size: 15px;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.button-white::after {
  position: absolute;
  top: 50%;
  right: 40px;
  height: 10px;
  width: 16px;
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/arrow-right-white.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  --tw-content: '';
  content: var(--tw-content);
}

.button-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.button-white:hover::after {
  --tw-translate-x: 20px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/arrow-right-gold.svg');
}

@media (min-width: 768px) {
  .button-white {
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 35px;
    padding-right: 75px;
    font-size: 18px;
    line-height: 20px;
    letter-spacing: -0.29px;
  }
}

@media (min-width: 1024px) {
  .button-white {
    padding-top: 21px;
    padding-bottom: 21px;
  }
}

.buttonpdf {
  position: relative;
  margin-right: 16px;
  margin-bottom: 16px;
  display: inline-table;
  border-radius: 33px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  padding-top: 13px;
  padding-bottom: 13px;
  padding-left: 28px;
  padding-right: 69px;
  font-size: 15px;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

.buttonpdf::after {
  position: absolute;
  top: 50%;
  right: 40px;
  height: 16px;
  width: 16px;
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/pdf-icon-navy.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  --tw-content: '';
  content: var(--tw-content);
}

.buttonpdf:last-child {
  margin-right: 0;
  margin-bottom: 0;
}

.buttonpdf:hover {
  background-color: rgb(*********** / 0.7);
}

@media (min-width: 768px) {
  .buttonpdf {
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 35px;
    padding-right: 75px;
    font-size: 18px;
    line-height: 20px;
    letter-spacing: -0.29px;
  }
}

@media (min-width: 1024px) {
  .buttonpdf {
    padding-top: 21px;
    padding-bottom: 21px;
  }
}

.button.scroll-to:hover::after {
  --tw-translate-x: 0;
  content: var(--tw-content);
  --tw-translate-y: 2px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0;
}

.-right-1\/4 {
  right: -25%;
}

.-right-\[10px\] {
  right: -10px;
}

.-top-\[12px\] {
  top: -12px;
}

.bottom-0 {
  bottom: 0;
}

.bottom-12 {
  bottom: 48px;
}

.bottom-16 {
  bottom: 64px;
}

.bottom-20 {
  bottom: 80px;
}

.bottom-3 {
  bottom: 12px;
}

.bottom-5 {
  bottom: 20px;
}

.bottom-8 {
  bottom: 32px;
}

.bottom-\[10vw\] {
  bottom: 10vw;
}

.bottom-\[20\%\] {
  bottom: 20%;
}

.left-0 {
  left: 0;
}

.left-1\/2 {
  left: 50%;
}

.left-5 {
  left: 20px;
}

.left-6 {
  left: 24px;
}

.left-8 {
  left: 32px;
}

.left-\[43px\] {
  left: 43px;
}

.left-\[58px\] {
  left: 58px;
}

.left-\[calc\(50\%_\+_2px\)\] {
  left: calc(50% + 2px);
}

.right-0 {
  right: 0;
}

.right-1\/4 {
  right: 25%;
}

.right-14 {
  right: 56px;
}

.right-32 {
  right: 128px;
}

.right-6 {
  right: 24px;
}

.right-8 {
  right: 32px;
}

.top-0 {
  top: 0;
}

.top-1\/2 {
  top: 50%;
}

.top-10 {
  top: 40px;
}

.top-12 {
  top: 48px;
}

.top-8 {
  top: 32px;
}

.top-\[27px\] {
  top: 27px;
}

.top-\[46px\] {
  top: 46px;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-50 {
  z-index: 50;
}

.z-\[60\] {
  z-index: 60;
}

.z-\[70\] {
  z-index: 70;
}

.z-\[999\] {
  z-index: 999;
}

.order-1 {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
}

.col-span-12 {
  grid-column: span 12 / span 12;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-7 {
  grid-column: span 7 / span 7;
}

.col-start-1 {
  grid-column-start: 1;
}

.col-start-9 {
  grid-column-start: 9;
}

.float-right {
  float: right;
}

.m-0 {
  margin: 0;
}

.-mx-4 {
  margin-left: -16px;
  margin-right: -16px;
}

.mx-0 {
  margin-left: 0;
  margin-right: 0;
}

.mx-1 {
  margin-left: 4px;
  margin-right: 4px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.mx-6 {
  margin-left: 24px;
  margin-right: 24px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-10 {
  margin-top: 40px;
  margin-bottom: 40px;
}

.my-16 {
  margin-top: 64px;
  margin-bottom: 64px;
}

.my-20 {
  margin-top: 80px;
  margin-bottom: 80px;
}

.my-4 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.my-5 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.-ml-3 {
  margin-left: -12px;
}

.-ml-4 {
  margin-left: -16px;
}

.-mr-4 {
  margin-right: -16px;
}

.-mt-10 {
  margin-top: -40px;
}

.-mt-14 {
  margin-top: -56px;
}

.-mt-24 {
  margin-top: -96px;
}

.-mt-8 {
  margin-top: -32px;
}

.-mt-\[100px\] {
  margin-top: -100px;
}

.-mt-\[30px\] {
  margin-top: -30px;
}

.-mt-\[30vmin\] {
  margin-top: -30vmin;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 4px;
}

.mb-10 {
  margin-bottom: 40px;
}

.mb-12 {
  margin-bottom: 48px;
}

.mb-14 {
  margin-bottom: 56px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-20 {
  margin-bottom: 80px;
}

.mb-3 {
  margin-bottom: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-5 {
  margin-bottom: 20px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mb-7 {
  margin-bottom: 28px;
}

.mb-8 {
  margin-bottom: 32px;
}

.mb-9 {
  margin-bottom: 36px;
}

.mb-\[14px\] {
  margin-bottom: 14px;
}

.mb-\[18px\] {
  margin-bottom: 18px;
}

.mb-\[22px\] {
  margin-bottom: 22px;
}

.mb-\[28px\] {
  margin-bottom: 28px;
}

.mb-\[33px\] {
  margin-bottom: 33px;
}

.mb-\[45px\] {
  margin-bottom: 45px;
}

.mb-\[60px\] {
  margin-bottom: 60px;
}

.mb-\[80px\] {
  margin-bottom: 80px;
}

.mb-\[9px\] {
  margin-bottom: 9px;
}

.ml-10 {
  margin-left: 40px;
}

.ml-2 {
  margin-left: 8px;
}

.ml-5 {
  margin-left: 20px;
}

.ml-\[10\%\] {
  margin-left: 10%;
}

.ml-\[30px\] {
  margin-left: 30px;
}

.mr-10 {
  margin-right: 40px;
}

.mr-3 {
  margin-right: 12px;
}

.mr-4 {
  margin-right: 16px;
}

.mr-6 {
  margin-right: 24px;
}

.mr-8 {
  margin-right: 32px;
}

.mr-px {
  margin-right: 1px;
}

.mt-0 {
  margin-top: 0;
}

.mt-0\.5 {
  margin-top: 2px;
}

.mt-1 {
  margin-top: 4px;
}

.mt-1\.5 {
  margin-top: 6px;
}

.mt-10 {
  margin-top: 40px;
}

.mt-12 {
  margin-top: 48px;
}

.mt-14 {
  margin-top: 56px;
}

.mt-16 {
  margin-top: 64px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-20 {
  margin-top: 80px;
}

.mt-24 {
  margin-top: 96px;
}

.mt-28 {
  margin-top: 112px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-32 {
  margin-top: 128px;
}

.mt-4 {
  margin-top: 16px;
}

.mt-5 {
  margin-top: 20px;
}

.mt-6 {
  margin-top: 24px;
}

.mt-7 {
  margin-top: 28px;
}

.mt-8 {
  margin-top: 32px;
}

.mt-\[100px\] {
  margin-top: 100px;
}

.mt-\[10px\] {
  margin-top: 10px;
}

.mt-\[130px\] {
  margin-top: 130px;
}

.mt-\[22px\] {
  margin-top: 22px;
}

.mt-\[25vh\] {
  margin-top: 25vh;
}

.mt-\[35px\] {
  margin-top: 35px;
}

.mt-\[52px\] {
  margin-top: 52px;
}

.mt-\[72px\] {
  margin-top: 72px;
}

.mt-\[90px\] {
  margin-top: 90px;
}

.mt-\[9px\] {
  margin-top: 9px;
}

.block {
  display: block;
}

.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.inline-flex {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.table {
  display: table;
}

.inline-table {
  display: inline-table;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.aspect-\[1\.78540772532\] {
  aspect-ratio: 1.78540772532;
}

.aspect-\[1\/1\] {
  aspect-ratio: 1/1;
}

.aspect-\[10\/7\] {
  aspect-ratio: 10/7;
}

.aspect-\[16\/9\] {
  aspect-ratio: 16/9;
}

.aspect-\[17\/11\] {
  aspect-ratio: 17/11;
}

.aspect-\[1\] {
  aspect-ratio: 1;
}

.aspect-\[3\/2\] {
  aspect-ratio: 3/2;
}

.aspect-\[5\/3\] {
  aspect-ratio: 5/3;
}

.h-0 {
  height: 0;
}

.h-0\.5 {
  height: 2px;
}

.h-16 {
  height: 64px;
}

.h-8 {
  height: 32px;
}

.h-9 {
  height: 36px;
}

.h-\[116px\] {
  height: 116px;
}

.h-\[18px\] {
  height: 18px;
}

.h-\[24px\] {
  height: 24px;
}

.h-\[300px\] {
  height: 300px;
}

.h-\[49px\] {
  height: 49px;
}

.h-\[50px\] {
  height: 50px;
}

.h-\[56\.25vw\] {
  height: 56.25vw;
}

.h-\[560px\] {
  height: 560px;
}

.h-\[5px\] {
  height: 5px;
}

.h-\[60px\] {
  height: 60px;
}

.h-\[calc\(100vh_-_env\(safe-area-inset-bottom\)\)\] {
  height: calc(100vh - env(safe-area-inset-bottom));
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.min-h-\[100vh\] {
  min-height: 100vh;
}

.min-h-\[160px\] {
  min-height: 160px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-full {
  min-height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.w-1\/2 {
  width: 50%;
}

.w-10\/12 {
  width: 83.333333%;
}

.w-16 {
  width: 64px;
}

.w-2 {
  width: 8px;
}

.w-20 {
  width: 80px;
}

.w-4 {
  width: 16px;
}

.w-5 {
  width: 20px;
}

.w-7\/12 {
  width: 58.333333%;
}

.w-8\/12 {
  width: 66.666667%;
}

.w-9 {
  width: 36px;
}

.w-\[100vw\] {
  width: 100vw;
}

.w-\[109px\] {
  width: 109px;
}

.w-\[149px\] {
  width: 149px;
}

.w-\[18px\] {
  width: 18px;
}

.w-\[200px\] {
  width: 200px;
}

.w-\[24px\] {
  width: 24px;
}

.w-\[256px\] {
  width: 256px;
}

.w-\[28px\] {
  width: 28px;
}

.w-\[30\%\] {
  width: 30%;
}

.w-\[330px\] {
  width: 330px;
}

.w-\[46\%\] {
  width: 46%;
}

.w-\[49px\] {
  width: 49px;
}

.w-\[50px\] {
  width: 50px;
}

.w-\[60px\] {
  width: 60px;
}

.w-\[70\%\] {
  width: 70%;
}

.w-\[83vw\] {
  width: 83vw;
}

.w-\[92vw\] {
  width: 92vw;
}

.w-\[calc\(100\%_\/_2_-_9px\)\] {
  width: calc(100% / 2 - 9px);
}

.w-\[calc\(100vw-60px\)\] {
  width: calc(100vw - 60px);
}

.w-\[max-content\] {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.w-screen {
  width: 100vw;
}

.min-w-\[177\.77vh\] {
  min-width: 177.77vh;
}

.min-w-\[50\%\] {
  min-width: 50%;
}

.max-w-\[600px\] {
  max-width: 600px;
}

.max-w-\[725px\] {
  max-width: 725px;
}

.flex-none {
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1 {
  --tw-translate-y: -4px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\.5 {
  --tw-translate-y: -6px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-\[156px\] {
  --tw-translate-y: -156px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-\[50px\] {
  --tw-translate-y: -50px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1\/4 {
  --tw-translate-x: 25%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1 {
  --tw-translate-y: 4px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1\.5 {
  --tw-translate-y: 6px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-5 {
  --tw-translate-y: 20px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-8 {
  --tw-translate-y: 32px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[10px\] {
  --tw-translate-y: 10px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[34px\] {
  --tw-translate-y: 34px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-3 {
  --tw-rotate: -3deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-45 {
  --tw-rotate: -45deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-3 {
  --tw-rotate: 3deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
  --tw-rotate: 90deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-default {
  cursor: default;
}

.cursor-pointer {
  cursor: pointer;
}

.resize {
  resize: both;
}

.list-disc {
  list-style-type: disc;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-10 {
  grid-template-columns: repeat(10, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-rows-3 {
  grid-template-rows: repeat(3, minmax(0, 1fr));
}

.grid-rows-\[80px_20px_20px_150px_0px\] {
  grid-template-rows: 80px 20px 20px 150px 0px;
}

.flex-col {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.flex-col-reverse {
  -webkit-box-orient: vertical;
  -webkit-box-direction: reverse;
      -ms-flex-direction: column-reverse;
          flex-direction: column-reverse;
}

.flex-wrap {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.content-center {
  -ms-flex-line-pack: center;
      align-content: center;
}

.items-start {
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.items-end {
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

.items-center {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.justify-start {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.justify-end {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.justify-center {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.justify-between {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.gap-6 {
  gap: 24px;
}

.gap-8 {
  gap: 32px;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.break-all {
  word-break: break-all;
}

.rounded-\[10px\] {
  border-radius: 10px;
}

.rounded-\[26px\] {
  border-radius: 26px;
}

.rounded-\[29px\] {
  border-radius: 29px;
}

.rounded-\[33px\] {
  border-radius: 33px;
}

.rounded-\[50\%\] {
  border-radius: 50%;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-tl-\[600px\] {
  border-top-left-radius: 600px;
}

.border {
  border-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-0 {
  border-top-width: 0px;
}

.border-dashed {
  border-style: dashed;
}

.border-\[\#D8D8D8\] {
  --tw-border-opacity: 1;
  border-color: rgb(216 216 216 / var(--tw-border-opacity, 1));
}

.border-\[\#ced3d9\] {
  --tw-border-opacity: 1;
  border-color: rgb(206 211 217 / var(--tw-border-opacity, 1));
}

.border-black\/20 {
  border-color: rgb(0 0 0 / 0.2);
}

.border-blue {
  --tw-border-opacity: 1;
  border-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
}

.border-blue\/20 {
  border-color: rgb(2 30 66 / 0.2);
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity, 1));
}

.border-white\/20 {
  border-color: rgb(*********** / 0.2);
}

.border-white\/\[\.49\] {
  border-color: rgb(*********** / .49);
}

.border-yellow {
  --tw-border-opacity: 1;
  border-color: rgb(255 193 7 / var(--tw-border-opacity, 1));
}

.border-opacity-100 {
  --tw-border-opacity: 1;
}

.border-opacity-30 {
  --tw-border-opacity: 0.3;
}

.bg-\[\#cfd3d9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(207 211 217 / var(--tw-bg-opacity, 1));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
}

.bg-cranleighclay {
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

.bg-darkblue {
  --tw-bg-opacity: 1;
  background-color: rgb(6 17 31 / var(--tw-bg-opacity, 1));
}

.bg-grey {
  --tw-bg-opacity: 1;
  background-color: rgb(208 208 206 / var(--tw-bg-opacity, 1));
}

.bg-heathlandgreen {
  --tw-bg-opacity: 1;
  background-color: rgb(127 156 144 / var(--tw-bg-opacity, 1));
}

.bg-red {
  --tw-bg-opacity: 1;
  background-color: rgb(220 53 69 / var(--tw-bg-opacity, 1));
}

.bg-weyblue {
  --tw-bg-opacity: 1;
  background-color: rgb(125 156 192 / var(--tw-bg-opacity, 1));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

.bg-close-icon-homepage-notification-navy {
  background-image: url('/wp-content/themes/cranleigh/assets/images/close-icon-homepage-notification-navy.svg');
}

.bg-close-icon-white {
  background-image: url('/wp-content/themes/cranleigh/assets/images/close-icon-white.svg');
}

.bg-contain {
  background-size: contain;
}

.bg-cover {
  background-size: cover;
}

.bg-fixed {
  background-attachment: fixed;
}

.bg-center {
  background-position: center;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.object-center {
  -o-object-position: center;
     object-position: center;
}

.p-10 {
  padding: 40px;
}

.p-8 {
  padding: 32px;
}

.\!px-\[28px\] {
  padding-left: 28px !important;
  padding-right: 28px !important;
}

.px-10 {
  padding-left: 40px;
  padding-right: 40px;
}

.px-11 {
  padding-left: 44px;
  padding-right: 44px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.px-5 {
  padding-left: 20px;
  padding-right: 20px;
}

.px-6 {
  padding-left: 24px;
  padding-right: 24px;
}

.px-8 {
  padding-left: 32px;
  padding-right: 32px;
}

.px-9 {
  padding-left: 36px;
  padding-right: 36px;
}

.px-\[22px\] {
  padding-left: 22px;
  padding-right: 22px;
}

.px-\[26px\] {
  padding-left: 26px;
  padding-right: 26px;
}

.px-\[30px\] {
  padding-left: 30px;
  padding-right: 30px;
}

.px-\[35px\] {
  padding-left: 35px;
  padding-right: 35px;
}

.px-\[5px\] {
  padding-left: 5px;
  padding-right: 5px;
}

.py-1\.5 {
  padding-top: 6px;
  padding-bottom: 6px;
}

.py-10 {
  padding-top: 40px;
  padding-bottom: 40px;
}

.py-12 {
  padding-top: 48px;
  padding-bottom: 48px;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-20 {
  padding-top: 80px;
  padding-bottom: 80px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-4 {
  padding-top: 16px;
  padding-bottom: 16px;
}

.py-5 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.py-6 {
  padding-top: 24px;
  padding-bottom: 24px;
}

.py-8 {
  padding-top: 32px;
  padding-bottom: 32px;
}

.py-9 {
  padding-top: 36px;
  padding-bottom: 36px;
}

.py-\[15px\] {
  padding-top: 15px;
  padding-bottom: 15px;
}

.py-\[18px\] {
  padding-top: 18px;
  padding-bottom: 18px;
}

.py-\[21px\] {
  padding-top: 21px;
  padding-bottom: 21px;
}

.py-\[30px\] {
  padding-top: 30px;
  padding-bottom: 30px;
}

.py-\[39px\] {
  padding-top: 39px;
  padding-bottom: 39px;
}

.py-\[47px\] {
  padding-top: 47px;
  padding-bottom: 47px;
}

.py-\[5px\] {
  padding-top: 5px;
  padding-bottom: 5px;
}

.py-\[68px\] {
  padding-top: 68px;
  padding-bottom: 68px;
}

.py-\[70px\] {
  padding-top: 70px;
  padding-bottom: 70px;
}

.py-\[9px\] {
  padding-top: 9px;
  padding-bottom: 9px;
}

.pb-0 {
  padding-bottom: 0;
}

.pb-10 {
  padding-bottom: 40px;
}

.pb-14 {
  padding-bottom: 56px;
}

.pb-16 {
  padding-bottom: 64px;
}

.pb-2\.5 {
  padding-bottom: 10px;
}

.pb-20 {
  padding-bottom: 80px;
}

.pb-3 {
  padding-bottom: 12px;
}

.pb-32 {
  padding-bottom: 128px;
}

.pb-4 {
  padding-bottom: 16px;
}

.pb-40 {
  padding-bottom: 160px;
}

.pb-5 {
  padding-bottom: 20px;
}

.pb-6 {
  padding-bottom: 24px;
}

.pb-7 {
  padding-bottom: 28px;
}

.pb-8 {
  padding-bottom: 32px;
}

.pb-9 {
  padding-bottom: 36px;
}

.pb-\[11px\] {
  padding-bottom: 11px;
}

.pb-\[14px\] {
  padding-bottom: 14px;
}

.pb-\[22px\] {
  padding-bottom: 22px;
}

.pb-\[50px\] {
  padding-bottom: 50px;
}

.pb-\[60px\] {
  padding-bottom: 60px;
}

.pl-12 {
  padding-left: 48px;
}

.pl-2\.5 {
  padding-left: 10px;
}

.pl-20 {
  padding-left: 80px;
}

.pl-4 {
  padding-left: 16px;
}

.pl-5 {
  padding-left: 20px;
}

.pl-6 {
  padding-left: 24px;
}

.pl-8 {
  padding-left: 32px;
}

.pl-\[22px\] {
  padding-left: 22px;
}

.pl-\[30px\] {
  padding-left: 30px;
}

.pl-\[35px\] {
  padding-left: 35px;
}

.pl-\[75px\] {
  padding-left: 75px;
}

.pr-12 {
  padding-right: 48px;
}

.pr-14 {
  padding-right: 56px;
}

.pr-3 {
  padding-right: 12px;
}

.pr-4 {
  padding-right: 16px;
}

.pr-7 {
  padding-right: 28px;
}

.pr-8 {
  padding-right: 32px;
}

.pr-\[10\%\] {
  padding-right: 10%;
}

.pr-\[136px\] {
  padding-right: 136px;
}

.pr-\[35px\] {
  padding-right: 35px;
}

.pr-\[75px\] {
  padding-right: 75px;
}

.pt-0 {
  padding-top: 0;
}

.pt-1 {
  padding-top: 4px;
}

.pt-10 {
  padding-top: 40px;
}

.pt-12 {
  padding-top: 48px;
}

.pt-20 {
  padding-top: 80px;
}

.pt-5 {
  padding-top: 20px;
}

.pt-6 {
  padding-top: 24px;
}

.pt-7 {
  padding-top: 28px;
}

.pt-8 {
  padding-top: 32px;
}

.pt-\[126px\] {
  padding-top: 126px;
}

.pt-\[13px\] {
  padding-top: 13px;
}

.pt-\[170px\] {
  padding-top: 170px;
}

.pt-\[40px\] {
  padding-top: 40px;
}

.pt-\[45px\] {
  padding-top: 45px;
}

.pt-\[62px\] {
  padding-top: 62px;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.-indent-\[99999px\] {
  text-indent: -99999px;
}

.align-bottom {
  vertical-align: bottom;
}

.font-aspectbold {
  font-family: proxima-nova, sans-serif;
}

.font-aspectlight {
  font-family: proxima-nova, sans-serif;
}

.font-aspectregular {
  font-family: anth, sans-serif;
}

.font-body {
  font-family: proxima-nova, sans-serif;
}

.text-\[0px\] {
  font-size: 0px;
}

.text-\[12px\] {
  font-size: 12px;
}

.text-\[13px\] {
  font-size: 13px;
}

.text-\[14px\] {
  font-size: 14px;
}

.text-\[15px\] {
  font-size: 15px;
}

.text-\[16px\] {
  font-size: 16px;
}

.text-\[18px\] {
  font-size: 18px;
}

.text-\[20px\] {
  font-size: 20px;
}

.text-\[22px\] {
  font-size: 22px;
}

.text-\[24px\] {
  font-size: 24px;
}

.text-\[28px\] {
  font-size: 28px;
}

.text-\[30px\] {
  font-size: 30px;
}

.text-\[32px\] {
  font-size: 32px;
}

.text-\[40px\] {
  font-size: 40px;
}

.text-\[42px\] {
  font-size: 42px;
}

.text-\[56px\] {
  font-size: 56px;
}

.text-base {
  font-size: 18px;
  line-height: 28px;
  letter-spacing: normal;
}

.text-copy16 {
  font-size: 16px;
  line-height: 22px;
  letter-spacing: normal;
}

.text-copy19 {
  font-size: 19px;
  line-height: 22px;
  letter-spacing: -0.3px;
}

.text-cta {
  font-size: 18px;
  line-height: 20px;
  letter-spacing: -0.29px;
}

.text-textfooter {
  font-size: 16px;
  line-height: 24px;
  letter-spacing: normal;
}

.text-title {
  font-size: 44px;
  line-height: 48px;
  letter-spacing: -0.75px;
}

.text-title30 {
  font-size: 30px;
  line-height: 36px;
  letter-spacing: -0.48px;
}

.text-title46 {
  font-size: 46px;
  line-height: 52px;
  letter-spacing: -0.77px;
}

.text-titlefooter {
  font-size: 24px;
  line-height: 28px;
  letter-spacing: -0.21px;
}

.text-titleh4 {
  font-size: 36px;
  line-height: 40px;
  letter-spacing: -0.75px;
}

.text-titlelg {
  font-size: 90px;
  line-height: 90px;
  letter-spacing: -1.5px;
}

.text-titlemd {
  font-size: 24px;
  line-height: 31px;
  letter-spacing: -0.25px;
}

.text-titlesidebar {
  font-size: 32px;
  line-height: 38px;
  letter-spacing: -0.5px;
}

.text-titlesm {
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
}

.text-titlestats {
  font-size: 48px;
  line-height: 52px;
  letter-spacing: -0.8px;
}

.text-titlexl {
  font-size: 172px;
  line-height: 172px;
  letter-spacing: -4px;
}

.font-regular {
  font-weight: 400;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.italic {
  font-style: italic;
}

.leading-1 {
  line-height: 20px;
}

.leading-\[14px\] {
  line-height: 14px;
}

.leading-\[16px\] {
  line-height: 16px;
}

.leading-\[19px\] {
  line-height: 19px;
}

.leading-\[22px\] {
  line-height: 22px;
}

.leading-\[24px\] {
  line-height: 24px;
}

.leading-\[25px\] {
  line-height: 25px;
}

.leading-\[28px\] {
  line-height: 28px;
}

.leading-\[30px\] {
  line-height: 30px;
}

.leading-\[31px\] {
  line-height: 31px;
}

.leading-\[32px\] {
  line-height: 32px;
}

.leading-\[33px\] {
  line-height: 33px;
}

.leading-\[34px\] {
  line-height: 34px;
}

.leading-\[36px\] {
  line-height: 36px;
}

.leading-\[48px\] {
  line-height: 48px;
}

.tracking-\[-0\.75px\] {
  letter-spacing: -0.75px;
}

.tracking-\[-0\.77px\] {
  letter-spacing: -0.77px;
}

.tracking-\[-0\.97px\] {
  letter-spacing: -0.97px;
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-blue {
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

.text-blue\/60 {
  color: rgb(2 30 66 / 0.6);
}

.text-cranleighclay {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.text-heathlandgreen {
  --tw-text-opacity: 1;
  color: rgb(127 156 144 / var(--tw-text-opacity, 1));
}

.text-weyblue {
  --tw-text-opacity: 1;
  color: rgb(125 156 192 / var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.text-yellow {
  --tw-text-opacity: 1;
  color: rgb(255 193 7 / var(--tw-text-opacity, 1));
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-60 {
  opacity: 0.6;
}

.filter {
  -webkit-filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
          filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition {
  -webkit-transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, backdrop-filter, -webkit-box-shadow, -webkit-transform, -webkit-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, backdrop-filter, -webkit-box-shadow, -webkit-transform, -webkit-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-box-shadow, -webkit-transform, -webkit-filter;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition-duration: 150ms;
          transition-duration: 150ms;
}

.duration-300 {
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

.duration-500 {
  -webkit-transition-duration: 500ms;
          transition-duration: 500ms;
}

.duration-\[0\.550s\] {
  -webkit-transition-duration: 0.550s;
          transition-duration: 0.550s;
}

.ease-in {
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

:root {
  --swiper-theme-color: #007aff;
}

.post-edit-link {
  position: fixed;
  top: 8px;
  left: 50%;
  z-index: 20;
  margin-top: 28px;
  display: table;
  --tw-translate-x: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 30px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(125 156 192 / var(--tw-bg-opacity, 1));
  padding-left: 48px;
  padding-right: 48px;
  padding-top: 12px;
  padding-bottom: 12px;
  font-size: 11px;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.post-edit-link:hover {
  opacity: 0.9;
}

.pdflist li {
  position: relative;
  list-style-type: none;
  padding-left: 24px;
}

.pdflist li::after {
  position: absolute;
  top: 50%;
  left: 0;
  height: 16px;
  width: 16px;
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-color: transparent;
  background-image: url('/wp-content/themes/cranleigh/assets/images/pdf-icon-navy.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  --tw-content: '';
  content: var(--tw-content);
}

.pdflist li a {
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

[data-element="logo_carousel"] .swiper-wrapper,
.social_carousel .swiper-wrapper {
  -webkit-transition-timing-function: linear !important;
          transition-timing-function: linear !important;
}

[data-element="testimonial_carousel"] .swiper-pagination {
  position: absolute;
  bottom: 6px;
  right: 20px;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 1024px) {
  [data-element="testimonial_carousel"] .swiper-pagination {
    bottom: 20px;
  }
}

[data-element="testimonial_carousel"] .swiper-pagination span {
  margin-left: 4px;
  margin-right: 4px;
  height: 4px;
  width: 13px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
  opacity: 0.6;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

[data-element="testimonial_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
  width: 26px;
  --tw-bg-opacity: 1;
  background-color: rgb(125 156 192 / var(--tw-bg-opacity, 1));
  opacity: 1;
}

.sub-menu li:first-child a {
  margin-bottom: 32px;
  display: table;
  font-family: proxima-nova, sans-serif;
  font-size: 32px;
  letter-spacing: -0.5px;
  text-transform: uppercase;
  line-height: 44px;
  position: relative;
  -webkit-transition-delay: 0s;
          transition-delay: 0s;
}

.sub-menu li:first-child a.sub-menu-open {
  z-index: 20;
  -webkit-transition-delay: 300ms;
          transition-delay: 300ms;
}

.sub-menu li:first-child a.sub-menu-open a::after {
  content: none;
}

#main-menu > .menu-item-has-children > .sub-menu li:first-child a > a {
  position: relative;
  padding-right: 32px;
}

#main-menu > .menu-item-has-children > .sub-menu li:first-child a > a::after {
  position: absolute;
  top: 50%;
  right: 0;
  height: 16px;
  width: 16px;
  --tw-translate-y: -50%;
  --tw-rotate: -90deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/sidebar-menu-arrow-cranleighClay.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  --tw-content: '';
  content: var(--tw-content);
}

.sub-menu-0
	> .menu-item-has-children.menu-item-has-children
	> .sub-menu li:first-child a
	> a {
  position: relative;
  padding-right: 32px;
}

.sub-menu-0
	> .menu-item-has-children.menu-item-has-children
	> .sub-menu li:first-child a
	> a::after {
  position: absolute;
  top: 50%;
  right: 0;
  height: 16px;
  width: 16px;
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/menu-plus-cranleighClay.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  --tw-content: '';
  content: var(--tw-content);
}

.sub-menu-1
	> .menu-item-has-children.menu-item-has-children
	> .sub-menu li:first-child a
	> a {
  position: relative;
  padding-right: 32px;
}

.sub-menu-1
	> .menu-item-has-children.menu-item-has-children
	> .sub-menu li:first-child a
	> a::after {
  --tw-content: none;
  content: var(--tw-content);
}

.sub-menu-0
	> .menu-item-has-children.menu-item-has-children
	> .sub-menu li:first-child a
	> a.actv::after,
.sub-menu-1
	> .menu-item-has-children.menu-item-has-children
	> .sub-menu li:first-child a
	> a.actv::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/menu-minus-cranleighClay.svg');
}

.sub-menu-1 li:first-child {
  display: none !important;
}

@media only screen and (max-width: 1023px) {
  [data-element="stats_carousel"] {
    margin-left: -16px !important;
    margin-right: -16px !important;
  }

  .sub-menu-open {
    z-index: -1 !important;
  }

  .sub-menu-open > a {
    opacity: 0 !important;
  }

  .tablepress {
    margin-bottom: 48px !important;
  }

  .gform_wrapper.gravity-theme .gfield textarea.large {
    height: 180px !important;
  }

  [data-element="vacancy_wrap"] {
    height: auto !important;
  }

  .gform_wrapper .gform_validation_errors > h2.hide_summary {
    padding-left: 48px;
    text-align: left;
  }
}

[data-element="stats_carousel"].disabled .swiper-pagination {
  display: none !important;
}

[data-element="stats_carousel"].disabled .swiper-slide {
  padding-bottom: 32px;
}

[data-element="stats_carousel"] .swiper-pagination {
  position: absolute;
  bottom: 32px;
  left: 40px;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media (min-width: 1024px) {
  [data-element="stats_carousel"] .swiper-pagination {
    bottom: 20px;
    left: auto;
    right: 20px;
  }
}

[data-element="stats_carousel"] .swiper-pagination span {
  margin-left: 4px;
  margin-right: 4px;
  height: 4px;
  width: 13px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 0.6;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

[data-element="stats_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
  width: 26px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 1;
}

[data-element="video_carousel"].disabled .swiper-pagination {
  display: none !important;
}

[data-element="video_carousel"] .swiper-pagination {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

[data-element="video_carousel"] .swiper-pagination span {
  margin-left: 4px;
  margin-right: 4px;
  height: 4px;
  width: 13px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 0.6;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

[data-element="video_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
  width: 26px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 1;
}

[data-element="quote_carousel"].disabled .swiper-pagination {
  display: none !important;
}

[data-element="quote_carousel"] .swiper-pagination {
  position: absolute;
  top: 40px;
  right: 40px;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

[data-element="quote_carousel"] .swiper-pagination span {
  margin-left: 4px;
  margin-right: 4px;
  height: 4px;
  width: 13px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 0.6;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

[data-element="quote_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
  width: 26px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 1;
}

[data-element="staff_carousel"].disabled .swiper-pagination {
  display: none !important;
}

[data-element="staff_carousel"] .swiper-pagination {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

[data-element="staff_carousel"] .swiper-pagination span {
  margin-left: 4px;
  margin-right: 4px;
  height: 4px;
  width: 13px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 0.6;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

[data-element="staff_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
  width: 26px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 1;
}

[data-element="stats_home"].swiper {
  padding-bottom: 20px;
}

[data-element="stats_home"] .swiper-pagination {
  position: absolute;
  bottom: 0px;
  left: 50%;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  --tw-translate-x: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

[data-element="stats_home"] .swiper-pagination span {
  margin-left: 4px;
  margin-right: 4px;
  height: 4px;
  width: 13px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 1;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

[data-element="stats_home"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
  width: 26px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 1;
}

[data-element="image_carousel"].disabled .swiper-pagination {
  display: none !important;
}

[data-element="image_carousel"] .swiper-pagination {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

[data-element="image_carousel"] .swiper-pagination span {
  margin-left: 4px;
  margin-right: 4px;
  height: 4px;
  width: 13px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 0.6;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

[data-element="image_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
  width: 26px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 1;
}

[data-element="instagram_carousel"] .swiper-pagination {
  position: absolute;
  bottom: 28px;
  right: 20px;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

[data-element="instagram_carousel"] .swiper-pagination span {
  margin-left: 4px;
  margin-right: 4px;
  height: 4px;
  width: 13px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 0.6;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

[data-element="instagram_carousel"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
  width: 26px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  opacity: 1;
}

[data-element="video_slider"].disabled .swiper-pagination {
  display: none !important;
}

[data-element="video_slider"] .swiper-pagination {
  position: absolute;
  bottom: 20px;
  left: 50%;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  --tw-translate-x: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

[data-element="video_slider"] .swiper-pagination span {
  margin-left: 4px;
  margin-right: 4px;
  height: 4px;
  width: 13px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(12 34 63 / var(--tw-bg-opacity, 1));
  opacity: 0.6;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

[data-element="video_slider"]
	.swiper-pagination
	span.swiper-pagination-bullet-active {
  width: 26px;
  --tw-bg-opacity: 1;
  background-color: rgb(125 156 192 / var(--tw-bg-opacity, 1));
  opacity: 1;
}

[data-state="open"] h3::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/accordion-minus-weyBlue.svg');
}

@media (min-width: 1024px) {
  [data-state="open"] .wysiwyg {
    padding-bottom: 8px;
  }
}

.currentmenu {
  top: 0;
  left: 0;
  margin: 0;
  height: 20px;
  width: 5px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  padding: 0;
  opacity: 0;
  position: absolute !important;
}

[data-element="sidebar_menu"] .page_item_has_children > a {
  position: relative;
  padding-right: 20px;
}

[data-element="sidebar_menu"] .page_item_has_children > a::after {
  position: absolute;
  top: 8px;
  right: 0;
  height: 6px;
  width: 10px;
  background-image: url('/wp-content/themes/cranleigh/assets/images/sidebar-menu-arrow-cranleighClay.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  --tw-content: '';
  content: var(--tw-content);
}

[data-element="sidebar_menu"] .page_item_has_children > a.actv::after {
  content: var(--tw-content);
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

[data-element="sidebar_menu"] .children {
  overflow: hidden;
  padding-left: 16px;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: normal;
}

.error404 section.bg-grey[data-element="sidebar_menu"] .children.py-10.mt-36 {
  display: none;
}

[data-element="sidebar_menu"] .children > * {
  margin-bottom: 12px;
}

[data-element="sidebar_menu"] .children li:first-child {
  margin-top: 12px;
}

[data-element="sidebar_menu"] .children li:last-child {
  margin-bottom: 0;
}

[data-element="sidebar_menu"]
	.page_item_has_children
	.page_item_has_children
	> a::after,
[data-element="sidebar_menu"]
	.page_item_has_children
	.page_item_has_children
	> a.actv::after {
  --tw-content: none;
  content: var(--tw-content);
}

[data-element="quick_links"].actv::after {
  content: var(--tw-content);
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

[data-element="where_carousel"] .swiper-button-disabled {
  cursor: default;
  opacity: 0.6;
}

.link-container {
  position: relative;
  -webkit-transition-delay: 0s;
          transition-delay: 0s;
}

.link-container.sub-menu-open {
  z-index: 20;
  -webkit-transition-delay: 300ms;
          transition-delay: 300ms;
}

.link-container.sub-menu-open a::after {
  content: none !important;
}

.sub-menu {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 10;
  height: 100vh;
  width: 100vw;
  max-width: 600px;
  overflow: auto;
  border-left-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(6 17 31 / var(--tw-bg-opacity, 1));
  padding-left: 40px;
  padding-right: 40px;
  padding-top: 70px;
  padding-bottom: 70px;
}

@media (min-width: 1024px) {
  .sub-menu {
    width: 50vw;
    padding-left: 60px;
    padding-right: 60px;
  }
}

.sub-menu li {
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .sub-menu li {
    margin-bottom: 8px;
  }
}

.sub-menu li a {
  font-size: 20px;
  line-height: 24px;
}

@media (min-width: 1024px) {
  .sub-menu li a {
    font-size: 24px;
  }
}

.sub-menu-1,
.sub-menu-2 {
  position: relative;
  right: auto;
  height: auto;
  width: 100%;
  border-style: none;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
}

.sub-menu-1 {
  margin-top: 8px;
}

.sub-menu-1 .menu-back,
.sub-menu-2 .menu-back {
  display: none;
}

.sub-menu-1 li {
  margin-bottom: 4px;
}

.sub-menu-1 li a {
  padding-left: 20px;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: normal;
}

.sub-menu-2 a {
  padding-left: 40px;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: normal;
}

#main-menu > .menu-item-has-children > .link-container > a {
  position: relative;
  padding-right: 32px;
}

#main-menu > .menu-item-has-children > .link-container > a::after {
  position: absolute;
  top: 50%;
  right: 0;
  height: 16px;
  width: 16px;
  --tw-translate-y: -50%;
  --tw-rotate: -90deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/sidebar-menu-arrow-cranleighClay.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  --tw-content: '';
  content: var(--tw-content);
}

#main-menu .current_page_item {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.sub-menu-0
	> .menu-item-has-children.menu-item-has-children
	> .link-container
	> a {
  position: relative;
  padding-right: 32px;
}

.sub-menu-0
	> .menu-item-has-children.menu-item-has-children
	> .link-container
	> a::after {
  position: absolute;
  top: 50%;
  right: 0;
  height: 16px;
  width: 16px;
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/menu-plus-cranleighClay.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  --tw-content: '';
  content: var(--tw-content);
}

.sub-menu-1
	> .menu-item-has-children.menu-item-has-children
	> .link-container
	> a {
  position: relative;
  padding-right: 32px;
}

.sub-menu-1
	> .menu-item-has-children.menu-item-has-children
	> .link-container
	> a::after {
  --tw-content: none;
  content: var(--tw-content);
}

.sub-menu-0
	> .menu-item-has-children.menu-item-has-children
	> .link-container
	> a.actv::after,
.sub-menu-1
	> .menu-item-has-children.menu-item-has-children
	> .link-container
	> a.actv::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/menu-minus-cranleighClay.svg');
}

button.open span {
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

button.open [data-js="show-scroll"] {
  --tw-bg-opacity: 0;
}

#page-wrap::after {
  visibility: hidden;
  position: fixed;
  inset: 0;
  z-index: 20;
  height: 100%;
  width: 100%;
  background-color: rgb(0 0 0 / 0.6);
  opacity: 0;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  --tw-content: '';
  content: var(--tw-content);
}

#page-wrap.open::after {
  visibility: visible;
  content: var(--tw-content);
  opacity: 1;
}

.nochild div a::after {
  content: none !important;
}

/* Join the team tpl */

.page-template-tpl .banner .button::after {
  content: var(--tw-content);
  --tw-rotate: 90deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

#search-filter-form-66371,
#search-filter-form-66427,
#search-filter-form-46287,
#search-filter-form-67716,
#search-filter-form-76059,
#search-filter-form-1856,
#search-filter-form-68473,
#search-filter-form-68701 {
  width: 100%;
}

#search-filter-form-66371 ul,
#search-filter-form-66371 ul li.sf-field-taxonomy-vacancy_type,
#search-filter-form-66427 ul,
#search-filter-form-66427 ul li.sf-field-category,
#search-filter-form-46287 ul,
#search-filter-form-46287 ul li.sf-field-category,
#search-filter-form-67716 ul,
#search-filter-form-67716 ul li.sf-field-taxonomy-staff_categories,
#search-filter-form-76059 ul,
#search-filter-form-68473 ul,
#search-filter-form-68701 ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

@media (min-width: 768px) {
  #search-filter-form-66371 ul,
#search-filter-form-66371 ul li.sf-field-taxonomy-vacancy_type,
#search-filter-form-66427 ul,
#search-filter-form-66427 ul li.sf-field-category,
#search-filter-form-46287 ul,
#search-filter-form-46287 ul li.sf-field-category,
#search-filter-form-67716 ul,
#search-filter-form-67716 ul li.sf-field-taxonomy-staff_categories,
#search-filter-form-76059 ul,
#search-filter-form-68473 ul,
#search-filter-form-68701 ul {
    width: auto;
  }
}

@media (min-width: 1024px) {
  #search-filter-form-66371 ul,
#search-filter-form-66371 ul li.sf-field-taxonomy-vacancy_type,
#search-filter-form-66427 ul,
#search-filter-form-66427 ul li.sf-field-category,
#search-filter-form-46287 ul,
#search-filter-form-46287 ul li.sf-field-category,
#search-filter-form-67716 ul,
#search-filter-form-67716 ul li.sf-field-taxonomy-staff_categories,
#search-filter-form-76059 ul,
#search-filter-form-68473 ul,
#search-filter-form-68701 ul {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
  }
}

#search-filter-form-67716 ul, #search-filter-form-76059 ul {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.sf-field-taxonomy-vacancy_type h4,
.sf-field-category h4 {
  margin-right: 16px;
  padding: 0;
  font-family: proxima-nova, sans-serif;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: normal;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

#search-filter-form-66371 > ul,
#search-filter-form-67716 > ul,
#search-filter-form-76059 > ul,
#search-filter-form-1856 > ul {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#search-filter-form-66427 > ul,
#search-filter-form-46287 > ul {
  display: inline-block;
  width: 100%;
}

#search-filter-form-66427 > ul > li,
#search-filter-form-46287 > ul > li {
  float: left;
}

#search-filter-form-66427 > ul > li:last-child,
#search-filter-form-46287 > ul > li:last-child {
  margin-top: 8px;
}

@media (min-width: 768px) {
  #search-filter-form-66427 > ul > li:last-child,
#search-filter-form-46287 > ul > li:last-child {
    float: right;
  }
}

@media (min-width: 1024px) {
  #search-filter-form-66427 > ul > li:last-child,
#search-filter-form-46287 > ul > li:last-child {
    margin-top: 4px;
  }
}

#search-filter-form-66427 > ul > li:last-child,
#search-filter-form-46287 > ul > li:last-child {
  width: 100%;
}

#search-filter-form-66427 > ul > li:last-child > *,
#search-filter-form-46287 > ul > li:last-child > * {
  width: 100%;
}

#search-filter-form-66427 > ul > li:last-child > * > *,
#search-filter-form-46287 > ul > li:last-child > * > * {
  width: 100%;
}

@media (min-width: 768px) {
  #search-filter-form-66427 > ul > li:last-child,
#search-filter-form-46287 > ul > li:last-child {
    width: auto;
  }

  #search-filter-form-66427 > ul > li:last-child > *,
#search-filter-form-46287 > ul > li:last-child > * {
    width: auto;
  }

  #search-filter-form-66427 > ul > li:last-child > * > *,
#search-filter-form-46287 > ul > li:last-child > * > * {
    width: auto;
  }
}

#search-filter-form-66427 select,
#search-filter-form-46287 select {
  margin-top: 5px;
  width: 100%;
  padding-top: 11px;
  padding-bottom: 11px;
  padding-left: 18px;
  padding-right: 18px;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
}

@media (min-width: 768px) {
  #search-filter-form-66427 select,
#search-filter-form-46287 select {
    width: 170px;
  }
}

@media (min-width: 1024px) {
  #search-filter-form-66427 select,
#search-filter-form-46287 select {
    margin-left: 32px;
  }
}

.searchandfilter label,
#search-filter-form-66371 input.sf-input-text,
#search-filter-form-66371 .sf-field-search {
  width: 100%;
}

@media (min-width: 1024px) {
  .searchandfilter label,
#search-filter-form-66371 input.sf-input-text,
#search-filter-form-66371 .sf-field-search {
    width: auto;
  }
}

#search-filter-form-66371 input,
#search-filter-form-66427 input,
#search-filter-form-46287 input,
#search-filter-form-67716 input,
#search-filter-form-76059 input,
#search-filter-form-1856 input,
#search-filter-form-68473 input,
#search-filter-form-68701 input {
  -webkit-appearance: none;
}

#search-filter-form-66371 .sf-label-radio,
#search-filter-form-66427 .sf-label-radio,
#search-filter-form-46287 .sf-label-radio,
#search-filter-form-67716 .sf-label-radio,
#search-filter-form-76059 .sf-label-radio,
#search-filter-form-1856 .sf-label-radio,
#search-filter-form-68473 .sf-label-radio,
#search-filter-form-68701 .sf-label-radio {
  cursor: pointer;
  border-radius: 33px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity, 1));
  padding-top: 11px;
  padding-bottom: 11px;
  padding-left: 24px;
  padding-right: 24px;
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

#search-filter-form-66371 li,
#search-filter-form-66427 li,
#search-filter-form-46287 li,
#search-filter-form-67716 li,
#search-filter-form-76059 li,
#search-filter-form-68473 li,
#search-filter-form-68701 li,
#search-filter-form-1856 li {
  margin-right: 16px;
  padding-top: 10px;
  padding-bottom: 10px;
}

#search-filter-form-66371 li:last-child,
#search-filter-form-66427 li:last-child,
#search-filter-form-46287 li:last-child,
#search-filter-form-67716 li:last-child,
#search-filter-form-76059 li:last-child,
#search-filter-form-68473 li:last-child,
#search-filter-form-68701 li:last-child,
#search-filter-form-1856 li:last-child {
  margin: 0;
}

#search-filter-form-66371 li input,
#search-filter-form-66427 li input,
#search-filter-form-46287 li input,
#search-filter-form-67716 li input,
#search-filter-form-76059 li input,
#search-filter-form-68473 li input,
#search-filter-form-68701 li input,
#search-filter-form-1856 li input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 0;
}

#search-filter-form-66371 .sf-option-active .sf-label-radio,
#search-filter-form-66427 .sf-option-active .sf-label-radio,
#search-filter-form-46287 .sf-option-active .sf-label-radio,
#search-filter-form-67716 .sf-option-active .sf-label-radio,
#search-filter-form-76059 .sf-option-active .sf-label-radio,
#search-filter-form-68473 .sf-option-active .sf-label-radio,
#search-filter-form-68701 .sf-option-active .sf-label-radio {
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

#search-filter-form-66371 input.sf-input-text,
#search-filter-form-66427 input.sf-input-text,
#search-filter-form-46287 input.sf-input-text,
#search-filter-form-67716 input.sf-input-text,
#search-filter-form-76059 input.sf-input-text,
#search-filter-form-68473 input.sf-input-text,
#search-filter-form-68701 input.sf-input-text {
  position: relative;
  border-radius: 33px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
  padding-right: 16px;
  padding-top: 12px;
  padding-bottom: 12px;
  padding-left: 40px;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.sf-field-search {
  position: relative;
}

.sf-field-search::after {
  position: absolute;
  top: 50%;
  left: 12px;
  height: 16px;
  width: 16px;
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/icon-search-white.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  --tw-content: '';
  content: var(--tw-content);
}

form#search-filter-form-66426 .sf-field-search {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 16px;
  padding-right: 32px;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

form#search-filter-form-66426 .sf-field-search::after {
  left: auto;
  right: 12px;
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/icon-search-blue.svg');
}

form#search-filter-form-66426 .sf-field-search input {
  padding-top: 8px;
  padding-bottom: 8px;
  outline: 2px solid transparent;
  outline-offset: 2px;
}

/* departments */

#search-filter-form-66416 {
  width: 100%;
}

#search-filter-form-66416 ul,
#search-filter-form-66416 ul li.sf-field-taxonomy-vacancy_type {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

@media (min-width: 1024px) {
  #search-filter-form-66416 ul,
#search-filter-form-66416 ul li.sf-field-taxonomy-vacancy_type {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
  }
}

#search-filter-form-66416 > ul {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#search-filter-form-66416 input {
  -webkit-appearance: none;
}

#search-filter-form-66416 .sf-label-radio {
  border-radius: 33px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity, 1));
  padding-top: 16px;
  padding-bottom: 16px;
  padding-left: 24px;
  padding-right: 24px;
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

#search-filter-form-66416 li {
  margin-right: 16px;
}

#search-filter-form-66416 li:last-child {
  margin: 0;
}

#search-filter-form-66416 .sf-option-active .sf-label-radio {
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

#search-filter-form-66416 input.sf-input-text {
  position: relative;
  border-radius: 33px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
  padding-right: 16px;
  padding-top: 12px;
  padding-bottom: 12px;
  padding-left: 40px;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

/* 404 page */

.error404 footer {
  display: none !important;
}

.error404 section.bg-grey.overflow-hidden.py-10.mt-36 {
  display: none !important;
}

.error404 [data-element="footer_logo_carousel"] {
  display: none;
}

/* remove margin from last point admission */

[data-element="admission_step_n"]:last-child {
  margin-bottom: 0 !important;
}

[data-element="admission_img"][data-count="1"] {
  opacity: 1 !important;
}

[data-element="admission_step_n"][data-count="1"] {
  color: #ffc627 !important;
  background: #0c223f !important;
}

/*logo dark on white pages */

.page-template-tpl-news [data-js="navigation"] g.txt * {
  fill: #021E42;
}

/* pagination */

.pagination__pages {
  margin-left: 8px;
  margin-right: 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.page-numbers {
  margin-left: 6px;
  margin-right: 6px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 40px;
  width: 40px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
  padding-top: 1px;
  font-size: 14px;
  letter-spacing: normal;
  line-height: 18px;
}

@media (min-width: 1024px) {
  .page-numbers {
    margin-left: 8px;
    margin-right: 8px;
    height: 39px;
    width: 39px;
  }
}

.page-numbers.dots {
  border-style: none;
}

.page-numbers.current {
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

.next.page-numbers {
  position: relative;
  margin: 0;
  margin-left: 12px;
  text-indent: -9999px;
}

.next.page-numbers::after {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 40px;
  width: 40px;
  --tw-translate-y: -50%;
  --tw-translate-x: -50%;
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/pagination-previous-navy.svg');
  background-size: contain;
  background-repeat: no-repeat;
  --tw-content: '';
  content: var(--tw-content);
}

@media (min-width: 1024px) {
  .next.page-numbers {
    margin-left: 60px;
  }

  .next.page-numbers::after {
    height: 39px;
    content: var(--tw-content);
    width: 39px;
  }
}

.prev.page-numbers {
  position: relative;
  margin: 0;
  margin-right: 12px;
  text-indent: -9999px;
}

.prev.page-numbers::after {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 40px;
  width: 40px;
  --tw-translate-y: -50%;
  --tw-translate-x: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/pagination-previous-navy.svg');
  background-size: contain;
  background-repeat: no-repeat;
  --tw-content: '';
  content: var(--tw-content);
}

@media (min-width: 1024px) {
  .prev.page-numbers {
    margin-right: 60px;
  }

  .prev.page-numbers::after {
    height: 39px;
    content: var(--tw-content);
    width: 39px;
  }
}

.mfp-figure:after {
  content: none;
}

img.mfp-img {
  width: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  height: 100%;
}

.mfp-bottom-bar {
  margin-top: -80px;
  margin-left: -32px;
}

.mfp-arrow {
  left: 5vw;
}

.mfp-arrow::before {
  --tw-content: none;
  content: var(--tw-content);
}

.mfp-arrow::after {
  top: 0;
  margin: 0;
  height: 48px;
  width: 48px;
  border-width: 0px;
  background-image: url('/wp-content/themes/cranleigh/assets/images/pagination-previous-navy.svg');
  background-size: contain;
  content: var(--tw-content);
  background-repeat: no-repeat;
}

@media (min-width: 1024px) {
  .mfp-arrow::after {
    height: 65px;
    content: var(--tw-content);
    width: 65px;
  }
}

.mfp-arrow-right {
  left: auto;
  right: 5vw;
}

.mfp-arrow-right::after {
  content: var(--tw-content);
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

button.mfp-close,
button.mfp-arrow {
  margin-top: -24px;
  height: 48px;
  width: 48px;
}

@media (min-width: 1024px) {
  button.mfp-close,
button.mfp-arrow {
    margin-top: -32px;
    height: 65px;
    width: 65px;
  }
}

button.mfp-close {
  top: 8px;
  margin: 0;
}

.mfp-counter {
  font-family: proxima-nova, sans-serif;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.mfp-image-holder .mfp-close {
  margin-bottom: 8px;
  display: table;
  height: 24px;
  width: 24px;
  border-width: 0px;
  background-image: url('/wp-content/themes/cranleigh/assets/images/close-icon-white.svg');
  background-size: contain;
  background-repeat: no-repeat;
  text-indent: 9999px;
  opacity: 1;
}

/*Contact page*/

.page-template-tpl-contact div#content-wrap + section {
  padding-top: 0;
}

.page-template-tpl-contact [data-element="footer_logo_carousel"] {
  display: none !important;
}

@media only screen and (min-width: 1024px) {
  .page-template-tpl-contact [data-js="navigation"] {
    position: fixed;
  }

  #map-canvas {
    position: sticky !important;
  }
}

.gform_title {
  display: none;
}

.gfield_consent_label {
  font-size: 14px;
  line-height: 16px;
}

.gfield_consent_label a {
  position: relative;
  font-family: proxima-nova, sans-serif;
}

.gfield_consent_label a::after {
  position: absolute;
  left: 0;
  bottom: -5px;
  height: 1px;
  width: 100%;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  --tw-content: '';
  content: var(--tw-content);
}

.gform_wrapper .gfield_required .gfield_required_text {
  display: none;
}

.page-template-tpl-contact .gform_wrapper .gfield_required {
  position: relative;
}

.page-template-tpl-contact .gform_wrapper .gfield_required::after {
  --tw-content: '*';
  content: var(--tw-content);
}

.gform_wrapper textarea,
.gform_wrapper input {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 12px;
  padding-right: 12px;
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.gform_wrapper textarea {
  padding: 12px 20px !important;
  resize: none;
}

.wysiwyg .gform_wrapper ul li {
  padding-left: 0;
}

.wysiwyg .gform_wrapper ul li::after {
  left: -10px;
  top: 7px;
  height: 6px;
  width: 6px;
}

.gform_wrapper input[type="submit"] {
  position: relative;
  display: inline-table;
  cursor: pointer;
  border-radius: 33px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  padding-top: 13px;
  padding-bottom: 13px;
  padding-left: 35px;
  padding-right: 75px;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: -0.29px;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.gform_wrapper input[type="submit"]::after {
  content: var(--tw-content);
  height: 12px;
}

@media (min-width: 768px) {
  .gform_wrapper input[type="submit"] {
    padding-top: 16px;
    padding-bottom: 16px;
    font-size: 17px;
  }
}

@media (min-width: 1024px) {
  .gform_wrapper input[type="submit"] {
    padding-top: 21px;
    padding-bottom: 21px;
  }
}

.gform_footer.top_label {
  position: relative;
}

.gform_footer.top_label::after {
  position: absolute;
  top: calc(50% - 4px);
  left: 115px;
  height: 16px;
  width: 16px;
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/button-envelope-icon-black.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  --tw-content: '';
  content: var(--tw-content);
}

.gform_footer.top_label:hover::after {
  content: var(--tw-content);
  --tw-translate-x: 20px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.gform_wrapper .gfield_label {
  font-weight: 700 !important;
}

.gform_wrapper .gfield_error label {
  color: #fff !important;
}

.gform_wrapper .gfield_description {
  padding: 2px 0 2px 12px !important;
  font-size: 12px !important;
}

body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	.gfield.gfield--width-half {
  grid-column: span 12 / span 12;
}

@media (min-width: 1024px) {
  body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	.gfield.gfield--width-half {
    grid-column: span 6 / span 6;
  }
}

body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	.gfield.gfield--width-third {
  grid-column: span 12 / span 12;
}

@media (min-width: 1024px) {
  body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	.gfield.gfield--width-third {
    grid-column: span 4 / span 4;
  }
}

body:not(.page-template-tpl-contact) .gform_wrapper.gravity-theme .gfield {
  grid-column: 1/-1;
  min-width: 0;
}

body:not(.page-template-tpl-contact) .gform_wrapper.gravity-theme * {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
}

body:not(.page-template-tpl-contact) .gform_wrapper div.gform_fields {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(12, minmax(0, 1fr));
  gap: 16px;
}

body:not(.page-template-tpl-contact) .gfield_visibility_hidden,
body:not(.page-template-tpl-contact) .hidden_label {
}

.ginput_container_consent {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.ginput_container_consent input {
  width: 20px !important;
  height: 20px !important;
  margin-right: 10px;
}

body:not(.page-template-tpl-contact) .gform_wrapper.gravity-theme textarea,
body:not(.page-template-tpl-contact) .gform_wrapper.gravity-theme input {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
}

body:not(.page-template-tpl-contact) .gform_wrapper input[type="submit"] {
  width: auto;
  border-style: none;
}

body:not(.page-template-tpl-contact) .gfield_label {
  margin-bottom: 8px;
  display: inline-block;
  padding: 0;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: normal;
}

body:not(.page-template-tpl-contact) .gform_footer.top_label {
  margin-top: 20px;
}

body:not(.page-template-tpl-contact) .gform_wrapper .gfield_required,
body:not(.page-template-tpl-contact) .gform_wrapper .gfield_label {
  width: auto !important;
}

body:not(.page-template-tpl-contact) ul.gform_fields textarea,
body:not(.page-template-tpl-contact) ul.gform_fields input {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
  padding: 8px;
}

body:not(.page-template-tpl-contact)
	.gform_legacy_markup_wrapper
	.gf_progressbar_percentage.percentbar_blue {
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
}

body:not(.page-template-tpl-contact)
	.gform_legacy_markup_wrapper
	.gform_page_footer
	.button.gform_next_button,
body:not(.page-template-tpl-contact)
	.gform_legacy_markup_wrapper
	.gform_page_footer
	.button.gform_previous_button {
  position: relative;
  display: inline-table;
  cursor: pointer;
  border-radius: 33px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  padding-top: 21px;
  padding-bottom: 21px;
  padding-left: 35px;
  padding-right: 35px;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: -0.29px;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

body:not(.page-template-tpl-contact) .gform_wrapper.gravity-theme select {
  height: 50px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
}

body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	.gfield-choice-input {
  width: auto;
}

body:not(.page-template-tpl-contact) .gform_wrapper .gfield_error label {
  color: #0c223f !important;
}

#boxcontent .popup-gallery p {
  font-size: 18px;
  line-height: 28px;
  letter-spacing: normal;
}

body:not(.page-template-tpl-contact)
	.gform_wrapper
	.form_saved_message_emailform
	div.gform_fields {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

body:not(.page-template-tpl-contact)
	.gform_wrapper
	.form_saved_message_emailform
	input[type="email"] {
  height: 50px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
}

body:not(.page-template-tpl-contact)
	.gform_wrapper
	.form_saved_message_emailform
	input[type="submit"] {
  height: 50px;
  padding-left: 35px;
  padding-right: 35px;
  padding-top: 0;
  padding-bottom: 0;
}

body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	input[type="button"] {
  height: 50px;
  width: 200px;
  border-style: none;
  padding-left: 35px;
  padding-right: 35px;
  padding-top: 0;
  padding-bottom: 0;
}

.gform_wrapper.gravity-theme .gf_progressbar_title,
body
	.gform_legacy_markup_wrapper
	.gf_progressbar_wrapper
	.gf_progressbar_title {
  font-family: proxima-nova, sans-serif;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
  opacity: 1;
}

.gform_wrapper.gravity-theme .gf_progressbar_blue,
body .gform_legacy_markup_wrapper .gf_progressbar_wrapper .gf_progressbar_blue {
  margin-bottom: 40px;
  height: 6px;
  background-color: rgb(2 30 66 / 0.2);
}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_blue,
body .gform_legacy_markup_wrapper .gf_progressbar_percentage.percentbar_blue {
  position: relative;
  height: 6px;
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_blue span,
body
	.gform_legacy_markup_wrapper
	.gf_progressbar_percentage.percentbar_blue
	span {
  position: absolute;
  bottom: -32px;
  right: -24px;
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

.gform_wrapper.gravity-theme .gf_progressbar_percentage.percentbar_blue span::after,
body
	.gform_legacy_markup_wrapper
	.gf_progressbar_percentage.percentbar_blue
	span::after {
  position: absolute;
  top: -24px;
  right: 0;
  height: 18px;
  width: 18px;
  border-radius: 9999px;
  border-width: 3px;
  --tw-border-opacity: 1;
  border-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

.ui-datepicker-header select,
.ui-datepicker-header .ui-datepicker-next,
.ui-datepicker-header .ui-datepicker-prev {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.ui-datepicker-header select {
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
  max-width: inherit !important;
}

.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	.ui-datepicker-next,
.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	.ui-datepicker-prev {
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
  background-image: url(../images/arrow-right-white.svg);
  margin-right: 10px;
  width: 20px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	.ui-datepicker-prev {
  margin-left: 10px;
  width: 20px;
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	.ui-datepicker-next::before,
.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	.ui-datepicker-prev::before {
  content: none !important;
}

select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}

.gform_wrapper.gravity-theme legend.gfield_label {
  font-family: proxima-nova, sans-serif;
  font-size: 28px;
  text-transform: uppercase;
  line-height: 34px;
  letter-spacing: -0.48px;
}

.gform_wrapper fieldset,
.gform_wrapper.gravity-theme fieldset {
  margin-top: 32px;
}

.gform_wrapper.gravity-theme .ginput_complex label,
body:not(.page-template-tpl-contact) .gform_wrapper .gfield_label {
  font-weight: 700;
}

.gform_wrapper.gravity-theme .ginput_container_address span {
  margin-bottom: 8px;
}

.gform_wrapper.gravity-theme .gform_previous_button.button,
.gform_wrapper.gravity-theme .gform_save_link.button,
body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	input[type="button"],
.gpnf-modal .tingle-btn,
.gform_wrapper .gpnf-add-entry {
  position: relative;
  display: inline-table;
  height: auto;
  width: auto;
  cursor: pointer;
  border-radius: 33px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  padding-top: 13px;
  padding-bottom: 13px;
  padding-left: 28px;
  padding-right: 69px;
  font-size: 15px;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.gform_wrapper.gravity-theme .gform_previous_button.button::after,
.gform_wrapper.gravity-theme .gform_save_link.button::after,
body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	input[type="button"]::after,
.gpnf-modal .tingle-btn::after,
.gform_wrapper .gpnf-add-entry::after {
  --tw-content: none;
  content: var(--tw-content);
}

@media (min-width: 768px) {
  .gform_wrapper.gravity-theme .gform_previous_button.button,
.gform_wrapper.gravity-theme .gform_save_link.button,
body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	input[type="button"],
.gpnf-modal .tingle-btn,
.gform_wrapper .gpnf-add-entry {
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 35px;
    padding-right: 35px;
    font-size: 17px;
  }
}

@media (min-width: 1024px) {
  .gform_wrapper.gravity-theme .gform_previous_button.button,
.gform_wrapper.gravity-theme .gform_save_link.button,
body:not(.page-template-tpl-contact)
	.gform_wrapper.gravity-theme
	input[type="button"],
.gpnf-modal .tingle-btn,
.gform_wrapper .gpnf-add-entry {
    padding-top: 21px;
    padding-bottom: 21px;
    font-size: 18px;
    line-height: 20px;
    letter-spacing: -0.29px;
  }
}

.gform_wrapper.gravity-theme .gform_save_link.button svg {
  display: none;
}

.gform_wrapper .gpnf-nested-entries th {
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
  width: 25% !important;
}

.gform_wrapper .gpnf-nested-entries th * {
  width: auto !important;
}

.gpnf-modal .gpnf-modal-header {
  background-color: #0d2140 !important;
}

.gpnf-modal .tingle-btn {
  background-color: #ffc627 !important;
  border-radius: 33px !important;
}

body:not(.page-template-tpl-contact) .gform_wrapper .gpnf-add-entry {
  width: auto;
}

body:not(.page-template-tpl-contact) .gform_wrapper input[type="submit"],
.gform_legacy_markup_wrapper .gform_footer input.button,
.gform_legacy_markup_wrapper .gform_footer input[type="submit"],
.gform_legacy_markup_wrapper .gform_page_footer input.button,
.gform_legacy_markup_wrapper .gform_page_footer input[type="submit"] {
  position: relative;
}

body:not(.page-template-tpl-contact) .gform_wrapper input[type="submit"]::after,
.gform_legacy_markup_wrapper .gform_footer input.button::after,
.gform_legacy_markup_wrapper .gform_footer input[type="submit"]::after,
.gform_legacy_markup_wrapper .gform_page_footer input.button::after,
.gform_legacy_markup_wrapper .gform_page_footer input[type="submit"]::after {
  position: absolute;
  top: 50%;
  right: 40px;
  height: 10px;
  width: 16px;
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-image: url('/wp-content/themes/cranleigh/assets/images/arrow-right-gold.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  --tw-content: '';
  content: var(--tw-content);
}

body:not(.page-template-tpl-contact) .gform_wrapper input[type="submit"]:hover::after,
.gform_legacy_markup_wrapper .gform_footer input.button:hover::after,
.gform_legacy_markup_wrapper .gform_footer input[type="submit"]:hover::after,
.gform_legacy_markup_wrapper .gform_page_footer input.button:hover::after,
.gform_legacy_markup_wrapper .gform_page_footer input[type="submit"]:hover::after {
  content: var(--tw-content);
  --tw-translate-x: 20px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

body
	.gform_legacy_markup_wrapper
	.field_sublabel_above
	.ginput_complex.ginput_container
	label,
body
	.gform_legacy_markup_wrapper
	.field_sublabel_above
	div[class*="gfield_time_"].ginput_container
	label {
  font-weight: 700;
  font-size: 15px;
  padding-top: 5px;
  margin-bottom: 0;
  margin-top: 0;
}

.gform_legacy_markup_wrapper .description_above .gfield_description {
  line-height: 18px;
}

body:not(.page-template-tpl-contact) .gform_wrapper input[type="submit"] {
  font-size: 18px;
}

body
	.gform_legacy_markup_wrapper
	input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]) {
  font-size: 15px;
  margin-bottom: 0;
  margin-top: 0;
  padding: 8px;
}

body .gform_legacy_markup_wrapper .gfield_header_item,
body .gform_legacy_markup_wrapper .gform_fileupload_rules,
body .gform_legacy_markup_wrapper .ginput_complex label,
body:not(.page-template-tpl-contact) .gform_wrapper .gfield_label {
  margin-bottom: 0;
  margin-top: 0;
  padding-top: 5px;
  font-size: 15px;
  line-height: 20px;
}

body .gform_wrapper.gravity-theme .field_sublabel_above .description,
body .gform_wrapper.gravity-theme .field_sublabel_above .gfield_description,
body .gform_wrapper.gravity-theme .field_sublabel_above .gsection_description {
  margin-top: 0;
  padding: 0 !important;
  line-height: 19px;
}

.required_label > label {
  position: relative;
}

.required_label > label::after {
  --tw-text-opacity: 1;
  color: rgb(125 156 192 / var(--tw-text-opacity, 1));
  --tw-content: '*';
  content: var(--tw-content);
}

body .gform_legacy_markup_wrapper .top_label div.ginput_container {
  margin-top: 0 !important;
}

body
	.gform-theme-datepicker:not(.gform-legacy-datepicker)
	.ui-datepicker-header
	select {
  background-size: 24px 18px;
  background-position: 100% 50%;
}

.ui-datepicker-header,
.ui-datepicker-header .ui-datepicker-header {
  background: #0c223f !important;
}

.tingle-modal--overflow::-webkit-scrollbar {
  z-index: 9999px;
  height: 6px;
  border-radius: 3px;
}

.tingle-modal--overflow::-webkit-scrollbar-track {
  border-radius: 3px;
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

.tingle-modal--overflow::-webkit-scrollbar-thumb {
  border-radius: 3px;
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
}

.tingle-modal--overflow::-webkit-scrollbar-thumb:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(6 17 31 / var(--tw-bg-opacity, 1));
}

.tingle-modal:before {
  -webkit-filter: none !important;
          filter: none !important;
  backdrop-filter: none !important;
}

body .gform_wrapper.gravity-theme .gfield_checkbox label,
body .gform_wrapper.gravity-theme .gfield_radio label {
  line-height: 22px;
}

.gchoice.gchoice_3_111_1 {
  margin-top: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.gchoice.gchoice_3_111_1 input {
  --tw-translate-y: 4px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.gform_previous_button.button::before {
  --tw-content: 'Previous';
  content: var(--tw-content);
}

.gform_wrapper .gform_page_footer input[type="submit"] {
  padding-left: 75px;
}

.gform_legacy_markup_wrapper .gfield_checkbox li label,
.gform_legacy_markup_wrapper .gfield_radio li label {
  font-size: 14px !important;
  margin-top: 6px !important;
  margin-bottom: 4px !important;
}

.gform_legacy_markup_wrapper .description_above .gfield_description {
  border: none !important;
  padding: 0 !important;
  margin-bottom: 14px !important;
}

.wysiwyg .gform_wrapper ul:not(.pdflist) li {
  margin-bottom: 8px;
}

.home #page-wrap {
  overflow: hidden;
}

.error404 section.bg-grey.home #page-wrap.py-10.mt-36 {
  display: none;
}

.gform_wrapper.gravity-theme .gform_save_link.button {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

#signup-content {
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
  padding-top: 200px;
  padding-bottom: 200px;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

li.sf-field-search input {
  height: 40px !important;
}

.post-password-form input {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(208 208 206 / var(--tw-border-opacity, 1));
}

.post-password-form p {
  margin-bottom: 20px;
}

.gform_wrapper .post-password-form input[type="submit"] {
  padding-right: 35px;
}

.gform_button.button {
  margin: 0 !important;
}

@media (min-width: 1024px) {
  .lg\:container {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
  }

  @media (min-width: 640px) {
    .lg\:container {
      max-width: 640px;
    }
  }

  @media (min-width: 768px) {
    .lg\:container {
      max-width: 768px;
    }
  }

  @media (min-width: 1024px) {
    .lg\:container {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {
    .lg\:container {
      max-width: 1280px;
    }
  }

  @media (min-width: 1340px) {
    .lg\:container {
      max-width: 1340px;
    }
  }
}

.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}

.before\:-top-12::before {
  content: var(--tw-content);
  top: -48px;
}

.before\:bottom-0::before {
  content: var(--tw-content);
  bottom: 0;
}

.before\:left-0::before {
  content: var(--tw-content);
  left: 0;
}

.before\:top-0::before {
  content: var(--tw-content);
  top: 0;
}

.before\:z-10::before {
  content: var(--tw-content);
  z-index: 10;
}

.before\:h-\[20vh\]::before {
  content: var(--tw-content);
  height: 20vh;
}

.before\:h-\[25\%\]::before {
  content: var(--tw-content);
  height: 25%;
}

.before\:h-\[30\%\]::before {
  content: var(--tw-content);
  height: 30%;
}

.before\:h-\[33\%\]::before {
  content: var(--tw-content);
  height: 33%;
}

.before\:h-full::before {
  content: var(--tw-content);
  height: 100%;
}

.before\:h-px::before {
  content: var(--tw-content);
  height: 1px;
}

.before\:w-full::before {
  content: var(--tw-content);
  width: 100%;
}

.before\:bg-blue\/50::before {
  content: var(--tw-content);
  background-color: rgb(2 30 66 / 0.5);
}

.before\:bg-gradient-to-b::before {
  content: var(--tw-content);
  background-image: -webkit-gradient(linear, left top, left bottom, from(var(--tw-gradient-stops)));
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.before\:bg-gradient-to-t::before {
  content: var(--tw-content);
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--tw-gradient-stops)));
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.before\:from-black\/0::before {
  content: var(--tw-content);
  --tw-gradient-from: rgb(0 0 0 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.before\:from-white\/0::before {
  content: var(--tw-content);
  --tw-gradient-from: rgb(*********** / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(*********** / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.before\:to-black\/50::before {
  content: var(--tw-content);
  --tw-gradient-to: rgb(0 0 0 / 0.5) var(--tw-gradient-to-position);
}

.before\:to-black\/60::before {
  content: var(--tw-content);
  --tw-gradient-to: rgb(0 0 0 / 0.6) var(--tw-gradient-to-position);
}

.before\:to-white\/50::before {
  content: var(--tw-content);
  --tw-gradient-to: rgb(*********** / 0.5) var(--tw-gradient-to-position);
}

.before\:content-\[\'\'\]::before {
  --tw-content: '';
  content: var(--tw-content);
}

.before\:content-none::before {
  --tw-content: none;
  content: var(--tw-content);
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:-left-\[17px\]::after {
  content: var(--tw-content);
  left: -17px;
}

.after\:-left-\[50vw\]::after {
  content: var(--tw-content);
  left: -50vw;
}

.after\:-left-\[72px\]::after {
  content: var(--tw-content);
  left: -72px;
}

.after\:-right-\[50px\]::after {
  content: var(--tw-content);
  right: -50px;
}

.after\:bottom-0::after {
  content: var(--tw-content);
  bottom: 0;
}

.after\:bottom-1\/2::after {
  content: var(--tw-content);
  bottom: 50%;
}

.after\:left-0::after {
  content: var(--tw-content);
  left: 0;
}

.after\:left-1\/2::after {
  content: var(--tw-content);
  left: 50%;
}

.after\:left-10::after {
  content: var(--tw-content);
  left: 40px;
}

.after\:left-3::after {
  content: var(--tw-content);
  left: 12px;
}

.after\:left-4::after {
  content: var(--tw-content);
  left: 16px;
}

.after\:left-5::after {
  content: var(--tw-content);
  left: 20px;
}

.after\:left-8::after {
  content: var(--tw-content);
  left: 32px;
}

.after\:left-px::after {
  content: var(--tw-content);
  left: 1px;
}

.after\:right-0::after {
  content: var(--tw-content);
  right: 0;
}

.after\:right-1::after {
  content: var(--tw-content);
  right: 4px;
}

.after\:right-10::after {
  content: var(--tw-content);
  right: 40px;
}

.after\:right-6::after {
  content: var(--tw-content);
  right: 24px;
}

.after\:right-\[30px\]::after {
  content: var(--tw-content);
  right: 30px;
}

.after\:right-auto::after {
  content: var(--tw-content);
  right: auto;
}

.after\:top-0::after {
  content: var(--tw-content);
  top: 0;
}

.after\:top-1\/2::after {
  content: var(--tw-content);
  top: 50%;
}

.after\:top-10::after {
  content: var(--tw-content);
  top: 40px;
}

.after\:top-5::after {
  content: var(--tw-content);
  top: 20px;
}

.after\:top-8::after {
  content: var(--tw-content);
  top: 32px;
}

.after\:top-\[calc\(50\%_-_12px\)\]::after {
  content: var(--tw-content);
  top: calc(50% - 12px);
}

.after\:top-\[calc\(50\%_-_2px\)\]::after {
  content: var(--tw-content);
  top: calc(50% - 2px);
}

.after\:z-20::after {
  content: var(--tw-content);
  z-index: 20;
}

.after\:ml-4::after {
  content: var(--tw-content);
  margin-left: 16px;
}

.after\:inline-flex::after {
  content: var(--tw-content);
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.after\:h-0::after {
  content: var(--tw-content);
  height: 0;
}

.after\:h-1::after {
  content: var(--tw-content);
  height: 4px;
}

.after\:h-1\/3::after {
  content: var(--tw-content);
  height: 33.333333%;
}

.after\:h-2\.5::after {
  content: var(--tw-content);
  height: 10px;
}

.after\:h-3\/4::after {
  content: var(--tw-content);
  height: 75%;
}

.after\:h-4::after {
  content: var(--tw-content);
  height: 16px;
}

.after\:h-40::after {
  content: var(--tw-content);
  height: 160px;
}

.after\:h-6::after {
  content: var(--tw-content);
  height: 24px;
}

.after\:h-7::after {
  content: var(--tw-content);
  height: 28px;
}

.after\:h-\[14px\]::after {
  content: var(--tw-content);
  height: 14px;
}

.after\:h-\[15px\]::after {
  content: var(--tw-content);
  height: 15px;
}

.after\:h-\[17px\]::after {
  content: var(--tw-content);
  height: 17px;
}

.after\:h-\[25\%\]::after {
  content: var(--tw-content);
  height: 25%;
}

.after\:h-\[26px\]::after {
  content: var(--tw-content);
  height: 26px;
}

.after\:h-\[30px\]::after {
  content: var(--tw-content);
  height: 30px;
}

.after\:h-\[40\%\]::after {
  content: var(--tw-content);
  height: 40%;
}

.after\:h-\[47px\]::after {
  content: var(--tw-content);
  height: 47px;
}

.after\:h-\[50vh\]::after {
  content: var(--tw-content);
  height: 50vh;
}

.after\:h-\[51px\]::after {
  content: var(--tw-content);
  height: 51px;
}

.after\:h-\[54px\]::after {
  content: var(--tw-content);
  height: 54px;
}

.after\:h-\[56px\]::after {
  content: var(--tw-content);
  height: 56px;
}

.after\:h-\[60\%\]::after {
  content: var(--tw-content);
  height: 60%;
}

.after\:h-\[62px\]::after {
  content: var(--tw-content);
  height: 62px;
}

.after\:h-\[63px\]::after {
  content: var(--tw-content);
  height: 63px;
}

.after\:h-\[68px\]::after {
  content: var(--tw-content);
  height: 68px;
}

.after\:h-\[6px\]::after {
  content: var(--tw-content);
  height: 6px;
}

.after\:h-full::after {
  content: var(--tw-content);
  height: 100%;
}

.after\:w-0::after {
  content: var(--tw-content);
  width: 0;
}

.after\:w-1::after {
  content: var(--tw-content);
  width: 4px;
}

.after\:w-2\.5::after {
  content: var(--tw-content);
  width: 10px;
}

.after\:w-4::after {
  content: var(--tw-content);
  width: 16px;
}

.after\:w-7::after {
  content: var(--tw-content);
  width: 28px;
}

.after\:w-\[11px\]::after {
  content: var(--tw-content);
  width: 11px;
}

.after\:w-\[14px\]::after {
  content: var(--tw-content);
  width: 14px;
}

.after\:w-\[150vw\]::after {
  content: var(--tw-content);
  width: 150vw;
}

.after\:w-\[17px\]::after {
  content: var(--tw-content);
  width: 17px;
}

.after\:w-\[26px\]::after {
  content: var(--tw-content);
  width: 26px;
}

.after\:w-\[30px\]::after {
  content: var(--tw-content);
  width: 30px;
}

.after\:w-\[50px\]::after {
  content: var(--tw-content);
  width: 50px;
}

.after\:w-\[54px\]::after {
  content: var(--tw-content);
  width: 54px;
}

.after\:w-\[56px\]::after {
  content: var(--tw-content);
  width: 56px;
}

.after\:w-\[5px\]::after {
  content: var(--tw-content);
  width: 5px;
}

.after\:w-\[62px\]::after {
  content: var(--tw-content);
  width: 62px;
}

.after\:w-\[67px\]::after {
  content: var(--tw-content);
  width: 67px;
}

.after\:w-\[68px\]::after {
  content: var(--tw-content);
  width: 68px;
}

.after\:w-full::after {
  content: var(--tw-content);
  width: 100%;
}

.after\:-translate-x-1\/2::after {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:-translate-y-1\/2::after {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:-translate-y-\[58\%\]::after {
  content: var(--tw-content);
  --tw-translate-y: -58%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:-translate-y-\[calc\(50\%_\+_1px\)\]::after {
  content: var(--tw-content);
  --tw-translate-y: calc(calc(50% + 1px) * -1);
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:translate-y-1\/2::after {
  content: var(--tw-content);
  --tw-translate-y: 50%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:rotate-180::after {
  content: var(--tw-content);
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:rotate-90::after {
  content: var(--tw-content);
  --tw-rotate: 90deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:cursor-pointer::after {
  content: var(--tw-content);
  cursor: pointer;
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:border-2::after {
  content: var(--tw-content);
  border-width: 2px;
}

.after\:border-b-\[15px\]::after {
  content: var(--tw-content);
  border-bottom-width: 15px;
}

.after\:border-r-\[17px\]::after {
  content: var(--tw-content);
  border-right-width: 17px;
}

.after\:border-t-\[15px\]::after {
  content: var(--tw-content);
  border-top-width: 15px;
}

.after\:border-blue::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
}

.after\:border-b-\[transparent\]::after {
  content: var(--tw-content);
  border-bottom-color: transparent;
}

.after\:border-r-\[\#cfd3d9\]::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-right-color: rgb(207 211 217 / var(--tw-border-opacity, 1));
}

.after\:border-r-blue::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-right-color: rgb(2 30 66 / var(--tw-border-opacity, 1));
}

.after\:border-t-\[transparent\]::after {
  content: var(--tw-content);
  border-top-color: transparent;
}

.after\:bg-blue::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
}

.after\:bg-cranleighclay::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

.after\:bg-accordion-plus-weyblue::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/accordion-plus-weyBlue.svg');
}

.after\:bg-arrow-down-white::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/arrow-down-white.svg');
}

.after\:bg-arrow-right-gold::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/arrow-right-gold.svg');
}

.after\:bg-arrow-right-yellow::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/arrow-right-yellow.svg');
}

.after\:bg-bio-icon-navy::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/bio-icon-navy.svg');
}

.after\:bg-button-pin-icon-black::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/button-pin-icon-black.svg');
}

.after\:bg-calendar-icon-black::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/calendar-icon-black.svg');
}

.after\:bg-calendar-icon-white::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/calendar-icon-white.svg');
}

.after\:bg-clock-icon-black::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/clock-icon-black.svg');
}

.after\:bg-clock-icon-white::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/clock-icon-white.svg');
}

.after\:bg-close-icon-navy::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/close-icon-navy.svg');
}

.after\:bg-email-icon-white::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/email-icon-white.svg');
}

.after\:bg-envelope-yellow-navy::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/envelope-yellow-navy.svg');
}

.after\:bg-exclamation-mark-icon-navy::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/exclamation-mark-icon-navy.svg');
}

.after\:bg-gradient-to-b::after {
  content: var(--tw-content);
  background-image: -webkit-gradient(linear, left top, left bottom, from(var(--tw-gradient-stops)));
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.after\:bg-pdf-icon-navy::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/pdf-icon-navy.svg');
}

.after\:bg-phone-icon-white::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/phone-icon-white.svg');
}

.after\:bg-phone-yellow::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/phone-yellow.svg');
}

.after\:bg-play-icon-red::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/play-icon-red.svg');
}

.after\:bg-quicklinks-arrow-navy::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/quicklinks-arrow-navy.svg');
}

.after\:bg-quote-mark-red::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/quote-mark-red.svg');
}

.after\:bg-quote-mark-white::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/quote-mark-white.svg');
}

.after\:bg-suitcase-icon-navy::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/suitcase-icon-navy.svg');
}

.after\:bg-suitcase-icon-white::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/suitcase-icon-white.svg');
}

.after\:from-black\/0::after {
  content: var(--tw-content);
  --tw-gradient-from: rgb(0 0 0 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.after\:from-white\/0::after {
  content: var(--tw-content);
  --tw-gradient-from: rgb(*********** / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(*********** / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.after\:to-black\/100::after {
  content: var(--tw-content);
  --tw-gradient-to: rgb(0 0 0 / 1) var(--tw-gradient-to-position);
}

.after\:to-black\/50::after {
  content: var(--tw-content);
  --tw-gradient-to: rgb(0 0 0 / 0.5) var(--tw-gradient-to-position);
}

.after\:to-black\/70::after {
  content: var(--tw-content);
  --tw-gradient-to: rgb(0 0 0 / 0.7) var(--tw-gradient-to-position);
}

.after\:to-white::after {
  content: var(--tw-content);
  --tw-gradient-to: #FFF var(--tw-gradient-to-position);
}

.after\:bg-contain::after {
  content: var(--tw-content);
  background-size: contain;
}

.after\:bg-center::after {
  content: var(--tw-content);
  background-position: center;
}

.after\:bg-no-repeat::after {
  content: var(--tw-content);
  background-repeat: no-repeat;
}

.after\:duration-300::after {
  content: var(--tw-content);
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

.after\:ease-in::after {
  content: var(--tw-content);
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.after\:\!content-none::after {
  --tw-content: none !important;
  content: var(--tw-content) !important;
}

.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}

.after\:content-none::after {
  --tw-content: none;
  content: var(--tw-content);
}

.first\:mb-\[18px\]:first-child {
  margin-bottom: 18px;
}

.first\:mt-0:first-child {
  margin-top: 0;
}

.first\:mt-2:first-child {
  margin-top: 8px;
}

.first\:w-full:first-child {
  width: 100%;
}

.last\:mb-0:last-child {
  margin-bottom: 0;
}

.last\:mr-0:last-child {
  margin-right: 0;
}

.last\:border-none:last-child {
  border-style: none;
}

.last\:bg-blue:last-child {
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
}

.last\:pb-0:last-child {
  padding-bottom: 0;
}

.last\:text-cranleighclay:last-child {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.last\:after\:bg-arrow-right-yellow:last-child::after {
  content: var(--tw-content);
  background-image: url('/wp-content/themes/cranleigh/assets/images/arrow-right-yellow.svg');
}

.odd\:bg-black\/5:nth-child(odd) {
  background-color: rgb(0 0 0 / 0.05);
}

.odd\:pr-8:nth-child(odd) {
  padding-right: 32px;
}

.first-of-type\:visible:first-of-type {
  visibility: visible;
}

.first-of-type\:absolute:first-of-type {
  position: absolute;
}

.first-of-type\:left-0:first-of-type {
  left: 0;
}

.first-of-type\:top-0:first-of-type {
  top: 0;
}

.first-of-type\:mt-0:first-of-type {
  margin-top: 0;
}

.first-of-type\:h-auto:first-of-type {
  height: auto;
}

.first-of-type\:h-full:first-of-type {
  height: 100%;
}

.first-of-type\:w-full:first-of-type {
  width: 100%;
}

.first-of-type\:opacity-100:first-of-type {
  opacity: 1;
}

.hover\:border-grey:hover {
  --tw-border-opacity: 1;
  border-color: rgb(208 208 206 / var(--tw-border-opacity, 1));
}

.hover\:bg-\[\#cca026\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(204 160 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(2 30 66 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cranleighclay:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

.hover\:bg-grey:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(208 208 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-heathlandgreen:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(127 156 144 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 53 69 / var(--tw-bg-opacity, 1));
}

.hover\:bg-weyblue:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(125 156 192 / var(--tw-bg-opacity, 1));
}

.hover\:text-blue:hover {
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

.hover\:text-cranleighclay:hover {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.hover\:opacity-70:hover {
  opacity: 0.7;
}

.hover\:opacity-80:hover {
  opacity: 0.8;
}

.hover\:after\:-translate-y-0\.5:hover::after {
  content: var(--tw-content);
  --tw-translate-y: -2px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:after\:translate-x-0:hover::after {
  content: var(--tw-content);
  --tw-translate-x: 0;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:after\:translate-x-5:hover::after {
  content: var(--tw-content);
  --tw-translate-x: 20px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:after\:translate-y-\[5px\]:hover::after {
  content: var(--tw-content);
  --tw-translate-y: 5px;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.children\:relative > * {
  position: relative;
}

.children\:mb-12 > * {
  margin-bottom: 48px;
}

.children\:mb-2 > * {
  margin-bottom: 8px;
}

.children\:mb-3 > * {
  margin-bottom: 12px;
}

.children\:mb-4 > * {
  margin-bottom: 16px;
}

.children\:ml-5 > * {
  margin-left: 20px;
}

.children\:mr-4 > * {
  margin-right: 16px;
}

.children\:mr-5 > * {
  margin-right: 20px;
}

.children\:flex > * {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.children\:inline-table > * {
  display: inline-table;
}

.children\:aspect-\[3\/2\] > * {
  aspect-ratio: 3/2;
}

.children\:h-\[150vh\] > * {
  height: 150vh;
}

.children\:h-\[34px\] > * {
  height: 34px;
}

.children\:h-\[60px\] > * {
  height: 60px;
}

.children\:h-auto > * {
  height: auto;
}

.children\:h-full > * {
  height: 100%;
}

.children\:w-5\/12 > * {
  width: 41.666667%;
}

.children\:w-\[205px\] > * {
  width: 205px;
}

.children\:w-\[34px\] > * {
  width: 34px;
}

.children\:w-\[60px\] > * {
  width: 60px;
}

.children\:w-full > * {
  width: 100%;
}

.children\:cursor-pointer > * {
  cursor: pointer;
}

.children\:list-none > * {
  list-style-type: none;
}

.children\:items-center > * {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.children\:justify-start > * {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.children\:rounded-\[10px\] > * {
  border-radius: 10px;
}

.children\:rounded-\[33px\] > * {
  border-radius: 33px;
}

.children\:border-b > * {
  border-bottom-width: 1px;
}

.children\:border-white\/20 > * {
  border-color: rgb(*********** / 0.2);
}

.children\:bg-pagination-previous-navy > * {
  background-image: url('/wp-content/themes/cranleigh/assets/images/pagination-previous-navy.svg');
}

.children\:bg-contain > * {
  background-size: contain;
}

.children\:bg-no-repeat > * {
  background-repeat: no-repeat;
}

.children\:object-cover > * {
  -o-object-fit: cover;
     object-fit: cover;
}

.children\:object-center > * {
  -o-object-position: center;
     object-position: center;
}

.children\:px-3 > * {
  padding-left: 12px;
  padding-right: 12px;
}

.children\:px-8 > * {
  padding-left: 32px;
  padding-right: 32px;
}

.children\:py-1 > * {
  padding-top: 4px;
  padding-bottom: 4px;
}

.children\:py-3 > * {
  padding-top: 12px;
  padding-bottom: 12px;
}

.children\:py-\[0px\] > * {
  padding-top: 0px;
  padding-bottom: 0px;
}

.children\:pb-\[14px\] > * {
  padding-bottom: 14px;
}

.children\:pt-\[14px\] > * {
  padding-top: 14px;
}

.children\:indent-\[9999px\] > * {
  text-indent: 9999px;
}

.children\:font-aspectbold > * {
  font-family: proxima-nova, sans-serif;
}

.children\:text-\[12px\] > * {
  font-size: 12px;
}

.children\:text-\[21px\] > * {
  font-size: 21px;
}

.children\:text-base > * {
  font-size: 18px;
  line-height: 28px;
  letter-spacing: normal;
}

.children\:text-copy16 > * {
  font-size: 16px;
  line-height: 22px;
  letter-spacing: normal;
}

.children\:text-copy19 > * {
  font-size: 19px;
  line-height: 22px;
  letter-spacing: -0.3px;
}

.children\:text-titlesidebar > * {
  font-size: 32px;
  line-height: 38px;
  letter-spacing: -0.5px;
}

.children\:text-titlesm > * {
  font-size: 14px;
  line-height: 16px;
  letter-spacing: normal;
}

.children\:uppercase > * {
  text-transform: uppercase;
}

.children\:leading-\[28px\] > * {
  line-height: 28px;
}

.children\:leading-\[30px\] > * {
  line-height: 30px;
}

.children\:leading-\[31px\] > * {
  line-height: 31px;
}

.children\:leading-\[44px\] > * {
  line-height: 44px;
}

.children\:\!text-white > * {
  --tw-text-opacity: 1 !important;
  color: rgb(*********** / var(--tw-text-opacity, 1)) !important;
}

.children\:text-blue > * {
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

.children\:text-weyblue > * {
  --tw-text-opacity: 1;
  color: rgb(125 156 192 / var(--tw-text-opacity, 1));
}

.children\:text-white > * {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity, 1));
}

.children\:opacity-0 > * {
  opacity: 0;
}

.children\:duration-300 > * {
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

.children\:ease-in-out > * {
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.children\:after\:absolute > *::after {
  content: var(--tw-content);
  position: absolute;
}

.children\:after\:-bottom-2 > *::after {
  content: var(--tw-content);
  bottom: -8px;
}

.children\:after\:left-0 > *::after {
  content: var(--tw-content);
  left: 0;
}

.children\:after\:h-0\.5 > *::after {
  content: var(--tw-content);
  height: 2px;
}

.children\:after\:w-full > *::after {
  content: var(--tw-content);
  width: 100%;
}

.children\:after\:content-\[\'\'\] > *::after {
  --tw-content: '';
  content: var(--tw-content);
}

.children\:last\:mr-0:last-child > * {
  margin-right: 0;
}

.last\:children\:mb-0 > *:last-child {
  margin-bottom: 0;
}

.last\:children\:mr-0 > *:last-child {
  margin-right: 0;
}

.last\:children\:border-b-0 > *:last-child {
  border-bottom-width: 0px;
}

.odd\:children\:border-r > *:nth-child(odd) {
  border-right-width: 1px;
}

@media (min-width: 640px) {
  .sm\:left-\[100px\] {
    left: 100px;
  }
}

@media (min-width: 768px) {
  .md\:bottom-9 {
    bottom: 36px;
  }

  .md\:bottom-\[2vw\] {
    bottom: 2vw;
  }

  .md\:left-10 {
    left: 40px;
  }

  .md\:left-\[95px\] {
    left: 95px;
  }

  .md\:mx-0 {
    margin-left: 0;
    margin-right: 0;
  }

  .md\:mb-4 {
    margin-bottom: 16px;
  }

  .md\:mb-5 {
    margin-bottom: 20px;
  }

  .md\:mt-0 {
    margin-top: 0;
  }

  .md\:mt-10 {
    margin-top: 40px;
  }

  .md\:mt-2 {
    margin-top: 8px;
  }

  .md\:mt-\[20vw\] {
    margin-top: 20vw;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:h-\[400px\] {
    height: 400px;
  }

  .md\:min-h-\[280px\] {
    min-height: 280px;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-4\/12 {
    width: 33.333333%;
  }

  .md\:w-9\/12 {
    width: 75%;
  }

  .md\:w-\[203px\] {
    width: 203px;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:border-none {
    border-style: none;
  }

  .md\:p-14 {
    padding: 56px;
  }

  .md\:px-10 {
    padding-left: 40px;
    padding-right: 40px;
  }

  .md\:px-5 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .md\:px-6 {
    padding-left: 24px;
    padding-right: 24px;
  }

  .md\:pr-8 {
    padding-right: 32px;
  }

  .md\:text-\[10vw\] {
    font-size: 10vw;
  }

  .md\:text-\[13px\] {
    font-size: 13px;
  }

  .md\:text-\[34px\] {
    font-size: 34px;
  }

  .md\:text-\[48px\] {
    font-size: 48px;
  }

  .md\:text-\[86px\] {
    font-size: 86px;
  }

  .md\:text-base {
    font-size: 18px;
    line-height: 28px;
    letter-spacing: normal;
  }

  .md\:text-titlesm {
    font-size: 14px;
    line-height: 16px;
    letter-spacing: normal;
  }

  .md\:leading-\[24px\] {
    line-height: 24px;
  }

  .md\:leading-\[36px\] {
    line-height: 36px;
  }

  .after\:md\:-left-\[108px\]::after {
    content: var(--tw-content);
    left: -108px;
  }

  .md\:children\:mb-4 > * {
    margin-bottom: 16px;
  }
}

@media (min-width: 1024px) {
  .lg\:-left-\[100px\] {
    left: -100px;
  }

  .lg\:-left-\[60px\] {
    left: -60px;
  }

  .lg\:bottom-16 {
    bottom: 64px;
  }

  .lg\:bottom-8 {
    bottom: 32px;
  }

  .lg\:bottom-\[5vw\] {
    bottom: 5vw;
  }

  .lg\:left-8 {
    left: 32px;
  }

  .lg\:left-auto {
    left: auto;
  }

  .lg\:right-14 {
    right: 56px;
  }

  .lg\:right-20 {
    right: 80px;
  }

  .lg\:top-\[35px\] {
    top: 35px;
  }

  .lg\:top-\[70px\] {
    top: 70px;
  }

  .lg\:top-\[96px\] {
    top: 96px;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-start-2 {
    grid-column-start: 2;
  }

  .lg\:mx-0 {
    margin-left: 0;
    margin-right: 0;
  }

  .lg\:my-14 {
    margin-top: 56px;
    margin-bottom: 56px;
  }

  .lg\:-mb-10 {
    margin-bottom: -40px;
  }

  .lg\:-mt-10 {
    margin-top: -40px;
  }

  .lg\:-mt-32 {
    margin-top: -128px;
  }

  .lg\:mb-0 {
    margin-bottom: 0;
  }

  .lg\:mb-10 {
    margin-bottom: 40px;
  }

  .lg\:mb-12 {
    margin-bottom: 48px;
  }

  .lg\:mb-14 {
    margin-bottom: 56px;
  }

  .lg\:mb-20 {
    margin-bottom: 80px;
  }

  .lg\:mb-24 {
    margin-bottom: 96px;
  }

  .lg\:mb-4 {
    margin-bottom: 16px;
  }

  .lg\:mb-6 {
    margin-bottom: 24px;
  }

  .lg\:mb-8 {
    margin-bottom: 32px;
  }

  .lg\:mb-\[30px\] {
    margin-bottom: 30px;
  }

  .lg\:mb-\[44px\] {
    margin-bottom: 44px;
  }

  .lg\:mb-\[50px\] {
    margin-bottom: 50px;
  }

  .lg\:mb-\[74px\] {
    margin-bottom: 74px;
  }

  .lg\:mb-\[80px\] {
    margin-bottom: 80px;
  }

  .lg\:ml-10 {
    margin-left: 40px;
  }

  .lg\:ml-5 {
    margin-left: 20px;
  }

  .lg\:ml-auto {
    margin-left: auto;
  }

  .lg\:mr-10 {
    margin-right: 40px;
  }

  .lg\:mr-8 {
    margin-right: 32px;
  }

  .lg\:mr-auto {
    margin-right: auto;
  }

  .lg\:mt-0 {
    margin-top: 0;
  }

  .lg\:mt-12 {
    margin-top: 48px;
  }

  .lg\:mt-14 {
    margin-top: 56px;
  }

  .lg\:mt-2 {
    margin-top: 8px;
  }

  .lg\:mt-20 {
    margin-top: 80px;
  }

  .lg\:mt-24 {
    margin-top: 96px;
  }

  .lg\:mt-28 {
    margin-top: 112px;
  }

  .lg\:mt-3 {
    margin-top: 12px;
  }

  .lg\:mt-32 {
    margin-top: 128px;
  }

  .lg\:mt-4 {
    margin-top: 16px;
  }

  .lg\:mt-40 {
    margin-top: 160px;
  }

  .lg\:mt-6 {
    margin-top: 24px;
  }

  .lg\:mt-9 {
    margin-top: 36px;
  }

  .lg\:mt-\[10vw\] {
    margin-top: 10vw;
  }

  .lg\:mt-\[120px\] {
    margin-top: 120px;
  }

  .lg\:mt-\[190px\] {
    margin-top: 190px;
  }

  .lg\:mt-\[206px\] {
    margin-top: 206px;
  }

  .lg\:mt-\[3vw\] {
    margin-top: 3vw;
  }

  .lg\:mt-\[66px\] {
    margin-top: 66px;
  }

  .lg\:mt-\[77px\] {
    margin-top: 77px;
  }

  .lg\:mt-\[80px\] {
    margin-top: 80px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .lg\:table {
    display: table;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:aspect-\[3\/2\] {
    aspect-ratio: 3/2;
  }

  .lg\:h-\[151px\] {
    height: 151px;
  }

  .lg\:h-\[650px\] {
    height: 650px;
  }

  .lg\:h-\[90vh\] {
    height: 90vh;
  }

  .lg\:h-screen {
    height: 100vh;
  }

  .lg\:min-h-\[189px\] {
    min-height: 189px;
  }

  .lg\:min-h-\[270px\] {
    min-height: 270px;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-10\/12 {
    width: 83.333333%;
  }

  .lg\:w-11\/12 {
    width: 91.666667%;
  }

  .lg\:w-2\/12 {
    width: 16.666667%;
  }

  .lg\:w-3\/12 {
    width: 25%;
  }

  .lg\:w-4\/12 {
    width: 33.333333%;
  }

  .lg\:w-5\/12 {
    width: 41.666667%;
  }

  .lg\:w-6\/12 {
    width: 50%;
  }

  .lg\:w-7\/12 {
    width: 58.333333%;
  }

  .lg\:w-8\/12 {
    width: 66.666667%;
  }

  .lg\:w-9\/12 {
    width: 75%;
  }

  .lg\:w-\[40vw\] {
    width: 40vw;
  }

  .lg\:w-\[590px\] {
    width: 590px;
  }

  .lg\:w-\[600px\] {
    width: 600px;
  }

  .lg\:w-\[62\%\] {
    width: 62%;
  }

  .lg\:w-\[90vw\] {
    width: 90vw;
  }

  .lg\:w-\[calc\(100\%_\/_3_-_12px\)\] {
    width: calc(100% / 3 - 12px);
  }

  .lg\:w-\[calc\(100vw-124px\)\] {
    width: calc(100vw - 124px);
  }

  .lg\:w-\[calc\(50\%_-_100px\)\] {
    width: calc(50% - 100px);
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:max-w-\[calc\(725px_-_100px\)\] {
    max-width: calc(725px - 100px);
  }

  .lg\:-translate-y-12 {
    --tw-translate-y: -48px;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:-translate-y-28 {
    --tw-translate-y: -112px;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:-translate-y-7 {
    --tw-translate-y: -28px;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:-translate-y-\[100px\] {
    --tw-translate-y: -100px;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-x-0 {
    --tw-translate-x: 0;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-\[22px\] {
    --tw-translate-y: 22px;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-\[5vw\] {
    --tw-translate-y: 5vw;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-\[70px\] {
    --tw-translate-y: 70px;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:rotate-0 {
    --tw-rotate: 0deg;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:grid-rows-2 {
    grid-template-rows: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-rows-\[130px_90px_90px_130px_130px\] {
    grid-template-rows: 130px 90px 90px 130px 130px;
  }

  .lg\:flex-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }

  .lg\:flex-nowrap {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
  }

  .lg\:items-center {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }

  .lg\:justify-start {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }

  .lg\:justify-end {
    -webkit-box-pack: end;
        -ms-flex-pack: end;
            justify-content: flex-end;
  }

  .lg\:justify-between {
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }

  .lg\:gap-4 {
    gap: 16px;
  }

  .lg\:gap-8 {
    gap: 32px;
  }

  .lg\:gap-x-\[30px\] {
    -webkit-column-gap: 30px;
       -moz-column-gap: 30px;
            column-gap: 30px;
  }

  .lg\:gap-y-14 {
    row-gap: 56px;
  }

  .lg\:overflow-visible {
    overflow: visible;
  }

  .lg\:bg-cranleighclay {
    --tw-bg-opacity: 1;
    background-color: rgb(*********** / var(--tw-bg-opacity, 1));
  }

  .lg\:p-0 {
    padding: 0;
  }

  .lg\:p-10 {
    padding: 40px;
  }

  .lg\:p-3 {
    padding: 12px;
  }

  .lg\:px-0 {
    padding-left: 0;
    padding-right: 0;
  }

  .lg\:px-10 {
    padding-left: 40px;
    padding-right: 40px;
  }

  .lg\:px-20 {
    padding-left: 80px;
    padding-right: 80px;
  }

  .lg\:px-\[50px\] {
    padding-left: 50px;
    padding-right: 50px;
  }

  .lg\:px-\[57px\] {
    padding-left: 57px;
    padding-right: 57px;
  }

  .lg\:px-\[60px\] {
    padding-left: 60px;
    padding-right: 60px;
  }

  .lg\:px-\[68px\] {
    padding-left: 68px;
    padding-right: 68px;
  }

  .lg\:py-0 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .lg\:py-10 {
    padding-top: 40px;
    padding-bottom: 40px;
  }

  .lg\:pb-0 {
    padding-bottom: 0;
  }

  .lg\:pb-20 {
    padding-bottom: 80px;
  }

  .lg\:pb-28 {
    padding-bottom: 112px;
  }

  .lg\:pb-8 {
    padding-bottom: 32px;
  }

  .lg\:pb-\[100px\] {
    padding-bottom: 100px;
  }

  .lg\:pb-\[138px\] {
    padding-bottom: 138px;
  }

  .lg\:pb-\[18px\] {
    padding-bottom: 18px;
  }

  .lg\:pb-\[44px\] {
    padding-bottom: 44px;
  }

  .lg\:pb-\[50px\] {
    padding-bottom: 50px;
  }

  .lg\:pb-\[70px\] {
    padding-bottom: 70px;
  }

  .lg\:pl-0 {
    padding-left: 0;
  }

  .lg\:pl-10 {
    padding-left: 40px;
  }

  .lg\:pl-14 {
    padding-left: 56px;
  }

  .lg\:pl-20 {
    padding-left: 80px;
  }

  .lg\:pl-5 {
    padding-left: 20px;
  }

  .lg\:pl-6 {
    padding-left: 24px;
  }

  .lg\:pl-8 {
    padding-left: 32px;
  }

  .lg\:pl-\[10\%\] {
    padding-left: 10%;
  }

  .lg\:pl-\[120px\] {
    padding-left: 120px;
  }

  .lg\:pr-0 {
    padding-right: 0;
  }

  .lg\:pr-20 {
    padding-right: 80px;
  }

  .lg\:pr-6 {
    padding-right: 24px;
  }

  .lg\:pr-8 {
    padding-right: 32px;
  }

  .lg\:pr-\[10\%\] {
    padding-right: 10%;
  }

  .lg\:pr-\[160px\] {
    padding-right: 160px;
  }

  .lg\:pt-0 {
    padding-top: 0;
  }

  .lg\:pt-10 {
    padding-top: 40px;
  }

  .lg\:pt-20 {
    padding-top: 80px;
  }

  .lg\:pt-6 {
    padding-top: 24px;
  }

  .lg\:pt-\[236px\] {
    padding-top: 236px;
  }

  .lg\:pt-\[54px\] {
    padding-top: 54px;
  }

  .lg\:pt-\[76px\] {
    padding-top: 76px;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-center {
    text-align: center;
  }

  .lg\:text-right {
    text-align: right;
  }

  .lg\:text-\[100px\] {
    font-size: 100px;
  }

  .lg\:text-\[11vw\] {
    font-size: 11vw;
  }

  .lg\:text-\[120px\] {
    font-size: 120px;
  }

  .lg\:text-\[124px\] {
    font-size: 124px;
  }

  .lg\:text-\[21px\] {
    font-size: 21px;
  }

  .lg\:text-\[22px\] {
    font-size: 22px;
  }

  .lg\:text-\[40px\] {
    font-size: 40px;
  }

  .lg\:text-\[78px\] {
    font-size: 78px;
  }

  .lg\:text-base {
    font-size: 18px;
    line-height: 28px;
    letter-spacing: normal;
  }

  .lg\:text-copy16 {
    font-size: 16px;
    line-height: 22px;
    letter-spacing: normal;
  }

  .lg\:text-copy19 {
    font-size: 19px;
    line-height: 22px;
    letter-spacing: -0.3px;
  }

  .lg\:text-title {
    font-size: 44px;
    line-height: 48px;
    letter-spacing: -0.75px;
  }

  .lg\:text-titleh2 {
    font-size: 60px;
    line-height: 60px;
    letter-spacing: -1px;
  }

  .lg\:text-titleh4 {
    font-size: 36px;
    line-height: 40px;
    letter-spacing: -0.75px;
  }

  .lg\:text-titlelg {
    font-size: 90px;
    line-height: 90px;
    letter-spacing: -1.5px;
  }

  .lg\:text-titlemd {
    font-size: 24px;
    line-height: 31px;
    letter-spacing: -0.25px;
  }

  .lg\:text-titlesidebar {
    font-size: 32px;
    line-height: 38px;
    letter-spacing: -0.5px;
  }

  .lg\:text-titlesm {
    font-size: 14px;
    line-height: 16px;
    letter-spacing: normal;
  }

  .lg\:text-titlexl {
    font-size: 172px;
    line-height: 172px;
    letter-spacing: -4px;
  }

  .lg\:leading-\[11vw\] {
    line-height: 11vw;
  }

  .lg\:leading-\[120px\] {
    line-height: 120px;
  }

  .lg\:leading-\[140px\] {
    line-height: 140px;
  }

  .lg\:leading-\[150px\] {
    line-height: 150px;
  }

  .lg\:leading-\[16px\] {
    line-height: 16px;
  }

  .lg\:leading-\[36px\] {
    line-height: 36px;
  }

  .lg\:leading-\[48px\] {
    line-height: 48px;
  }

  .lg\:leading-\[76px\] {
    line-height: 76px;
  }

  .lg\:tracking-\[-1\.14px\] {
    letter-spacing: -1.14px;
  }

  .lg\:tracking-\[-1\.67px\] {
    letter-spacing: -1.67px;
  }

  .lg\:text-weyblue {
    --tw-text-opacity: 1;
    color: rgb(125 156 192 / var(--tw-text-opacity, 1));
  }

  .lg\:before\:content-\[\'\'\]::before {
    --tw-content: '';
    content: var(--tw-content);
  }

  .lg\:after\:-right-\[37px\]::after {
    content: var(--tw-content);
    right: -37px;
  }

  .lg\:after\:left-auto::after {
    content: var(--tw-content);
    left: auto;
  }

  .lg\:after\:top-1\/2::after {
    content: var(--tw-content);
    top: 50%;
  }

  .lg\:after\:h-\[25\%\]::after {
    content: var(--tw-content);
    height: 25%;
  }

  .lg\:after\:h-\[40\%\]::after {
    content: var(--tw-content);
    height: 40%;
  }

  .lg\:after\:h-\[51px\]::after {
    content: var(--tw-content);
    height: 51px;
  }

  .lg\:after\:h-\[74px\]::after {
    content: var(--tw-content);
    height: 74px;
  }

  .lg\:after\:w-\[54px\]::after {
    content: var(--tw-content);
    width: 54px;
  }

  .lg\:after\:w-\[74px\]::after {
    content: var(--tw-content);
    width: 74px;
  }

  .lg\:after\:-translate-y-1\/2::after {
    content: var(--tw-content);
    --tw-translate-y: -50%;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:after\:bg-pdf-icon-navy::after {
    content: var(--tw-content);
    background-image: url('/wp-content/themes/cranleigh/assets/images/pdf-icon-navy.svg');
  }

  .lg\:after\:to-black\/100::after {
    content: var(--tw-content);
    --tw-gradient-to: rgb(0 0 0 / 1) var(--tw-gradient-to-position);
  }

  .lg\:after\:to-black\/50::after {
    content: var(--tw-content);
    --tw-gradient-to: rgb(0 0 0 / 0.5) var(--tw-gradient-to-position);
  }

  .lg\:after\:to-black\/70::after {
    content: var(--tw-content);
    --tw-gradient-to: rgb(0 0 0 / 0.7) var(--tw-gradient-to-position);
  }

  .lg\:after\:bg-contain::after {
    content: var(--tw-content);
    background-size: contain;
  }

  .lg\:after\:bg-center::after {
    content: var(--tw-content);
    background-position: center;
  }

  .lg\:after\:bg-no-repeat::after {
    content: var(--tw-content);
    background-repeat: no-repeat;
  }

  .lg\:after\:content-\[\'\'\]::after {
    --tw-content: '';
    content: var(--tw-content);
  }

  .lg\:after\:content-none::after {
    --tw-content: none;
    content: var(--tw-content);
  }

  .lg\:first\:mb-6:first-child {
    margin-bottom: 24px;
  }

  .lg\:last\:ml-8:last-child {
    margin-left: 32px;
  }

  .children\:lg\:mr-6 > * {
    margin-right: 24px;
  }

  .lg\:children\:mr-10 > * {
    margin-right: 40px;
  }

  .children\:lg\:h-\[65px\] > * {
    height: 65px;
  }

  .children\:lg\:w-1\/2 > * {
    width: 50%;
  }

  .children\:lg\:w-\[65px\] > * {
    width: 65px;
  }

  .children\:lg\:w-auto > * {
    width: auto;
  }

  .lg\:children\:w-\[243px\] > * {
    width: 243px;
  }

  .children\:lg\:px-5 > * {
    padding-left: 20px;
    padding-right: 20px;
  }

  .children\:lg\:py-6 > * {
    padding-top: 24px;
    padding-bottom: 24px;
  }

  .children\:lg\:pb-5 > * {
    padding-bottom: 20px;
  }

  .children\:lg\:text-copy16 > * {
    font-size: 16px;
    line-height: 22px;
    letter-spacing: normal;
  }

  .children\:lg\:text-copy19 > * {
    font-size: 19px;
    line-height: 22px;
    letter-spacing: -0.3px;
  }

  .children\:lg\:text-titlemd > * {
    font-size: 24px;
    line-height: 31px;
    letter-spacing: -0.25px;
  }

  .children\:lg\:leading-\[30px\] > * {
    line-height: 30px;
  }
}

@media (min-width: 1280px) {
  .xl\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .xl\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .xl\:-mb-32 {
    margin-bottom: -128px;
  }

  .xl\:mb-6 {
    margin-bottom: 24px;
  }

  .xl\:mt-0 {
    margin-top: 0;
  }

  .xl\:flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .xl\:w-1\/2 {
    width: 50%;
  }

  .xl\:w-10\/12 {
    width: 83.333333%;
  }

  .xl\:w-5\/12 {
    width: 41.666667%;
  }

  .xl\:w-9\/12 {
    width: 75%;
  }

  .xl\:bg-fixed {
    background-attachment: fixed;
  }

  .xl\:pl-16 {
    padding-left: 64px;
  }

  .xl\:text-copy16 {
    font-size: 16px;
    line-height: 22px;
    letter-spacing: normal;
  }

  .xl\:text-title {
    font-size: 44px;
    line-height: 48px;
    letter-spacing: -0.75px;
  }

  .xl\:text-titlelg {
    font-size: 90px;
    line-height: 90px;
    letter-spacing: -1.5px;
  }

  .xl\:text-titlexl {
    font-size: 172px;
    line-height: 172px;
    letter-spacing: -4px;
  }

  .xl\:after\:h-\[40\%\]::after {
    content: var(--tw-content);
    height: 40%;
  }
}

.\[\&\:nth-child\(1\)\]\:children\:col-span-2 > *:nth-child(1) {
  grid-column: span 2 / span 2;
}

.\[\&\:nth-child\(1\)\]\:children\:col-span-4 > *:nth-child(1) {
  grid-column: span 4 / span 4;
}

.\[\&\:nth-child\(1\)\]\:children\:col-start-2 > *:nth-child(1) {
  grid-column-start: 2;
}

.\[\&\:nth-child\(1\)\]\:children\:col-start-4 > *:nth-child(1) {
  grid-column-start: 4;
}

.\[\&\:nth-child\(1\)\]\:children\:row-span-3 > *:nth-child(1) {
  grid-row: span 3 / span 3;
}

.\[\&\:nth-child\(1\)\]\:children\:row-start-2 > *:nth-child(1) {
  grid-row-start: 2;
}

.\[\&\:nth-child\(1\)\]\:children\:mt-10 > *:nth-child(1) {
  margin-top: 40px;
}

.\[\&\:nth-child\(1\)\]\:children\:pl-8 > *:nth-child(1) {
  padding-left: 32px;
}

@media (min-width: 1024px) {
  .\[\&\:nth-child\(1\)\]\:children\:lg\:mt-0 > *:nth-child(1) {
    margin-top: 0;
  }

  .\[\&\:nth-child\(1\)\]\:children\:lg\:w-7\/12 > *:nth-child(1) {
    width: 58.333333%;
  }

  .\[\&\:nth-child\(1\)\]\:children\:lg\:pl-8 > *:nth-child(1) {
    padding-left: 32px;
  }
}

.\[\&\:nth-child\(2\)\]\:children\:col-span-2 > *:nth-child(2) {
  grid-column: span 2 / span 2;
}

.\[\&\:nth-child\(2\)\]\:children\:col-span-3 > *:nth-child(2) {
  grid-column: span 3 / span 3;
}

.\[\&\:nth-child\(2\)\]\:children\:col-start-4 > *:nth-child(2) {
  grid-column-start: 4;
}

.\[\&\:nth-child\(2\)\]\:children\:col-start-6 > *:nth-child(2) {
  grid-column-start: 6;
}

.\[\&\:nth-child\(2\)\]\:children\:row-span-1 > *:nth-child(2) {
  grid-row: span 1 / span 1;
}

.\[\&\:nth-child\(2\)\]\:children\:row-span-2 > *:nth-child(2) {
  grid-row: span 2 / span 2;
}

.\[\&\:nth-child\(2\)\]\:children\:row-start-1 > *:nth-child(2) {
  grid-row-start: 1;
}

.\[\&\:nth-child\(2\)\]\:children\:mt-14 > *:nth-child(2) {
  margin-top: 56px;
}

.\[\&\:nth-child\(2\)\]\:children\:pr-8 > *:nth-child(2) {
  padding-right: 32px;
}

.\[\&\:nth-child\(2\)\]\:children\:pt-10 > *:nth-child(2) {
  padding-top: 40px;
}

@media (min-width: 1024px) {
  .\[\&\:nth-child\(2\)\]\:children\:lg\:mt-28 > *:nth-child(2) {
    margin-top: 112px;
  }

  .\[\&\:nth-child\(2\)\]\:children\:lg\:w-4\/12 > *:nth-child(2) {
    width: 33.333333%;
  }

  .\[\&\:nth-child\(2\)\]\:children\:lg\:pt-10 > *:nth-child(2) {
    padding-top: 40px;
  }
}

.\[\&\:nth-child\(3\)\]\:children\:col-span-3 > *:nth-child(3) {
  grid-column: span 3 / span 3;
}

.\[\&\:nth-child\(3\)\]\:children\:col-start-1 > *:nth-child(3) {
  grid-column-start: 1;
}

.\[\&\:nth-child\(3\)\]\:children\:row-span-2 > *:nth-child(3) {
  grid-row: span 2 / span 2;
}

.\[\&\:nth-child\(3\)\]\:children\:row-span-3 > *:nth-child(3) {
  grid-row: span 3 / span 3;
}

.\[\&\:nth-child\(3\)\]\:children\:row-start-1 > *:nth-child(3) {
  grid-row-start: 1;
}

.\[\&\:nth-child\(3\)\]\:children\:mt-10 > *:nth-child(3) {
  margin-top: 40px;
}

.\[\&\:nth-child\(3\)\]\:children\:pb-24 > *:nth-child(3) {
  padding-bottom: 96px;
}

.\[\&\:nth-child\(3\)\]\:children\:pl-10 > *:nth-child(3) {
  padding-left: 40px;
}

@media (min-width: 1024px) {
  .\[\&\:nth-child\(3\)\]\:children\:lg\:ml-\[16\%\] > *:nth-child(3) {
    margin-left: 16%;
  }

  .\[\&\:nth-child\(3\)\]\:children\:lg\:mt-24 > *:nth-child(3) {
    margin-top: 96px;
  }

  .\[\&\:nth-child\(3\)\]\:children\:lg\:w-5\/12 > *:nth-child(3) {
    width: 41.666667%;
  }

  .\[\&\:nth-child\(3\)\]\:children\:lg\:pb-24 > *:nth-child(3) {
    padding-bottom: 96px;
  }

  .\[\&\:nth-child\(3\)\]\:children\:lg\:pl-10 > *:nth-child(3) {
    padding-left: 40px;
  }

  .\[\&\:nth-child\(3\)\]\:children\:lg\:pl-24 > *:nth-child(3) {
    padding-left: 96px;
  }

  .\[\&\:nth-child\(3\)\]\:children\:lg\:pt-20 > *:nth-child(3) {
    padding-top: 80px;
  }
}

.\[\&\:nth-child\(4\)\]\:children\:col-span-3 > *:nth-child(4) {
  grid-column: span 3 / span 3;
}

.\[\&\:nth-child\(4\)\]\:children\:col-start-4 > *:nth-child(4) {
  grid-column-start: 4;
}

.\[\&\:nth-child\(4\)\]\:children\:col-start-8 > *:nth-child(4) {
  grid-column-start: 8;
}

.\[\&\:nth-child\(4\)\]\:children\:row-span-3 > *:nth-child(4) {
  grid-row: span 3 / span 3;
}

.\[\&\:nth-child\(4\)\]\:children\:row-start-2 > *:nth-child(4) {
  grid-row-start: 2;
}

@media (min-width: 1024px) {
  .\[\&\:nth-child\(4\)\]\:children\:lg\:pb-10 > *:nth-child(4) {
    padding-bottom: 40px;
  }

  .\[\&\:nth-child\(4\)\]\:children\:lg\:pr-24 > *:nth-child(4) {
    padding-right: 96px;
  }
}

.\[\&\:nth-child\(5\)\]\:children\:col-span-2 > *:nth-child(5) {
  grid-column: span 2 / span 2;
}

.\[\&\:nth-child\(5\)\]\:children\:col-start-2 > *:nth-child(5) {
  grid-column-start: 2;
}

.\[\&\:nth-child\(5\)\]\:children\:row-span-2 > *:nth-child(5) {
  grid-row: span 2 / span 2;
}

.\[\&\:nth-child\(5\)\]\:children\:row-start-4 > *:nth-child(5) {
  grid-row-start: 4;
}

@media (min-width: 1024px) {
  .\[\&\:nth-child\(5\)\]\:children\:lg\:pb-20 > *:nth-child(5) {
    padding-bottom: 80px;
  }

  .\[\&\:nth-child\(5\)\]\:children\:lg\:pl-20 > *:nth-child(5) {
    padding-left: 80px;
  }
}

.\[\&\>div\:nth-child\(3\)\]\:border-none>div:nth-child(3) {
  border-style: none;
}

.\[\&_\.box\]\:\[\&\:nth-child\(1\)\]\:children\:relative > *:nth-child(1) .box {
  position: relative;
}

.\[\&_\.box\]\:\[\&\:nth-child\(1\)\]\:children\:bottom-0 > *:nth-child(1) .box {
  bottom: 0;
}

.\[\&_\.box\]\:\[\&\:nth-child\(1\)\]\:children\:right-0 > *:nth-child(1) .box {
  right: 0;
}

.\[\&_\.box\]\:\[\&\:nth-child\(1\)\]\:children\:block > *:nth-child(1) .box {
  display: block;
}

.\[\&_\.box\]\:\[\&\:nth-child\(1\)\]\:children\:bg-white > *:nth-child(1) .box {
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

@media (min-width: 1024px) {
  .\[\&_\.box\]\:\[\&\:nth-child\(1\)\]\:children\:lg\:absolute > *:nth-child(1) .box {
    position: absolute;
  }

  .\[\&_\.box\]\:\[\&\:nth-child\(1\)\]\:children\:lg\:w-4\/5 > *:nth-child(1) .box {
    width: 80%;
  }

  .\[\&_\.box\]\:\[\&\:nth-child\(1\)\]\:children\:lg\:pl-12 > *:nth-child(1) .box {
    padding-left: 48px;
  }

  .\[\&_\.box\]\:\[\&\:nth-child\(1\)\]\:children\:lg\:pt-12 > *:nth-child(1) .box {
    padding-top: 48px;
  }
}

.\[\&_\.icontochange\]\:after\:hover\:brightness-0:hover .icontochange::after {
  content: var(--tw-content);
  --tw-brightness: brightness(0);
  -webkit-filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
          filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.\[\&_\.icontochange\]\:after\:hover\:invert:hover .icontochange::after {
  content: var(--tw-content);
  --tw-invert: invert(100%);
  -webkit-filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
          filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.\[\&_\.screen-reader-text\]\:hidden .screen-reader-text {
  display: none;
}

.\[\&_\.search-field\]\:w-full .search-field {
  width: 100%;
}

.\[\&_\.search-field\]\:py-\[13px\] .search-field {
  padding-top: 13px;
  padding-bottom: 13px;
}

.\[\&_\.search-field\]\:pl-6 .search-field {
  padding-left: 24px;
}

.\[\&_\.search-field\]\:pr-14 .search-field {
  padding-right: 56px;
}

.\[\&_\.search-field\]\:text-textfooter .search-field {
  font-size: 16px;
  line-height: 24px;
  letter-spacing: normal;
}

.\[\&_\.search-field\]\:outline-none .search-field {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.\[\&_\.search-submit\]\:absolute .search-submit {
  position: absolute;
}

.\[\&_\.search-submit\]\:right-0 .search-submit {
  right: 0;
}

.\[\&_\.search-submit\]\:top-0 .search-submit {
  top: 0;
}

.\[\&_\.search-submit\]\:block .search-submit {
  display: block;
}

.\[\&_\.search-submit\]\:h-full .search-submit {
  height: 100%;
}

.\[\&_\.search-submit\]\:w-14 .search-submit {
  width: 56px;
}

.\[\&_\.search-submit\]\:cursor-pointer .search-submit {
  cursor: pointer;
}

.\[\&_\.search-submit\]\:bg-icon-search-blue .search-submit {
  background-image: url('/wp-content/themes/cranleigh/assets/images/icon-search-blue.svg');
}

.\[\&_\.search-submit\]\:bg-\[29px\] .search-submit {
  background-position: 29px;
}

.\[\&_\.search-submit\]\:bg-center .search-submit {
  background-position: center;
}

.\[\&_\.search-submit\]\:bg-no-repeat .search-submit {
  background-repeat: no-repeat;
}

.\[\&_\.search-submit\]\:indent-\[9999px\] .search-submit {
  text-indent: 9999px;
}

.\[\&_a\]\:\!text-white a {
  --tw-text-opacity: 1 !important;
  color: rgb(*********** / var(--tw-text-opacity, 1)) !important;
}

.\[\&_a\]\:text-yellow a {
  --tw-text-opacity: 1;
  color: rgb(255 193 7 / var(--tw-text-opacity, 1));
}

.\[\&_a\]\:\!underline a {
  text-decoration-line: underline !important;
}

.\[\&_a\]\:underline a {
  text-decoration-line: underline;
}

.\[\&_a\]\:\!underline-offset-4 a {
  text-underline-offset: 4px !important;
}

.\[\&_button\[aria-selected\=\'true\'\]\]\:bg-cranleighclay button[aria-selected='true'] {
  --tw-bg-opacity: 1;
  background-color: rgb(*********** / var(--tw-bg-opacity, 1));
}

.\[\&_button\[aria-selected\=\'true\'\]\]\:text-blue button[aria-selected='true'] {
  --tw-text-opacity: 1;
  color: rgb(2 30 66 / var(--tw-text-opacity, 1));
}

.\[\&_button\[aria-selected\=\'true\'\]\]\:opacity-100 button[aria-selected='true'] {
  opacity: 1;
}

.\[\&_form\]\:relative form {
  position: relative;
}

.\[\&_img\]\:aspect-\[3\/2\] img {
  aspect-ratio: 3/2;
}

.\[\&_img\]\:object-cover img {
  -o-object-fit: cover;
     object-fit: cover;
}

.\[\&_img\]\:hover\:scale-\[1\.3\]:hover img {
  --tw-scale-x: 1.3;
  --tw-scale-y: 1.3;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      -ms-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (min-width: 1024px) {
  .lg\:\[\&_img\]\:float-left img {
    float: left;
  }

  .lg\:\[\&_img\]\:mr-6 img {
    margin-right: 24px;
  }

  .lg\:\[\&_img\]\:mt-0 img {
    margin-top: 0;
  }
}

@media(max-height:750px) and (min-width:900px) {
  .\[\@media\(max-height\:750px\)_and_\(min-width\:900px\)\]\:pt-\[140px\] {
    padding-top: 140px;
  }
}