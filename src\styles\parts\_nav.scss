.top-nav {
    ///BASE THEME STYLING
    background-color: $primary;
    width: 100%;
    padding: 25px;
    /////////////////////
	&__logo {
        img {
            width: 250px;
        }   
    }

    ul {
        @include beauty-lists;
        li {
			position: relative;
			display: inline-block;
            a {
				display: inline-block;
                padding: 20px;
                color: #FFFFFF;
                font-size: rem(14px);
                letter-spacing: 1px;
                text-transform: uppercase;
                position: relative;
                &:after {
                    position: absolute;   
                    content: '';        
                    left: 50%;
                    width: 0;
                    height: 1px;
                    bottom: 10px;
                    background-color: $secondary;
                    @include tran-prefix(all 0.3s ease-in);
                }
                &:hover {
                    text-decoration: none;
                    &:after {
                        width: calc(100% - 40px);
                        left: 20px;  
                    }
                }
            }
            &.current_page_item {   
                a:after {
                    width: calc(100% - 40px);
                    left: 20px;
                }
            }
        }
    }
	/* SUBMENU */
	.sub-menu-wrap{
		position: absolute;
		z-index: 10;
		right: 0px;
		opacity: 0;
		pointer-events: none;
		text-align: left;
		min-width: 200px;
		transform: translateY(50px);
		transition: all 0.4s ease-in-out;

		background-color: $primary;
		color: $white;
		padding: 40px 0;

		.sub-menu-title,
		.menu-item {
			display: block;

			a{
				padding: 15px 20px;
			}
		}

		&.sub-menu-open{
			opacity: 1;
			pointer-events: all;
			transform: translateY(0px);
		}
	}

	/* MOBILE */
	.back-menu{
		display: none;
	}
}

.menu-main-container {
    padding-top: 40px;
}