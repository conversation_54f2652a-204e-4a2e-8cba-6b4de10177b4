<?php
/**
 * Template Name: Trips Planner
 *
 * @package twkmedia
 */

get_header();

if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();
		?>

		
		<section class="bg-blue pt-[170px] pb-[50px] lg:pb-20 mb-14">
			<div class="container text-left">
				<div class="w-10/12 mx-auto">
					<span class="font-aspectbold text-white text-base uppercase mb-6"><?php the_field('subtitle_bannertrip');?></span>
					<h1 class="font-aspectbold text-white text-title46 lg:text-titlelg uppercase mt-6"><?php the_field('title_bannertrip');?></h1>
					<div class="lg:flex items-start justify-between">
						<div class="wysiwyg wysiwygbanner lg:w-6/12 children:text-white mt-8">
							<p><?php the_field('copy_left_bannertrip');?></p>
						</div>
						<div class="wysiwyg wysiwygbanner lg:w-6/12 children:text-white lg:px-10 mt-8">
							<p><?php the_field('copy_right_bannertrip');?></p>
						</div>
					</div>
				</div>
			</div>
		</section>

		<div class="container mb-14">
			<div class="w-10/12 mx-auto">
				<div role="tablist" aria-label="" class="[&_button[aria-selected='true']]:text-blue [&_button[aria-selected='true']]:bg-cranleighclay [&_button[aria-selected='true']]:opacity-100 inline-flex justify-between items-center border border-blue rounded-[33px] px-[5px] py-[5px] children:font-aspectbold children:text-[12px] children:rounded-[33px] children:uppercase children:py-[0px] children:px-3 ">
				<?php
				$years = array("2022-2023", "2023-2024", "2024-2025", "2025-2026"); 
				$count = 0;
				foreach ($years as $year) {
					
					$args = array(
						'post_type' => 'chdbtrips',
						'posts_per_page'      => -1,
						'meta_query'    => array(
							array(
								'key'       => 'evc_academic_year',
								'compare'   => '=',
								'value'     => $year,
							),
							
						)
					);
					$query = new WP_Query( $args );
					if ($query->have_posts()) : ?>
					<?php $yeartab = str_replace('-','',$year)?> 
					<button class="opacity-60 text-blue"  role="tab" <?php if($count == 1):?>aria-selected="true"<?php else:?>aria-selected="false"<?php endif;?> aria-controls="panel-403-<?= $yeartab; ?>" id="tab-<?= $yeartab; ?>" tabindex="0"><?= $year; ?></button>
					<?php endif;
					$count++;
				}
				?>
				</div>
			</div>
		</div>
		


		<div class="container">
			<div class="w-10/12 mx-auto">
				<?php
				$years = array("2022-2023", "2023-2024", "2024-2025", "2025-2026"); 
				foreach ($years as $year) {
					
					$args = array(
						'post_type' => 'chdbtrips',
						'posts_per_page'      => -1,
						'meta_query'    => array(
							array(
								'key'       => 'evc_academic_year',
								'compare'   => '=',
								'value'     => $year,
							),
							
						)
					);
					$query = new WP_Query( $args );
					if ($query->have_posts()) : ?>
						<?php
						$timings = array("Michaelmas Term & Christmas Holidays","Lent Term & Easter Holidays","Summer Term & Summer Holidays");
						
						$yeartab = str_replace('-','',$year)?>
						
						<div class="first-of-type:opacity-100 first-of-type:h-auto first-of-type:visible opacity-0 h-0 invisible overflow-hidden " id="panel-403-<?= $yeartab;?>" role="tabpanel" tabindex="0" aria-labelledby="tab-<?= $yeartab;?>">
						<?php foreach ($timings as $timing) {
							$args = array(
								'post_type' => 'chdbtrips',
								'posts_per_page'      => -1,
								'meta_key'          => 'evc_dep_date',
								'orderby'           => 'meta_value',
								'order'             => 'ASC',
								'meta_query'    => array(
									'relation'      => 'AND',
									array(
										'key'       => 'evc_timing',
										'compare'   => '=',
										'value'     => $timing,
									),
									array(
										'key'       => 'evc_academic_year',
										'compare'   => '=',
										'value'     => $year,
									),
									
								)
							);
							$query = new WP_Query( $args );
							if ($query->have_posts()) : ?>
								<h3 class="text-titleh4 uppercase mb-4 mt-14 first-of-type:mt-0"><?php echo $timing . ' ' . $year;?></h3>
								<div class="grid grid-cols-1 lg:grid-cols-5 font-aspectbold text-copy16 uppercase children:text-weyblue children:px-3 children:py-1 children:lg:px-5 children:lg:py-6 border-b border-blue/20">
									<span>Trip</span>
									<span>Year Group(s)</span>
									<span>Destination</span>
									<span>Timing</span>
									<span>Dates</span>
								</div>
								<?php 
								while ($query->have_posts()) : $query->the_post();  ?>
								<div class="get_popupeventtrip grid grid-cols-1 lg:grid-cols-5 text-copy16  children:px-3 children:lg:px-5 children:py-3 children:lg:py-6 odd:bg-black/5 duration-300 hover:bg-blue hover:text-cranleighclay cursor-pointer pointer-events-none" data-postid="<?php echo get_the_ID();?>">
									<span><?php the_title();?></span>
									<span><?php the_field('evc_year_groups');?></span>
									<span><?php the_field('evc_destination');?></span>
									<span><?php the_field('evc_timing_2');?></span>
									<span><?php the_field('evc_dep_date');?> - <?php the_field('evc_arr_date');?></span>
								</div>
								<?php endwhile; ?>
							<?php endif;
						}
						?>
						</div>


						
					<?php endif;
				}
				?>
			</div>
		</div>
		<div class="mt-32">
			<?php include locate_template('src/components/where_next/where_next.php'); ?>
		</div>
		<div id="boxcontent" class="boxpop fixed inset-0 bg-black/50 h-screen w-screen z-[60] invisible opacity-0"></div>

		<?php
	endwhile;
endif;

get_footer();
