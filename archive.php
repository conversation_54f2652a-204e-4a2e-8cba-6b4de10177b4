<?php
/**
 * The template for displaying archive pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package twkmedia
 */

get_header();
?>


<?php if ( have_posts() ) : ?>

<?php

    if ( is_category() ) {

        $title = single_cat_title('', false) . ' News &amp; Articles';

    } elseif ( is_tag() ) {

        $title = 'Posts Tagged &#8216;' . single_tag_title('', false) . '&#8217';

    } elseif ( is_author() ) {

        $heading = 'Author Archive';
        $title = get_the_author_meta('display_name');

    } elseif ( isset( $_GET['paged'] ) && ! empty( $_GET['paged'] ) ) {
         $title = 'Blog Archives';

    }
    include locate_template( 'tpl/parts/banner.php' );
					
?>


<?php $post = $posts[0]; // Hack. Set $post so that the_date() works. ?>



<div class="container main-content">

    <div class="row">
        <div class="col-md-12">
            <div class="select-contain width-300">
                <?php
						wp_dropdown_categories(
							array(
								'hide_empty'        => 0,
								'name'              => 'category_parent',
								'orderby'           => 'name',
								'selected'          => get_query_var( 'expertise' ),
								'hierarchical'      => true,
								'show_option_none'  => __( 'Areas of expertise' ),
								'option_none_value' => 'all',
								'child_of'          => 37,
								'id'                => 'news-archive-dropdown',
								'value_field'       => 'slug',
							)
						);
						?>
            </div>
        </div>
    </div>

    <div class="row">

        <?php
				while ( have_posts() ) :
					the_post();
					?>

        <div class="col-md-4 col-sm-6">
            <?php include locate_template( 'tpl/parts/news-teaser.php' ); ?>
        </div>

        <?php
				endwhile;
				?>
        <!-- end of the loop -->

        <!-- pagination here -->

        <?php wp_reset_postdata(); ?>

        <?php
				if ( get_query_var( 'max_num_pages' ) > 1 ) :
					?>
        <div class="col-sm-12">
            <div id="nav-below" class="navigation">
                <?php
							$big = 99999999;
							echo paginate_links(
								array(
									'base'      => str_replace( $big, '%#%', get_pagenum_link( $big ) ),
									'format'    => '?paged=%#%',
									'total'     => get_query_var( 'max_num_pages' ),
									'current'   => max( 1, get_query_var( 'paged' ) ),
									'show_all'  => false,
									'end_size'  => 2,
									'mid_size'  => 3,
									'prev_next' => true,
									'prev_text' => '«',
									'next_text' => '»',
									'type'      => 'list',
								)
							);
							?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>


<?php else : ?>


<h1>Sorry, no posts were found.</h1>


<?php endif; ?>


<?php
get_footer();
