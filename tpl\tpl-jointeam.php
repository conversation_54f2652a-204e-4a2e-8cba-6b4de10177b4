<?php
/**
 * Template Name: Join our team
 *
 * @package twkmedia
 */

get_header();

if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();
		?>

		<?php include locate_template('src/components/banner/banner.php'); ?>
		
		
		
		<section>
			<div class="container ">
				<div class="lg:flex justify-between items-start lg:w-10/12 mx-auto mt-10 lg:mt-[120px] ">
					<div class="lg:w-6/12 wysiwyg px-10 lg:px-0">
						<?php the_field('intro_copy_jointeam');?>
					</div>
					<div class="lg:w-6/12 grid grid-cols-6 grid-rows-3 gap-8 mt-10 lg:mt-0
					[&:nth-child(1)]:children:col-start-2 [&:nth-child(1)]:children:col-span-2 [&:nth-child(1)]:children:pl-8 
					[&:nth-child(2)]:children:col-start-4 [&:nth-child(2)]:children:col-span-3 [&:nth-child(2)]:children:row-span-2 [&:nth-child(2)]:children:pr-8 [&:nth-child(2)]:children:pt-10
					[&:nth-child(3)]:children:col-start-1 [&:nth-child(3)]:children:col-span-3 [&:nth-child(3)]:children:row-span-2 [&:nth-child(3)]:children:pl-10 [&:nth-child(3)]:children:pb-24">
						<?php if( have_rows('intro_gallery_jointeam') ): ?>
							<?php while( have_rows('intro_gallery_jointeam') ): the_row(); $image = get_sub_field('image');?>
								<?php echo twk_output_acf_image($image, 'large', 'min-h-full w-full object-cover' , 'lazy'); ?>
							<?php endwhile; ?>
						<?php endif; ?>
					</div>
				
				</div>
			</div>
		</section>

		<section>
			<div class="container">
				<div class="lg:flex justify-between items-endpx-10 lg:px-0">
					
					<div class="lg:w-5/12">
						
						<?php if( have_rows('testimonials_grid_jointeam') ): ?>
							<div class="swiper mb-10" data-element="testimonial_carousel">
								<div class="swiper-wrapper">
								<?php while( have_rows('testimonials_grid_jointeam') ): the_row();?>
									<div class="swiper-slide relative pt-20 after:content-[''] after:absolute after:top-0 after:left-px after:w-[67px] after:h-[63px] after:bg-quote-mark-red after:bg-center after:bg-contain after:bg-no-repeat">
										<h3 class="font-aspectregular text-titlemd uppercase"><?php echo get_sub_field('copy'); ?></h3>
										<span class="block font-aspectbold text-copy16 uppercase mt-5"><?php echo get_sub_field('name'); ?></span>
									</div>
								<?php endwhile; ?>
								</div>
								<div class="swiper-pagination"></div>
							</div>
						<?php endif; ?>
						
						<?php if(get_field('application_yellow_box')):?>
							<div class="wysiwyg bg-cranleighclay children:text-blue pt-1 pb-4 px-10 mb-10 lg:mb-0"> 
								<?php echo get_field('application_yellow_box'); ?>
							</div>
						<?php endif;?>
						
					</div>
					<div class="lg:w-6/12 bg-blue text-cranleighclay -mx-4 lg:mx-0">
						<div class=" py-10 px-8 lg:px-[50px]">
							<h3 class="text-title30 lg:text-title uppercase mb-4 "><?php echo get_field('applications_title_jointeam');?></h3>
							<div class="children:lg:leading-[30px] children:text-copy16 children:lg:text-copy19 "><?php echo get_field('applications_copy_jointeam');?></div>
							<div class="border-t border-white pt-10 mt-10">
								<h3 class="text-title30 lg:text-title uppercase mb-4 "><?php echo get_field('enquiry_title_jointeam');?></h3>
								<?php if(get_field('enquiry_copy_jointeam')):?>
								<div class="wysiwyg mt-2 children:!text-white [&_a]:!underline [&_a]:!underline-offset-4 [&_a]:!text-white"> 
									<?php echo get_field('enquiry_copy_jointeam');?>
								</div>
								<?php endif;?>
								<div class="xl:flex children:text-copy16 children:lg:text-copy19 children:leading-[30px]">
									<span class="relative after:content-[''] after:absolute after:w-4 after:h-4 after:top-1/2 after:-translate-y-1/2 after:left-0 after:bg-phone-icon-white after:bg-center after:bg-contain after:bg-no-repeat pl-6 mr-8"><?php echo get_field('phone_number_jointeam');?></span>
									<span class="relative after:content-[''] after:absolute after:w-4 after:h-4 after:top-1/2 after:-translate-y-1/2 after:left-0 after:bg-email-icon-white after:bg-center after:bg-contain after:bg-no-repeat pl-6"><a href="mailto:<?php echo get_field('email_jointeam');?>"><?php echo get_field('email_jointeam');?></a></span>
								</div>
							</div>
						</div>
					
					
					
					</div>
				</div>
			</div>
		</section>
		
		
		
		<section class="my-16">
			<div class="container bg-cranleighclay py-9">
				<div class="lg:flex lg:w-10/12 justify-between items-center mx-auto px-10 lg:px-0">
					<div class="lg:w-4/12">
						<h2 class="text-title30 lg:text-titleh2 uppercase mb-4 lg:mb-0"><?php echo get_field('related_policies_title_jointeam');?></h2>
					</div>
					<div class="lg:w-6/12 flex flex-wrap items-start">
						<?php if( have_rows('policies_links') ): ?>
							<?php while( have_rows('policies_links') ): the_row(); ?>
							<?php 
							$link = get_sub_field('link');
							if( $link ): 
								$link_url = $link['url'];
								$link_title = $link['title'];
								$link_target = $link['target'] ? $link['target'] : '_self';
								?>
								<a target="_blank" class="relative min-w-[50%] text-copy16 after:content-[''] after:absolute after:w-4 after:h-4 after:top-1/2 after:-translate-y-1/2 after:left-0 after:bg-pdf-icon-navy after:bg-center after:bg-contain after:bg-no-repeat pl-6 odd:pr-8 mb-6 last:mb-0" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
							<?php endif; ?>
							<?php endwhile; ?>
						<?php endif; ?>	
					</div>
				</div>
			</div>
		</section>

		

		<?php include locate_template('src/components/where_next/where_next.php'); ?>
		
		

		 

		<?php
	endwhile;
endif;

get_footer();
