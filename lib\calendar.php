<?php
/**
 * Calendar
 *
 * @package twkmedia
 */

/**
 * Calendar query string.
 *
 * @return void
 */
function twk_calendar_query_string() {
	add_rewrite_endpoint( 'month-view', EP_PERMALINK | EP_PAGES );
}
add_action( 'init', 'twk_calendar_query_string' );


/**
 * Load events.
 *
 * @return void
 */
function twk_load_events() {
	$start_date = $_GET['start_date'];
	$type       = $_GET['type'];
	$cat        = $_GET['cat'];
	$week       = filter_var( $_GET['week'], FILTER_VALIDATE_BOOLEAN );
	$calendar   = $_GET['calendar'];

	if ( $week || 'week' === $type ) {

		$start_date = date( 'Y-m-d 00:00' );
		$end_date   = date( 'Y-m-d 23:59', strtotime( '+7 day' ) );
		$type       = 'week';

	} elseif ( 'month' === $type ) {

		$start_date = date( 'Y-m-01 00:00', strtotime( $start_date ) );
		$end_date   = date( 'Y-m-t 23:59', strtotime( $start_date ) );

	} else {

		$start_date = date( 'Y-m-d 00:00', strtotime( $start_date ) );
		$end_date   = date( 'Y-m-d 23:59', strtotime( $start_date ) );

	}

	$wmonth = date( 'm', strtotime( $start_date ) );

	ob_start();

		echo '<table class="calendar-events">';
			echo '<tbody>';

				$args = array(
					'post_type'      => 'tribe_events',
					'eventDisplay'   => 'custom',
					'posts_per_page' => '-1',
					'start_date'     => $start_date,
					'end_date'       => $end_date,
					'orderby'        => 'event_date',
					'order'          => 'ASC',
					'post_status'    => 'publish',
				);

				if ( 'ob-calendar' === $calendar ) {

					$args['tribe_events_cat'] = $calendar;

				} else {

					$args['tax_query'] = array(
						array(
							'taxonomy' => 'tribe_events_cat',
							'field'    => 'slug',
							'terms'    => array( 'ob-calendar' ),
							'operator' => 'NOT IN',
						),
					);

					// Filters only available on main calendar.
					// ------------------------------------------------------------------------------------
					// NEW: IF cat is an array and has more than 1 cat.
					if ( is_array( $cat ) ) {
						if ( count( $cat ) > 1 ) {
							// Make sure it only display events that have these 2 cats.
							$args['tax_query'] = array(
								'relation' => 'AND',
								array('taxonomy' => 'tribe_events_cat', 'field'=>'slug', 'terms'=>$cat['cat1']) ,
								array('taxonomy' => 'tribe_events_cat', 'field'=>'slug', 'terms'=>$cat['cat2']) ,
								array( 'taxonomy' => 'tribe_events_cat', 'field' => 'slug', 'terms' => array('ob-calendar'), 'operator' => 'NOT IN' )
							);
						}
					} else { // ... otherwise
						if ( $cat !== 'all' ) { // ... check the cat is not all

							$args['tribe_events_cat'] = $cat; // add cat filter.

						}
					}
					// ------------------------------------------------------------------------------------
				}

				// Load Calendar content.
				include locate_template( 'tpl/parts/loop-calendar.php' );

			echo '</tbody>';
		echo '</table>';

	$output = ob_get_clean();

	$json_array = array(
		'content' => $output,
		'type'    => $type,
		'ndate'   => $start_date,
		'week'    => $week,
	);
	if ( 'all' !== $cat ) {
		$json_array['cat'] = $cat;
	}

	echo wp_json_encode( $json_array );

	die;
}
add_action( 'wp_ajax_nopriv_events_ajax', 'twk_load_events' );
add_action( 'wp_ajax_events_ajax', 'twk_load_events' );





/**
 * Load term children.
 *
 * @return void
 */
function twk_load_term_children() {
	$termid        = $_GET['termid'];
	$term_children = get_term_children( $termid, 'tribe_events_cat' );

	ob_start();

		echo '<div class="calendar-filter subcat-filter-wrapper">';
			echo '<div id="subcat-calendar-filter">';
				echo '<select class="subcat-events-filters" name="" id="">';
					echo '<option value="all" data-selected="true">All</option>';

					foreach ( $term_children as $tc ) :
						$term_arr = get_term_by( 'id', $tc, 'tribe_events_cat' );
						echo '<option value="' . esc_attr( $term_arr->slug ) . '" data-termid="' . esc_attr( $term_arr->term_id ) . '">';
							echo esc_html( $term_arr->name );
						echo '</option>';
					endforeach;

				echo '</select>';
			echo '</div>';
		echo '</div>';

	$output = ob_get_clean();

	echo wp_json_encode( $output );

	die;
}
add_action( 'wp_ajax_nopriv_termchildren_ajax', 'twk_load_term_children' );
add_action( 'wp_ajax_termchildren_ajax', 'twk_load_term_children' );



/**
 * This function sort order spread events array by date.
 *
 * @param array $a Array.
 * @param array $b Array.
 * @return array The array in order by date.
 */
function twk_spread_events_date_compare( $a, $b ) {
	$t1 = strtotime( $a['enddate'] );
	$t2 = strtotime( $b['enddate'] );
	return $t1 - $t2;
}
