<?php
/**
 * Template part to display the block "Banner".
 *
 * @package twkmedia
 */

?>


<?php if(get_field('banner_type') == "style1"): ?>
<section class="relative banner mb-10 lg:mb-14
before:content-[''] before:absolute before:z-10 before:top-0 before:left-0 before:w-full before:h-[25%] before:bg-gradient-to-t before:from-black/0 before:to-black/50
after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-3/4 lg:after:h-[25%] after:bg-gradient-to-b after:from-black/0 after:to-black/50">
    <div class="relative h-[560px] lg:h-[650px] flex items-end">
        <?php 
        $background_image_topbanner = get_field('background_image_topbanner');
        if( !empty( $background_image_topbanner ) ): ?>
            <img class="absolute w-full h-full object-cover" src="<?php echo esc_url($background_image_topbanner['url']); ?>" alt="<?php echo esc_attr($background_image_topbanner['alt']); ?>" />
        <?php else: ?>
            
            <?php 
            $pages_fallback_image = get_field('pages_fallback_image', 'options');
            if( !empty( $pages_fallback_image ) ): ?>
                <img class="absolute w-full h-full object-cover" src="<?php echo esc_url($pages_fallback_image['url']); ?>" alt="<?php echo esc_attr($pages_fallback_image['alt']); ?>" />
            <?php endif; ?>
        <?php endif;?>
        <div class="container relative z-10 pb-6 lg:pb-[50px] px-10 lg:px-0">
            <div class="lg:flex items-end justify-between">
                <div class="lg:w-[62%] lg:translate-y-[22px]">
                    <?php if(get_field('subtitle_topbanner')):?><span class="font-aspectbold text-white text-base uppercase mb-6"><?php the_field('subtitle_topbanner');?></span>
                        
                    <?php else:?>
                        <?php
                        $current = $post->ID;
                        $parent = $post->post_parent;
                        ?>
                        <?php if(get_the_title($parent)):?> <span class="font-aspectbold text-white text-base uppercase mb-6"><?php echo get_the_title($parent);?> </span><?php endif;?>
                    <?php endif;?>
                    <h1 class="font-aspectbold text-white text-title46 leading-[48px] lg:text-[78px] lg:leading-[76px] xl:text-titlelg uppercase mt-3 lg:mt-6 "><?php if(get_field('title_topbanner')):?><?php the_field('title_topbanner');?><?php else: the_title();?><?php endif;?></h1>
                </div>
                <?php 
                $link = get_field('cta_topbanner');
                if( $link ): 
                    $link_url = $link['url'];
                    $link_title = $link['title'];
                    $link_target = $link['target'] ? $link['target'] : '_self';
                    ?>
                    <a class="<?php if(is_page_template( 'tpl/tpl-jointeam.php' )):?>after:!content-none !px-[28px]<?php endif;?> button mt-4 lg:mt-0 <?php if(get_field('sidebar_content')):?>lg:-translate-y-28<?php endif;?>" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
<?php else: ?>
<section class="bg-blue pt-[170px] pb-[50px] lg:pb-20 mb-14">
    <div class="container text-center">
            <?php if(get_field('subtitle_topbanner')):?><span class="font-aspectbold text-white text-base uppercase mb-6"><?php the_field('subtitle_topbanner');?></span>
            <?php else:?>
                <?php if ($post->post_parent) {
                    $ancestors = get_post_ancestors($post->ID);
                    $root = count($ancestors) - 1;
                    $parent = $ancestors[$root]; 
                ?>
                <span class="font-aspectbold text-white text-base uppercase mb-6"><?php echo get_the_title( $parent ); ?></span>
                <?php } ?>
            <?php endif;?>
            <h1 class="font-aspectbold text-white text-title46 lg:text-titlelg uppercase mt-6"><?php if(get_field('title_topbanner')):?><?php the_field('title_topbanner');?><?php else: the_title();?><?php endif;?></h1>
        <?php if(get_field('copy_topbanner')): ?>
        <div class="wysiwyg wysiwygbanner lg:w-8/12 mx-auto children:text-white mt-8 px-10 lg:px-0">
            <?php the_field('copy_topbanner');?>
        </div>
        <?php endif; ?>
    </div>
</section>
<?php endif; ?>
