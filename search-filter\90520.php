<?php // News 
?>
<div class="container px-4 md:px-10 mx-auto my-20 relative">
    <?php if ($query->have_posts()) : ?>
        <div class="flex flex-wrap">
            <?php $i = 0;
            while ($query->have_posts()) : $query->the_post();  ?>
    
                <div class="md:px-5 w-full md:w-1/2 xl:w-1/2 mb-10 border-b border-black/20 pb-8 md:border-none last:border-none last:pb-0">
                    <a href="<?php echo get_permalink($post->ID); ?>" class=" ">
                        <div class="aspect-w-3 aspect-h-2 relative flex items-end  mb-4 children:rounded-[10px] rounded-[10px] custom-shadow-2">
                            <?php echo twk_output_featured_image_with_fallback($post->ID, 'large', '', 'news') ?>  
                        </div>
                        <div class="font-bodybold text-green-light text-[15px] leading-[25px]">
                            
                            <?php if(get_field('date_to_display_in_list')): 
                                echo the_field('date_to_display_in_list'); 
                            else :
                                echo the_field('date_event'); 
                            endif;
                            ?>                            
                            <?php
                           
                            $cat = get_the_terms($post->ID, 'event_type');
                            if ($cat) : ?> • <?php echo $cat[0]->name; ?><?php endif; ?></div>
                        <h2 class="font-display text-4xl "><?php the_title();?></h2>
                    </a>
                </div>


                

            <?php $i++;
            endwhile; ?>

            <?php
            $total_pages = $query->max_num_pages;
            $paged = isset($_GET['sf_paged']) ? $_GET['sf_paged'] : 1;

            ?>
            <div class="w-full mt-20 mb-20">
                <div class="flex justify-center text-black">

                <?php require locate_template( 'tpl/parts/_pagination.php' ); ?>
            </div>
        </div>

    <?php else : ?>
        <div class="text-center">
            <h2 class=" text-4xl font-display">Sorry, no results found</h2>
        </div>
    <?php endif; ?>
</div>