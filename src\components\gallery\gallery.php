<?php
/**
 * Template part to display the block "Gallery".
 *
 * @package twkmedia
 */

?>



<div class="mb-10 lg:mb-12 popup-gallery">
    <?php if( have_rows('gallery_images') ): ?>
        <div class="relative flex flex-wrap justify-between">
            <?php $count=0; while( have_rows('gallery_images') ): the_row();         ?>
                
                <?php 
                    if($count<3):
                    $imagegallery = get_sub_field('image');
                    if( !empty( $imagegallery ) ): ?>
                    <div class="relative aspect-[3/2] first:w-full first:mb-[18px] lg:first:mb-6 w-[calc(100%_/_2_-_9px)] lg:w-[calc(100%_/_3_-_12px)]">
                        <a class="first-of-type:absolute first-of-type:top-0 first-of-type:left-0 first-of-type:w-full first-of-type:h-full" href="<?php echo esc_url($imagegallery['url']); ?>"></a>
                        <?php echo twk_output_acf_image($imagegallery, 'large', 'w-full h-full object-cover object-center' , 'lazy'); ?>
                    </div>
                    <?php endif;  endif; ?>
            <?php $count++; endwhile; ?>
            <div class="relative cursor-pointer w-full lg:w-[calc(100%_/_3_-_12px)] flex items-center justify-center lg:aspect-[3/2] lg:bg-cranleighclay text-copy16 lg:text-titlemd z-10 mt-4 lg:mt-0">
                
                    <?php $count=0; while( have_rows('gallery_images') ): the_row();         ?>
                    <?php 
                    if($count>2):?>
                    <?php   
                        $imagegallery = get_sub_field('image');
                        if( !empty( $imagegallery ) ): ?>
                            <a class="first-of-type:absolute first-of-type:top-0 first-of-type:left-0 first-of-type:w-full first-of-type:h-full z-10" href="<?php echo esc_url($imagegallery['url']); ?>"></a>
                        <?php  endif; ?>
                    <?php  endif; ?>
                    <?php $count++; endwhile; ?>
            <p class="text-copy16 lg:text-[22px] relative inline-table text-black bg-cranleighclay py-[21px] px-[35px] pr-[75px] lg:p-0 rounded-[33px] after:content-[''] after:absolute after:top-1/2 after:right-10 after:-translate-y-1/2 after:w-4 after:h-2.5 after:bg-arrow-right-gold after:bg-contain after:bg-center after:bg-no-repeat after:duration-300  after:ease-in hover:after:translate-x-5 lg:after:content-none">View all</p>
                            
            </div>
        </div>
    <?php endif; ?>
</div>