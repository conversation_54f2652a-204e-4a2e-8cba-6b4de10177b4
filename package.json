{"name": "twk-boilerplate-template", "version": "1.0.0", "description": "Template of the files that are created with create-twk-boilerplate", "main": "script.js", "directories": {"lib": "lib"}, "scripts": {"build:dev": "webpack --mode development", "build:prod": "webpack --mode production", "deploy": "node deploy", "tailwind": "npx tailwindcss -i ./src/styles/tailwind.css -o ./assets/css/style.css --watch", "dev": "npm-run-all --parallel deploy build:dev tailwind", "watch": "npm-run-all --parallel build:dev tailwind", "prod": "npm-run-all --parallel deploy build:prod tailwind"}, "author": "<EMAIL>", "license": "MIT", "devDependencies": {"@babel/core": "^7.13.10", "@babel/preset-env": "^7.13.10", "@fullhuman/postcss-purgecss": "^3.0.0", "@tailwindcss/typography": "^0.5.4", "autoprefixer": "^10.4.2", "babel-loader": "^8.2.2", "basic-ftp": "^4.6.3", "clean-css": "^4.2.3", "css-loader": "^6.5.1", "file-loader": "^6.2.0", "mini-css-extract-plugin": "^0.8.2", "node-notifier": "^8.0.2", "node-watch": "^0.6.4", "npm-run-all": "^4.1.5", "postcss": "^8.2.8", "postcss-aspect-ratio-property": "^1.0.0", "postcss-loader": "^4.0.3", "purgecss-with-wordpress": "^4.0.2", "sass": "^1.45.1", "sass-loader": "^7.3.1", "ssh2-sftp-client": "^7.2.1", "style-loader": "^0.23.1", "tailwindcss": "^3.1.8", "tailwindcss-debug-screens": "^2.2.1", "url-loader": "^4.1.1", "webpack": "^5.67.0", "webpack-build-notifier": "^2.3.0", "webpack-cli": "^4.9.2", "webpack-fix-style-only-entries": "^0.6.1"}, "browserslist": ["last 5 versions"], "babel": {"presets": [["@babel/preset-env", {"modules": false}]]}, "dependencies": {"@studio-freight/lenis": "^0.2.28", "gsap": "^3.13.0", "isotope-layout": "^3.0.6", "magnific-popup": "^1.1.0", "swiper": "^8.4.5"}}