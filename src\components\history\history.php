<?php
/**
 * Template part to display the history tpl.
 *
 * @package twkmedia
 */

$banner_image_historytpl = get_field('banner_image_historytpl');
$banner_title_historytpl = get_field('banner_title_historytpl');
$banner_subtitle_historytpl = get_field('banner_subtitle_historytpl');
$intro_copy_historytpl = get_field('intro_copy_historytpl');
$blue_strip_historytpl = get_field('blue_strip_historytpl');

?>

<div class="relative 
before:content-[''] before:absolute before:top-0 before:left-0 before:w-full before:h-[20vh] before:bg-gradient-to-t before:from-black/0 before:to-black/50
after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-[50vh] after:bg-gradient-to-b after:from-black/0 after:to-black/50
">
    <?php 
    if( !empty( $banner_image_historytpl ) ): ?>
        <img class="w-full h-[560px] lg:h-screen object-cover object-center" src="<?php echo esc_url($banner_image_historytpl['url']); ?>" alt="<?php echo esc_attr($banner_image_historytpl['alt']); ?>" />
    <?php else: ?>
        <?php 
        $pages_fallback_image = get_field('pages_fallback_image', 'options');
        if( !empty( $pages_fallback_image ) ): ?>
            <img class="w-full h-[560px] lg:h-screen object-cover object-center" src="<?php echo esc_url($pages_fallback_image['url']); ?>" alt="<?php echo esc_attr($pages_fallback_image['alt']); ?>" />
        <?php endif; ?>
    <?php endif;?>


    <div class="absolute w-full text-center bottom-16 md:bottom-9 lg:bottom-16 left-0 z-10">
        <span class=" text-base uppercase text-white"><?= $banner_subtitle_historytpl; ?></span>
        <h1 class=" text-title46 lg:text-titlelg uppercase text-white mt-6"><?= $banner_title_historytpl; ?></h1>
    </div>
</div>

<div class="container">
    <div class="wysiwyg lg:w-7/12 lg:text-center mx-auto mt-10 mb-10 lg:mt-[66px] lg:mb-20 px-10 lg:px-0">
        <?= $intro_copy_historytpl; ?>
    </div>
</div>
<div class="bg-blue pt-8 pb-8">
    <div class="container">
        <div class="wysiwyg wysiwygbanner w-full lg:w-8/12 text-white lg:text-center px-10 lg:px-0 children:text-copy16 children:leading-[28px] mx-auto ">
            <?= $blue_strip_historytpl; ?>
        </div>
    </div>
</div>

<div class="container mt-10 lg:mt-20 overflow-hidden">
    <div class="relative w-10/12 lg:w-9/12 xl:w-10/12 mx-auto pb-40 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-40 after:bg-gradient-to-b after:from-white/0 after:to-white">
    <svg id="svg_line_history" version="1.1" class="absolute top-0 left-0 w-5 h-full" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" xml:space="preserve">
        <path class="path-vert" fill="#0C223F" stroke-width="5" stroke="#0C223F" d="M0 0 v6000 0"/>
    </svg>
    <?php if( have_rows('timeline_grid') ): ?>
        <?php while( have_rows('timeline_grid') ): the_row(); ?>
            <?php if( have_rows('century') ): ?>
                <?php while( have_rows('century') ): the_row(); ?>
                    <h2 data-element="century" class=" text-weyblue text-titlelg lg:text-titlexl text-center mt-12 lg:mt-28 first:mt-0"><?php the_sub_field('year_century');?></h2>
                    <div class="relative z-10  -mt-24 lg:-mt-32">
                        <?php if( have_rows('timeline_event') ): ?>
                            <?php while( have_rows('timeline_event') ): the_row(); ?>
                                <div class="relative z-20 mt-16">
                                    <?php $style = get_sub_field('style');?>
                                    <span data-element="year" class="absolute w-20 left-[58px] md:left-[95px] lg:-left-[100px] top-8  text-titlemd
                                    <?php if($style == "dark"): echo 'text-white lg:text-weyblue'; else: echo 'text-weyblue'; endif;?>
                                      after:content-[''] after:absolute after:top-0 after:-left-[72px] after:md:-left-[108px] lg:after:left-auto lg:after:-right-[37px] after:w-[30px] after:h-[30px] after:bg-white after:border-2 after:border-blue after:rounded-full z-10"><?php the_sub_field('year');?></span>
                                    <div class="relative lg:flex justify-between items-start p-8 pt-20 lg:pt-10 lg:p-10 ml-[10%]
                                    after:content-[''] after:absolute after:top-8 after:-left-[17px] after:w-0 after:h-0 after:border-t-[15px] after:border-b-[15px] after:border-r-[17px] after:border-t-[transparent]  after:border-b-[transparent] 
                                    <?php if($style == "dark"): echo 'bg-blue after:border-r-blue'; else: echo 'bg-[#cfd3d9] after:border-r-[#cfd3d9]'; endif;?> ">
                                        
                                        <?php 
                                        $image = get_sub_field('image');
                                        if( !empty( $image ) ): ?>
                                            <?php echo twk_output_acf_image($image, 'large', 'hidden md:block w-[30%] mr-10 children:w-full' , 'lazy'); ?>

                                            
                                        <?php endif; ?>
                                        <div class="w-full mt-4 lg:mt-0">
                                            <h2 class=" text-titlemd lg:text-titlesidebar uppercase mb-4 <?php if($style == "dark"): echo 'text-white'; else: echo 'text-blue'; endif;?>"><?php echo get_sub_field('title');?></h2>
                                            <p class="text-copy16 <?php if($style == "dark"): echo 'text-white'; else: echo 'text-blue'; endif;?>"><?php echo get_sub_field('copy');?></p>
                                        </div>
                                        
                                    </div>

                                </div>
                            <?php endwhile; ?>
                        <?php endif; ?>
                    </div>
                <?php endwhile; ?>
            <?php endif; ?>
        <?php endwhile; ?>
    <?php endif; ?>
    </div>
</div>