<?php
/**
 * Template Name: Socs Feed - XML
 *
 * @package twkmedia
 */

?>

<?php
/**************************\
# FIXTURES & RESULTS PAGE
\**************************/

// $fixtures_url = "https://www.schoolssports.com/school/xml/fixturecalendar.ashx?ID=55&key=A82A3764-C6A6-4C21-B5E2-26D9E29E9F3B";
$fixtures_url = get_field( 'socs_feed' );
$fixtures     = simplexml_load_file( $fixtures_url );
$sports       = array();
$teams        = array();


/**
 * Checks if a word is in the array
 *
 * @param String $word A word.
 * @param array  $array The array to search the word.
 * @return boolean
 */
function is_in_array( $word, $array ) {
	$retval      = false;
	$count_array = count( $array );

	for ( $i = 0; $i < $count_array; $i++ ) {
		if ( $array[ $i ] === $word ) {

			$retval = true;
			break;

		}
	}

	return $retval;
}

$result_page = false;
foreach ( $fixtures as $fixture ) {
	if ( $fixture->result && ! $result_page ) {
		$result_page = true;
	}

	$sport_label = (string) $fixture->sport;
	$team_label  = (string) $fixture->team;

	if ( ! is_in_array( $sport_label, $sports ) ) {
		$sports[] = $sport_label;
	}
	if ( ! is_in_array( $team_label, $teams ) ) {
		$teams[] = $team_label;
	}
}

sort( $sports );
sort( $teams );

get_header();

require locate_template( 'tpl/parts/banner.php' );


if ( have_posts() ) {
	the_post();
}

?>

<div class="container big-padding ">
	<div class="row">

		<div class="col-md-8 article">
			<?php
			if ( is_user_logged_in() ) :
				edit_post_link( 'EDIT THIS PAGE' );
			endif;
			?>

			<h1 class="title title--section navy">
				<?php the_title(); ?>
			</h1>

			<span>Filter fixtures by:</span>
			<select class="selectSport tablesorter-filter" data-column="0">
				<option class="reset" value="">Sport</option>
				<?php foreach ( $sports as $sport ) : ?>
					<option><?php echo $sport; ?></option>
				<?php endforeach; ?>
			</select>

			<select class="selectTeam tablesorter-filter" data-column="3">
				<option class="reset" value="">Team</option>
				<?php foreach ( $teams as $team ) : ?>
					<option><?php echo $team; ?></option>
				<?php endforeach; ?>
			</select>

			<div class="table-responsive">
				<table class="fixtable spTablesorter">
					<thead>
						<tr>
							<?php if ( $result_page ) : ?>
								<th>Sport</th>
								<th>Date</th>
								<th>Team</th>
								<th>Opposition</th>
								<th>Home/Away</th>
								<th>Result</th>
							<?php else : ?>
								<th>Sport</th>
								<th>Date</th>
								<th>Time</th>
								<th>Team</th>
								<th>Opposition</th>
								<th></th>
							<?php endif; ?>
						</tr>
					</thead>
					<tbody>
						<?php foreach ( $fixtures as $fixture ) : ?>
							<tr>
								<?php if( $result_page ) : ?>

									<td><?php echo $fixture->sport; ?></td>
									<td><?php echo $fixture->date; ?></td>
									<td><?php echo $fixture->team; ?></td>
									<td><?php echo $fixture->opposition; ?></td>
									<td><?php echo $fixture->location; ?></td>
									<td> 
										<?php if ( 'See Report' === $fixture->result ) : ?>

											<?php $parts = wp_parse_url( $fixture->url ); ?>
											<?php parse_str( $parts['query'], $query ); ?>
											<?php $bsport_url = 'http://www.eltham-college-sports.org.uk/'; ?>
											<?php $durl = $bsport_url . 'pu_MatchReport.asp?ID=' . $query['Id'] . '&FID=' . $fixture->eventid . '&SRC=R'; ?>
											<a href="<?php echo esc_url( $durl ); ?>" class="getResults" target="_blank">See Report</a> 

										<?php else : ?>

											<?php echo $fixture->result; ?> 

										<?php endif; ?>
									</td>
								<?php else : ?>

									<td><?php echo $fixture->sport; ?></td>
									<td><?php echo $fixture->date; ?></td>
									<td><?php echo $fixture->time; ?></td>
									<td><?php echo $fixture->team; ?></td>
									<td><?php echo $fixture->opposition; ?></td>
									<td> <a href="<?php echo $fixture->url; ?>">Details</a> </td>

								<?php endif; ?>
							</tr>
						<?php endforeach; ?>
					</tbody>
				</table>
			</div>
		</div>

		<div class="col-md-4">
			<?php get_template_part( 'tpl/parts/sidebar' ); ?>
		</div>

	</div>
</div>

<?php get_footer(); ?>


<!-- <link rel="stylesheet" href="<?php echo get_template_directory_uri(); ?>/assets/css/blue.min.css" />-->
<script type="text/javascript"> var themeDirUrl = <?php echo wp_json_encode( get_template_directory_uri() ); ?>; </script>
<script type="text/javascript" src="<?php echo get_template_directory_uri(); ?>/assets/js/vendor/jquery.tablesorter.combined.min.js"></script>


<style type="text/css">
	.fixtable{
		table-layout: fixed;
		margin-top: 30px;
		width: 100%;
	}
	.fixtable thead{
		background-color: #0f2856;
		color: #ffcc00;
	}
	.entry-style .fixtable thead th{
		padding: 10px;
		cursor: pointer;
	} 
	.entry-style .fixtable thead th.tablesorter-headerAsc{ 
		background-image: url('<?php echo get_template_directory_uri(); ?>/assets/img/caret-arrow-up.svg'); 
		background-repeat: no-repeat; 
		background-position: 8px 50%;
		background-color: #C1940A;
		color: white;
		padding-left: 25px;
		background-size: 10px;
	} 
	.entry-style .fixtable thead th.tablesorter-headerDesc{ 
		background-image: url('<?php echo get_template_directory_uri(); ?>/assets/img/caret-down.svg'); 
		background-repeat: no-repeat; 
		background-position: 8px 50%;
		background-color: #C1940A;
		color: white;
		padding-left: 25px;
		background-size: 10px;
	}
	/* .entry-style .fixtable tbody tr{ border-bottom: 1px solid #e9e9e9; } */
	.entry-style .fixtable tbody tr:nth-child(even){ background-color: #f5f5f5; }
	.entry-style .fixtable tbody tr:nth-child(odd){ background-color: #e6e6e6; }
	.entry-style table td{ padding: 10px; }

	.entry-style .fixtable tbody td{ border: none; }
	.fixtable-filters{
		background-color: #eaeaea;
		padding: 30px;
		color: #757575;
	}
	.fixtable-filters select{ 
		-webkit-appearance: none; 
		-moz-appearance: none; 
		appearance: none; 
		border: 1px solid #dedede; 
		margin-left: 10px; 
		padding: 5px 10px 2px 10px; 
		font-size: 18px; 
		padding-right: 40px; 
		background-image: url('<?php echo get_template_directory_uri(); ?>/assets/img/select-arrow.png'); 
		background-repeat: no-repeat; 
		background-position: 95% 50%; 
		background-color: white; 
		line-height: 100%; 
	}
</style>

<script type="text/javascript">
	jQuery(function($){
		tablesorter();
		function tablesorter()
		{
			var $table = $(".spTablesorter").tablesorter({
				// theme: 'blue',
				widgets: ["zebra", "filter"],
				widgetOptions : {
					filter_columnFilters: false,
					filter_reset: '.reset'
				}
			});


			var err = false;
			$('.spTablesorter,.resTablesorter').on('filterEnd filterReset', function() {
				var c = this.config,
				fr = c.filteredRows;
				if (fr === 0 && !err) 
				{
					err = true;
					c.$table.append([
						'<tr class="noData remove-me" role="alert" aria-live="assertive">',
						'<td colspan="' + c.columns + '">No Data Found</td>',
						'</tr>'
					].join(''));
				} 
				else if(fr > 0)
				{
					c.$table.find('.noData').remove();
					err = false;
				}
			});


			var $resTable = $(".resTablesorter").tablesorter({
				// theme: 'blue',
				widgets: ["zebra", "filter"],
				widgetOptions : {
					filter_columnFilters: false,
					filter_reset: '.resReset'
				}
			});

			// $.tablesorter.filter.bindSearch( $table, $('.search') );

			// fixtures
			$(".selectSport").bind('change', function (e) {
				console.info('change filter');
				var cols=[];
				cols[0] = $(this).val();
				<?php if ( $result_page ) : ?>
				cols[2] = $('.selectTeam').val();
				<?php else : ?>
				cols[3] = $('.selectTeam').val();
				<?php endif; ?>
				$('.spTablesorter').trigger('search', [cols]);
			});

			$(".selectTeam").bind('change', function (e) {
				var cols=[];
				<?php if ( $result_page ) : ?>
				cols[2] = $(this).val();
				<?php else : ?>
				cols[3] = $(this).val();
				<?php endif; ?>
				cols[0] = $('.selectSport').val();
				cols[0] = $('.selectSport').val();
				$('.spTablesorter').trigger('search', [cols]);
			});

		}
	})
</script>
